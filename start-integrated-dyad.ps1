#!/usr/bin/env pwsh

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    DYAD INTEGRATED STARTUP SCRIPT" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "This script will start:" -ForegroundColor Yellow
Write-Host "1. Dyad Web Server (Backend API)" -ForegroundColor Yellow
Write-Host "2. AI Dev Ecosystem (Frontend UI)" -ForegroundColor Yellow
Write-Host ""

# Check if we're in the right directory
if (-not (Test-Path "package.json")) {
    Write-Host "Error: package.json not found. Please run this script from the dyad root directory." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

if (-not (Test-Path "ai-dev-ecosystem\package.json")) {
    Write-Host "Error: ai-dev-ecosystem directory not found. Please ensure the frontend is in the ai-dev-ecosystem folder." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Checking dependencies..." -ForegroundColor Yellow
Write-Host ""

# Check if node_modules exists in root
if (-not (Test-Path "node_modules")) {
    Write-Host "Installing backend dependencies..." -ForegroundColor Yellow
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to install backend dependencies" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

# Check if node_modules exists in frontend
if (-not (Test-Path "ai-dev-ecosystem\node_modules")) {
    Write-Host "Installing frontend dependencies..." -ForegroundColor Yellow
    Set-Location ai-dev-ecosystem
    npm install --legacy-peer-deps
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to install frontend dependencies" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
    Set-Location ..
}

Write-Host "All dependencies installed!" -ForegroundColor Green
Write-Host ""

Write-Host "Starting Dyad Web Server (Backend)..." -ForegroundColor Yellow
Write-Host "Backend will run on http://localhost:3002" -ForegroundColor Yellow
Write-Host ""

# Start backend in new window
Start-Process powershell -ArgumentList "-NoExit", "-Command", "Write-Host 'Starting Dyad Web Server...' -ForegroundColor Cyan; npm run web-server"

Write-Host "Waiting 5 seconds for backend to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 5

Write-Host ""
Write-Host "Starting AI Dev Ecosystem (Frontend)..." -ForegroundColor Yellow
Write-Host "Frontend will run on http://localhost:3000" -ForegroundColor Yellow
Write-Host ""

# Start frontend in new window
Start-Process powershell -ArgumentList "-NoExit", "-Command", "Write-Host 'Starting AI Dev Ecosystem Frontend...' -ForegroundColor Cyan; Set-Location ai-dev-ecosystem; npm run dev"

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "    DYAD INTEGRATION STARTED!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Services starting:" -ForegroundColor Yellow
Write-Host "- Backend API: http://localhost:3002" -ForegroundColor Cyan
Write-Host "- Frontend UI: http://localhost:3000" -ForegroundColor Cyan
Write-Host ""
Write-Host "Wait a few moments for both services to start, then:" -ForegroundColor Yellow
Write-Host "1. Open http://localhost:3000 in your browser" -ForegroundColor White
Write-Host "2. Test the integrated Dyad functionality" -ForegroundColor White
Write-Host ""
Write-Host "Features to test:" -ForegroundColor Yellow
Write-Host "- Create and manage apps" -ForegroundColor White
Write-Host "- Real-time app status updates" -ForegroundColor White
Write-Host "- File browser and code editor" -ForegroundColor White
Write-Host "- AI chat with streaming responses" -ForegroundColor White
Write-Host "- OpenRouter model selection" -ForegroundColor White
Write-Host ""
Read-Host "Press Enter to exit this window"
