# Dyad Web Server

This directory contains the HTTP API server that exposes Dyad's functionality for web frontends. It provides the same capabilities as the Electron IPC handlers but through REST API endpoints.

## Overview

The web server converts Dyad's IPC-based architecture to HTTP REST APIs, allowing external web applications to interact with Dyad's core functionality including:

- App management (create, read, update, delete apps)
- Chat functionality (create chats, send messages, stream responses)
- Settings management
- Language model configuration
- Node.js environment status
- File operations

## Architecture

```
┌─────────────────┐    HTTP/REST    ┌─────────────────┐
│   Web Frontend  │ ◄──────────────► │   Web Server    │
│ (ai-dev-ecosystem)│                │  (Express.js)   │
└─────────────────┘                 └─────────────────┘
                                             │
                                             ▼
                                    ┌─────────────────┐
                                    │  Dyad Backend   │
                                    │ (Database, IPC  │
                                    │   Handlers)     │
                                    └─────────────────┘
```

## Quick Start

1. **Install dependencies** (if not already done):
   ```bash
   npm install
   ```

2. **Start the web server**:
   ```bash
   npm run web-server
   ```

3. **For development with auto-reload**:
   ```bash
   npm run web-server:dev
   ```

4. **Access the API**:
   - Base URL: `http://localhost:3002`
   - Health check: `http://localhost:3002/health`
   - API endpoints: `http://localhost:3002/api/*`

## Environment Variables

Create a `.env` file in the project root with:

```env
# Web server configuration
WEB_SERVER_PORT=3002
WEB_FRONTEND_URL=http://localhost:3001

# API Keys (same as Electron app)
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
OPENROUTER_API_KEY=your_openrouter_key
# ... other API keys
```

## API Endpoints

### Apps
- `GET /api/apps` - List all apps
- `GET /api/apps/:id` - Get specific app
- `POST /api/apps` - Create new app
- `PUT /api/apps/:id` - Update app
- `DELETE /api/apps/:id` - Delete app
- `POST /api/apps/:id/start` - Start app
- `POST /api/apps/:id/stop` - Stop app
- `GET /api/apps/:id/files/*` - Read app file
- `PUT /api/apps/:id/files/*` - Write app file

### Chats
- `GET /api/chats` - List chats (optionally filtered by appId)
- `GET /api/chats/:id` - Get chat with messages
- `POST /api/chats` - Create new chat
- `POST /api/chats/:id/messages` - Add message to chat
- `POST /api/chats/:id/stream` - Stream chat response (SSE)
- `PUT /api/chats/:id` - Update chat
- `DELETE /api/chats/:id` - Delete chat

### Settings
- `GET /api/settings` - Get user settings
- `PUT /api/settings` - Update user settings
- `GET /api/settings/env-vars` - Get environment variables (masked)
- `GET /api/settings/provider/:provider` - Get provider settings
- `PUT /api/settings/provider/:provider` - Update provider settings

### Language Models
- `GET /api/language-models/providers` - Get all providers
- `GET /api/language-models/by-providers` - Get models grouped by provider
- `GET /api/language-models/all` - Get all available models
- `GET /api/language-models/provider/:provider` - Get models for provider

### Node.js
- `GET /api/node/status` - Get Node.js and package manager status
- `GET /api/node/version` - Get Node.js version
- `GET /api/node/pnpm-version` - Get pnpm version
- `POST /api/node/install-pnpm` - Install pnpm

### Health
- `GET /health` - Health check endpoint

## Usage with Web Frontend

### 1. Install the API Client

Copy `src/web-client/api-client.ts` to your web frontend project.

### 2. Initialize the Client

```typescript
import { DyadApiClient } from './api-client';

const dyad = new DyadApiClient('http://localhost:3002');
```

### 3. Use the API

```typescript
// List apps
const apps = await dyad.listApps();

// Create a new app
const newApp = await dyad.createApp({ name: 'my-app' });

// Get chats for an app
const chats = await dyad.listChats(newApp.id);

// Stream a chat response
await dyad.streamChat(chatId, {
  message: 'Hello!',
  model: { name: 'gpt-4', provider: 'openai' },
  appId: newApp.id
}, (chunk) => {
  console.log('Received:', chunk.content);
  if (chunk.done) {
    console.log('Stream complete');
  }
});
```

## Development

### File Structure

```
src/web-server/
├── index.ts              # Main server setup
├── routes/
│   ├── apps.ts          # App management endpoints
│   ├── chats.ts         # Chat functionality endpoints
│   ├── settings.ts      # Settings management endpoints
│   ├── language-models.ts # Language model endpoints
│   ├── node.ts          # Node.js status endpoints
│   └── ...              # Other route modules
└── README.md            # This file
```

### Adding New Endpoints

1. Create or update a route file in `routes/`
2. Export a setup function that returns an Express router
3. Import and register the route in `index.ts`

### Error Handling

All endpoints return JSON responses with consistent error format:

```json
{
  "error": "Error message",
  "details": "Additional details (in development)"
}
```

### CORS Configuration

The server is configured to accept requests from the web frontend URL specified in `WEB_FRONTEND_URL` environment variable.

## Security Considerations

- API keys are masked when returned through the API
- CORS is configured to only allow requests from specified origins
- File operations are restricted to the app directories
- Input validation is performed on all endpoints

## Limitations

Some features from the Electron app are not yet implemented in the web server:

- GitHub integration
- Supabase integration  
- Version control operations
- Custom model/provider management
- Proposal generation
- Advanced file operations

These will be added in future updates as needed.
