"use strict";
/**
 * AG3NT Framework - Task Planner Agent
 *
 * Specialized agent for breaking down high-level plans into executable tasks.
 * Works in coordination with PlanningAgent to create detailed task breakdowns.
 *
 * Features:
 * - Task decomposition and dependency analysis
 * - Resource estimation and timeline planning
 * - Task prioritization and scheduling
 * - Integration with workflow coordination
 * - MCP-enhanced task analysis
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = exports.TaskPlannerAgent = void 0;
const base_agent_1 = require("../core/base-agent");
const ai_service_1 = require("../../ai-service");
/**
 * Task Planner Agent - Sophisticated task breakdown and project planning
 */
class TaskPlannerAgent extends base_agent_1.BaseAgent {
    constructor(config = {}) {
        super('task-planner', {
            capabilities: {
                requiredCapabilities: [
                    'task_decomposition',
                    'dependency_analysis',
                    'effort_estimation',
                    'resource_planning',
                    'risk_assessment',
                    'timeline_optimization'
                ],
                contextFilters: ['planning', 'tasks', 'resources', 'timeline'],
                mcpEnhanced: true,
                sequentialThinking: true,
                contextEnrichment: true,
                ragIntegration: true
            },
            ...config
        });
        this.planningSteps = [
            'analyze_plan', 'decompose_tasks', 'analyze_dependencies',
            'estimate_effort', 'create_timeline', 'allocate_resources',
            'identify_risks', 'define_milestones', 'optimize_schedule'
        ];
    }
    /**
     * Execute task planning workflow
     */
    async executeWorkflow(state) {
        const input = state.input;
        console.log(`📋 Starting task planning for project: ${input.projectPlan?.summary?.title || 'Unknown Project'}`);
        // Execute planning steps sequentially
        for (const stepId of this.planningSteps) {
            console.log(`🔄 Executing step: ${stepId}`);
            const stepResult = await this.executeStepWithContext(stepId, input);
            if (stepResult.needsInput) {
                state.needsInput = true;
                state.question = stepResult.question;
                break;
            }
            // Update progress
            state.metadata.completedSteps++;
            state.metadata.lastUpdated = new Date().toISOString();
            this.emit('step_completed', {
                agentId: this.agentId,
                stepId,
                progress: state.metadata.completedSteps / state.metadata.totalSteps
            });
        }
        // Mark as completed if no input needed
        if (!state.needsInput) {
            state.completed = true;
            console.log(`✅ Task planning completed with ${state.results.tasks?.length || 0} tasks`);
        }
        return state;
    }
    /**
     * Execute individual planning step with context enhancement
     */
    async executeStepWithContext(stepId, input) {
        // Get enhanced context from context engine
        const enhancedState = await this.getEnhancedContext(stepId);
        // Execute step with MCP enhancement
        switch (stepId) {
            case 'analyze_plan':
                return await this.analyzePlanWithMCP(enhancedState, input);
            case 'decompose_tasks':
                return await this.decomposeTasksWithMCP(enhancedState);
            case 'analyze_dependencies':
                return await this.analyzeDependenciesWithMCP(enhancedState);
            case 'estimate_effort':
                return await this.estimateEffortWithMCP(enhancedState);
            case 'create_timeline':
                return await this.createTimelineWithMCP(enhancedState);
            case 'allocate_resources':
                return await this.allocateResourcesWithMCP(enhancedState);
            case 'identify_risks':
                return await this.identifyRisksWithMCP(enhancedState);
            case 'define_milestones':
                return await this.defineMilestonesWithMCP(enhancedState);
            case 'optimize_schedule':
                return await this.optimizeScheduleWithMCP(enhancedState);
            default:
                throw new Error(`Unknown task planning step: ${stepId}`);
        }
    }
    /**
     * Get total steps for progress tracking
     */
    getTotalSteps() {
        return this.planningSteps.length;
    }
    /**
     * Get relevant documentation for task planning
     */
    async getRelevantDocumentation() {
        return {
            taskPlanning: 'Task decomposition and project planning methodologies',
            agileMethodology: 'Agile development practices and sprint planning',
            resourcePlanning: 'Resource allocation and capacity planning',
            riskManagement: 'Project risk identification and mitigation strategies',
            timelineOptimization: 'Critical path method and schedule optimization'
        };
    }
    // ============================================================================
    // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
    // ============================================================================
    async analyzePlanWithMCP(state, input) {
        const analysis = await ai_service_1.aiService.analyzeProjectPlan(input.projectPlan, input.requirements, input.constraints);
        this.state.results.planAnalysis = analysis;
        return {
            results: analysis,
            needsInput: false,
            completed: false
        };
    }
    async decomposeTasksWithMCP(state) {
        const planAnalysis = this.state.results.planAnalysis;
        const tasks = await ai_service_1.aiService.decomposeIntoTasks(planAnalysis, this.state.input.preferences?.methodology || 'agile');
        this.state.results.tasks = tasks;
        return {
            results: tasks,
            needsInput: false,
            completed: false
        };
    }
    async analyzeDependenciesWithMCP(state) {
        const tasks = this.state.results.tasks;
        const dependencies = await ai_service_1.aiService.analyzeDependencies(tasks);
        this.state.results.dependencies = dependencies;
        return {
            results: dependencies,
            needsInput: false,
            completed: false
        };
    }
    async estimateEffortWithMCP(state) {
        const tasks = this.state.results.tasks;
        const estimates = await ai_service_1.aiService.estimateEffort(tasks, this.state.input.preferences?.teamSize || 3);
        // Update tasks with effort estimates
        this.state.results.tasks = tasks.map((task, index) => ({
            ...task,
            estimatedHours: estimates[index]?.hours || 8,
            complexity: estimates[index]?.complexity || 'medium'
        }));
        return {
            results: estimates,
            needsInput: false,
            completed: false
        };
    }
    async createTimelineWithMCP(state) {
        const tasks = this.state.results.tasks;
        const dependencies = this.state.results.dependencies;
        const timeline = await ai_service_1.aiService.createProjectTimeline(tasks, dependencies, this.state.input.preferences?.sprintDuration || 2);
        this.state.results.timeline = timeline;
        return {
            results: timeline,
            needsInput: false,
            completed: false
        };
    }
    async allocateResourcesWithMCP(state) {
        const tasks = this.state.results.tasks;
        const timeline = this.state.results.timeline;
        const allocation = await ai_service_1.aiService.allocateResources(tasks, timeline);
        this.state.results.resources = allocation;
        return {
            results: allocation,
            needsInput: false,
            completed: false
        };
    }
    async identifyRisksWithMCP(state) {
        const planAnalysis = this.state.results.planAnalysis;
        const tasks = this.state.results.tasks;
        const risks = await ai_service_1.aiService.identifyProjectRisks(planAnalysis, tasks);
        this.state.results.risks = risks;
        return {
            results: risks,
            needsInput: false,
            completed: false
        };
    }
    async defineMilestonesWithMCP(state) {
        const timeline = this.state.results.timeline;
        const tasks = this.state.results.tasks;
        const milestones = await ai_service_1.aiService.defineMilestones(timeline, tasks);
        this.state.results.milestones = milestones;
        return {
            results: milestones,
            needsInput: false,
            completed: false
        };
    }
    async optimizeScheduleWithMCP(state) {
        const timeline = this.state.results.timeline;
        const resources = this.state.results.resources;
        const risks = this.state.results.risks;
        const optimizedSchedule = await ai_service_1.aiService.optimizeSchedule(timeline, resources, risks);
        this.state.results.optimizedSchedule = optimizedSchedule;
        return {
            results: optimizedSchedule,
            needsInput: false,
            completed: true // Final step
        };
    }
}
exports.TaskPlannerAgent = TaskPlannerAgent;
exports.default = TaskPlannerAgent;
//# sourceMappingURL=task-planner-agent.js.map