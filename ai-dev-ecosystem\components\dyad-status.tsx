"use client"

import React from 'react';
import { useDyad } from '@/hooks/use-dyad';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  XCircle, 
  Loader2, 
  RefreshCw,
  AlertTriangle,
  Zap
} from 'lucide-react';

interface DyadStatusProps {
  className?: string;
}

export default function DyadStatus({ className }: DyadStatusProps) {
  const { isConnected, isLoading, error, client } = useDyad();

  const handleRetry = async () => {
    try {
      await client.healthCheck();
      window.location.reload(); // Simple way to refresh the connection state
    } catch (err) {
      console.error('Retry failed:', err);
    }
  };

  if (isLoading) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Loader2 className="w-4 h-4 animate-spin text-blue-500" />
        <span className="text-sm text-[#666]">Connecting to Dyad...</span>
      </div>
    );
  }

  if (error || !isConnected) {
    return (
      <div className={`space-y-3 ${className}`}>
        <div className="flex items-center gap-2">
          <XCircle className="w-4 h-4 text-red-500" />
          <Badge variant="destructive" className="bg-red-900/20 text-red-400 border-red-800">
            Disconnected
          </Badge>
        </div>
        
        <Alert className="bg-red-900/10 border-red-800/30">
          <AlertTriangle className="h-4 w-4 text-red-500" />
          <AlertDescription className="text-red-400 text-sm">
            <div className="space-y-2">
              <p>Cannot connect to Dyad backend server.</p>
              <p className="text-xs text-red-300">
                Make sure the Dyad web server is running on http://localhost:3002
              </p>
              {error && (
                <p className="text-xs text-red-300 font-mono bg-red-900/20 p-2 rounded">
                  {error}
                </p>
              )}
              <div className="flex gap-2 mt-3">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleRetry}
                  className="bg-red-900/20 border-red-800 text-red-400 hover:bg-red-900/30"
                >
                  <RefreshCw className="w-3 h-3 mr-1" />
                  Retry
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => window.open('http://localhost:3002/health', '_blank')}
                  className="bg-red-900/20 border-red-800 text-red-400 hover:bg-red-900/30"
                >
                  Check Server
                </Button>
              </div>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <CheckCircle className="w-4 h-4 text-green-500" />
      <Badge variant="secondary" className="bg-green-900/20 text-green-400 border-green-800">
        <Zap className="w-3 h-3 mr-1" />
        Connected
      </Badge>
      <span className="text-xs text-[#666]">Dyad Backend</span>
    </div>
  );
}
