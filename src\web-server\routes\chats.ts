import { Router } from 'express';
import { Server } from 'socket.io';
import { z } from 'zod';
import log from 'electron-log';
import { streamText } from 'ai';
import { db } from '../../db';
import { chats, messages, apps } from '../../db/schema';
import { eq, desc } from 'drizzle-orm';
import { getModelClient } from '../../ipc/utils/get_model_client';
import { readSettings } from '../../main/settings';
import { constructSystemPrompt, readAiRules } from '../../prompts/system_prompt';
import { getDyadAppPath } from '../../paths/paths';
import { extractCodebase } from '../../utils/codebase';

const logger = log.scope('web-server:chats');

// Validation schemas
const CreateChatSchema = z.object({
  appId: z.number(),
  title: z.string().optional(),
});

const SendMessageSchema = z.object({
  content: z.string().min(1),
  role: z.enum(['user', 'assistant']).default('user'),
});

const StreamChatSchema = z.object({
  message: z.string().min(1),
  model: z.object({
    name: z.string(),
    provider: z.string(),
  }),
  appId: z.number(),
});

export function setupChatRoutes(io: Server) {
  const router = Router();

  // GET /api/chats - List chats (optionally filtered by appId)
  router.get('/', async (req, res) => {
    try {
      const appId = req.query.appId ? parseInt(req.query.appId as string) : null;
      
      let allChats;

      if (appId) {
        allChats = await db.select().from(chats)
          .where(eq(chats.appId, appId))
          .orderBy(desc(chats.createdAt));
      } else {
        allChats = await db.select().from(chats)
          .orderBy(desc(chats.createdAt));
      }
      res.json(allChats);
    } catch (error) {
      logger.error('Error listing chats:', error);
      res.status(500).json({ error: 'Failed to list chats' });
    }
  });

  // GET /api/chats/:id - Get specific chat with messages
  router.get('/:id', async (req, res) => {
    try {
      const chatId = parseInt(req.params.id);
      if (isNaN(chatId)) {
        return res.status(400).json({ error: 'Invalid chat ID' });
      }

      const chat = await db.select().from(chats)
        .where(eq(chats.id, chatId))
        .limit(1);

      if (chat.length === 0) {
        return res.status(404).json({ error: 'Chat not found' });
      }

      // Get messages for this chat
      const chatMessages = await db.select().from(messages)
        .where(eq(messages.chatId, chatId))
        .orderBy(messages.createdAt);

      const chatWithMessages = {
        ...chat[0],
        messages: chatMessages,
      };

      res.json(chatWithMessages);
    } catch (error) {
      logger.error('Error getting chat:', error);
      res.status(500).json({ error: 'Failed to get chat' });
    }
  });

  // POST /api/chats - Create new chat
  router.post('/', async (req, res) => {
    try {
      const params = CreateChatSchema.parse(req.body);
      
      const [chat] = await db
        .insert(chats)
        .values({
          appId: params.appId,
          title: params.title,
        })
        .returning();

      logger.info(`Created chat: ${chat.id} for app ${params.appId}`);
      res.status(201).json(chat);
    } catch (error) {
      logger.error('Error creating chat:', error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: 'Invalid request data', details: error.errors });
      } else {
        res.status(500).json({ error: 'Failed to create chat' });
      }
    }
  });

  // POST /api/chats/:id/messages - Add message to chat
  router.post('/:id/messages', async (req, res) => {
    try {
      const chatId = parseInt(req.params.id);
      if (isNaN(chatId)) {
        return res.status(400).json({ error: 'Invalid chat ID' });
      }

      const messageData = SendMessageSchema.parse(req.body);

      // Verify chat exists
      const chat = await db.select().from(chats)
        .where(eq(chats.id, chatId))
        .limit(1);

      if (chat.length === 0) {
        return res.status(404).json({ error: 'Chat not found' });
      }

      // Add message to chat
      const [message] = await db
        .insert(messages)
        .values({
          chatId,
          content: messageData.content,
          role: messageData.role,
        })
        .returning();

      logger.info(`Added message to chat ${chatId}`);
      res.status(201).json(message);
    } catch (error) {
      logger.error('Error adding message:', error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: 'Invalid request data', details: error.errors });
      } else {
        res.status(500).json({ error: 'Failed to add message' });
      }
    }
  });

  // POST /api/chats/:id/stream - Stream chat response
  router.post('/:id/stream', async (req, res) => {
    try {
      const chatId = parseInt(req.params.id);
      if (isNaN(chatId)) {
        return res.status(400).json({ error: 'Invalid chat ID' });
      }

      const streamData = StreamChatSchema.parse(req.body);

      // Verify chat exists
      const chat = await db.select().from(chats)
        .where(eq(chats.id, chatId))
        .limit(1);

      if (chat.length === 0) {
        return res.status(404).json({ error: 'Chat not found' });
      }

      // Set up Server-Sent Events
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': process.env.WEB_FRONTEND_URL || 'http://localhost:3001',
        'Access-Control-Allow-Credentials': 'true',
        'Access-Control-Allow-Headers': 'Cache-Control, Content-Type, Authorization',
      });

      // Add user message to chat
      await db.insert(messages).values({
        chatId,
        content: streamData.message,
        role: 'user',
      });

      // Get app information for context
      const app = await db.select().from(chats)
        .leftJoin(apps, eq(chats.appId, apps.id))
        .where(eq(chats.id, chatId))
        .limit(1);

      if (app.length === 0 || !app[0].apps) {
        return res.status(404).json({ error: 'App not found' });
      }

      const appData = app[0].apps;

      // Get recent message history for context
      const recentMessages = await db.select().from(messages)
        .where(eq(messages.chatId, chatId))
        .orderBy(desc(messages.createdAt))
        .limit(20);

      try {
        // Get user settings and model configuration
        const settings = readSettings();
        const selectedModel = {
          provider: streamData.model.provider,
          name: streamData.model.name,
        };

        // Get model client for the specified provider/model
        const { modelClient } = await getModelClient(selectedModel, settings);

        // Prepare message history
        const messageHistory = recentMessages.reverse().map((msg) => ({
          role: msg.role as 'user' | 'assistant',
          content: msg.content,
        }));

        // Add the current user message to history
        messageHistory.push({
          role: 'user',
          content: streamData.message,
        });

        // Get app path and extract codebase context
        const appPath = getDyadAppPath(appData.path);

        // Read AI rules from the app
        let aiRules: string | undefined;
        try {
          aiRules = await readAiRules(appPath);
        } catch (error) {
          logger.warn('Failed to read AI rules:', error);
        }

        let systemPrompt = constructSystemPrompt({
          aiRules,
          chatMode: settings.selectedChatMode || 'build'
        });

        // Extract codebase context and create proper codebase prompt
        let codebasePrompt = '';
        try {
          const { formattedOutput: codebaseInfo } = await extractCodebase({
            appPath,
            chatContext: appData.chatContext || {},
          });

          if (codebaseInfo) {
            codebasePrompt = `This is my codebase. ${codebaseInfo}`;
          }
        } catch (error) {
          logger.warn('Failed to extract codebase context:', error);
        }

        // Prepare messages for the AI (following Dyad's pattern)
        const chatMessages = [
          { role: 'system', content: systemPrompt },
          // Add codebase context if available
          ...(codebasePrompt ? [
            { role: 'user', content: codebasePrompt },
            { role: 'assistant', content: 'OK, got it. I\'m ready to help' }
          ] : []),
          ...messageHistory.slice(-10), // Limit to recent messages
        ];

        // Start streaming response
        const { textStream } = await streamText({
          model: modelClient.model,
          messages: chatMessages,
          temperature: 0.1,
          maxTokens: 4000,
        });

        let fullResponse = '';

        // Stream the response
        for await (const textPart of textStream) {
          fullResponse += textPart;
          res.write(`data: ${JSON.stringify({ content: textPart, done: false })}\n\n`);
        }

        // Send completion signal
        res.write(`data: ${JSON.stringify({ content: '', done: true })}\n\n`);

        // Add assistant message to chat
        const [assistantMessage] = await db.insert(messages).values({
          chatId,
          content: fullResponse,
          role: 'assistant',
        }).returning();

        // Process the AI response to extract and apply code changes
        try {
          const { processFullResponseActions } = await import('../../ipc/processors/response_processor');
          const processingResult = await processFullResponseActions(
            fullResponse,
            chatId,
            { chatSummary: undefined, messageId: assistantMessage.id }
          );

          if (processingResult.updatedFiles) {
            logger.info(`Applied code changes for chat ${chatId}`);

            // Notify clients that files were updated
            io.emit('files-updated', {
              chatId,
              appId: streamData.appId,
              updatedFiles: processingResult.updatedFiles
            });
          }

          if (processingResult.error) {
            logger.error(`Error processing code changes: ${processingResult.error}`);
          }
        } catch (error) {
          logger.error('Error processing AI response for code changes:', error);
        }

        // Notify clients via Socket.IO
        io.emit('chat-message-added', {
          chatId,
          message: assistantMessage,
          appId: streamData.appId
        });

        logger.info(`Streaming response completed for chat ${chatId}`);
      } catch (error) {
        logger.error('Error during LLM streaming:', error);
        res.write(`data: ${JSON.stringify({ error: 'Failed to generate response', done: true })}\n\n`);
      }

      res.end();

      logger.info(`Streamed response for chat ${chatId}`);
    } catch (error) {
      logger.error('Error streaming chat:', error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: 'Invalid request data', details: error.errors });
      } else {
        res.status(500).json({ error: 'Failed to stream chat' });
      }
    }
  });

  // DELETE /api/chats/:id - Delete chat
  router.delete('/:id', async (req, res) => {
    try {
      const chatId = parseInt(req.params.id);
      if (isNaN(chatId)) {
        return res.status(400).json({ error: 'Invalid chat ID' });
      }

      // Delete chat messages first (due to foreign key constraint)
      await db.delete(messages).where(eq(messages.chatId, chatId));
      
      // Delete chat
      await db.delete(chats).where(eq(chats.id, chatId));

      // Notify clients via Socket.IO
      io.emit('chat-deleted', {
        chatId
      });

      logger.info(`Deleted chat: ${chatId}`);
      res.json({ message: 'Chat deleted successfully' });
    } catch (error) {
      logger.error('Error deleting chat:', error);
      res.status(500).json({ error: 'Failed to delete chat' });
    }
  });

  // PUT /api/chats/:id - Update chat (e.g., title)
  router.put('/:id', async (req, res) => {
    try {
      const chatId = parseInt(req.params.id);
      if (isNaN(chatId)) {
        return res.status(400).json({ error: 'Invalid chat ID' });
      }

      const { title } = req.body;

      if (typeof title !== 'string') {
        return res.status(400).json({ error: 'Title must be a string' });
      }

      const [updatedChat] = await db
        .update(chats)
        .set({ title })
        .where(eq(chats.id, chatId))
        .returning();

      if (!updatedChat) {
        return res.status(404).json({ error: 'Chat not found' });
      }

      res.json(updatedChat);
    } catch (error) {
      logger.error('Error updating chat:', error);
      res.status(500).json({ error: 'Failed to update chat' });
    }
  });

  return router;
}
