/**
 * AG3NT Framework - Multi-Agent Workflows
 * 
 * Comprehensive workflow definitions that coordinate multiple specialized agents
 * for complete project development lifecycle.
 * 
 * Features:
 * - End-to-end project development workflows
 * - Agent coordination and handoffs
 * - Parallel and sequential execution patterns
 * - Error handling and recovery
 * - Progress monitoring and reporting
 */

import { WorkflowDefinition, WorkflowStep } from "../agents/workflow-agent"
import { TaskDelegationSystem } from "../coordination/task-delegation-system"
import { ConsensusProtocolEngine } from "../coordination/consensus-protocol-engine"
import { WorkflowHandoffManager } from "../coordination/workflow-handoff-manager"
import { CoordinationPatternRegistry } from "../coordination/coordination-pattern-registry"

/**
 * Complete Project Development Workflow
 * Coordinates all agents from planning to deployment
 */
export const CompleteProjectWorkflow: WorkflowDefinition = {
  workflowId: 'complete-project-development',
  name: 'Complete Project Development',
  description: 'End-to-end project development from requirements to deployment',
  version: '1.0.0',
  type: 'sequential',
  steps: [
    {
      stepId: 'project-planning',
      name: 'Project Planning',
      description: 'Analyze requirements and create comprehensive project plan',
      type: 'agent_task',
      agentType: 'planner',
      input: {
        required: [
          { name: 'requirements', type: 'object', description: 'Project requirements and constraints' },
          { name: 'context', type: 'object', description: 'Project context and background' }
        ],
        optional: [
          { name: 'preferences', type: 'object', description: 'User preferences and constraints' }
        ],
        sources: [
          { type: 'user_input', source: 'initial_requirements', mapping: { 'requirements': 'requirements' } }
        ]
      },
      output: {
        produces: [
          { name: 'project_plan', type: 'object', description: 'Comprehensive project plan' },
          { name: 'architecture', type: 'object', description: 'System architecture design' },
          { name: 'tech_stack', type: 'object', description: 'Technology stack recommendations' }
        ],
        destinations: [
          { type: 'step_input', destination: 'task-planning', mapping: { 'project_plan': 'project_plan' } }
        ]
      },
      conditions: [],
      timeout: 300000, // 5 minutes
      retries: 2,
      priority: 'high'
    },
    {
      stepId: 'task-planning',
      name: 'Task Planning',
      description: 'Break down project plan into executable tasks',
      type: 'agent_task',
      agentType: 'task-planner',
      input: {
        required: [
          { name: 'project_plan', type: 'object', description: 'Project plan from planning step' },
          { name: 'requirements', type: 'object', description: 'Original requirements' }
        ],
        optional: [
          { name: 'constraints', type: 'object', description: 'Time and resource constraints' }
        ],
        sources: [
          { type: 'step_output', source: 'project-planning', mapping: { 'project_plan': 'project_plan' } }
        ]
      },
      output: {
        produces: [
          { name: 'tasks', type: 'array', description: 'List of executable tasks' },
          { name: 'dependencies', type: 'array', description: 'Task dependencies' },
          { name: 'timeline', type: 'object', description: 'Project timeline' }
        ],
        destinations: [
          { type: 'step_input', destination: 'parallel-development', mapping: { 'tasks': 'tasks' } }
        ]
      },
      conditions: [],
      timeout: 180000, // 3 minutes
      retries: 2,
      priority: 'high'
    },
    {
      stepId: 'parallel-development',
      name: 'Parallel Development',
      description: 'Execute frontend and backend development in parallel',
      type: 'parallel_group',
      input: {
        required: [
          { name: 'tasks', type: 'array', description: 'Tasks to be executed' },
          { name: 'architecture', type: 'object', description: 'System architecture' }
        ],
        optional: [],
        sources: [
          { type: 'step_output', source: 'task-planning', mapping: { 'tasks': 'tasks' } },
          { type: 'step_output', source: 'project-planning', mapping: { 'architecture': 'architecture' } }
        ]
      },
      output: {
        produces: [
          { name: 'frontend_code', type: 'object', description: 'Frontend implementation' },
          { name: 'backend_code', type: 'object', description: 'Backend implementation' }
        ],
        destinations: [
          { type: 'step_input', destination: 'integration-testing', mapping: { 
            'frontend_code': 'frontend_code',
            'backend_code': 'backend_code'
          }}
        ]
      },
      conditions: [],
      timeout: 1800000, // 30 minutes
      retries: 1,
      priority: 'critical'
    },
    {
      stepId: 'integration-testing',
      name: 'Integration Testing',
      description: 'Test integrated frontend and backend components',
      type: 'agent_task',
      agentType: 'tester',
      input: {
        required: [
          { name: 'frontend_code', type: 'object', description: 'Frontend implementation' },
          { name: 'backend_code', type: 'object', description: 'Backend implementation' },
          { name: 'test_requirements', type: 'object', description: 'Testing requirements' }
        ],
        optional: [],
        sources: [
          { type: 'step_output', source: 'parallel-development', mapping: { 
            'frontend_code': 'frontend_code',
            'backend_code': 'backend_code'
          }}
        ]
      },
      output: {
        produces: [
          { name: 'test_results', type: 'object', description: 'Integration test results' },
          { name: 'quality_metrics', type: 'object', description: 'Quality assessment metrics' }
        ],
        destinations: [
          { type: 'step_input', destination: 'code-review', mapping: { 
            'test_results': 'test_results',
            'quality_metrics': 'quality_metrics'
          }}
        ]
      },
      conditions: [],
      timeout: 600000, // 10 minutes
      retries: 2,
      priority: 'high'
    },
    {
      stepId: 'code-review',
      name: 'Code Review',
      description: 'Comprehensive code review and quality assessment',
      type: 'agent_task',
      agentType: 'reviewer',
      input: {
        required: [
          { name: 'frontend_code', type: 'object', description: 'Frontend code to review' },
          { name: 'backend_code', type: 'object', description: 'Backend code to review' },
          { name: 'test_results', type: 'object', description: 'Test results' },
          { name: 'quality_metrics', type: 'object', description: 'Quality metrics' }
        ],
        optional: [],
        sources: [
          { type: 'step_output', source: 'parallel-development', mapping: { 
            'frontend_code': 'frontend_code',
            'backend_code': 'backend_code'
          }},
          { type: 'step_output', source: 'integration-testing', mapping: { 
            'test_results': 'test_results',
            'quality_metrics': 'quality_metrics'
          }}
        ]
      },
      output: {
        produces: [
          { name: 'review_results', type: 'object', description: 'Code review results' },
          { name: 'approval_status', type: 'string', description: 'Approval status' },
          { name: 'recommendations', type: 'array', description: 'Improvement recommendations' }
        ],
        destinations: [
          { type: 'step_input', destination: 'deployment-preparation', mapping: { 
            'review_results': 'review_results',
            'approval_status': 'approval_status'
          }}
        ]
      },
      conditions: [
        {
          type: 'if',
          expression: 'approval_status === "approved"',
          action: 'continue',
          target: 'deployment-preparation'
        },
        {
          type: 'if',
          expression: 'approval_status === "needs_changes"',
          action: 'branch',
          target: 'fix-issues'
        }
      ],
      timeout: 300000, // 5 minutes
      retries: 1,
      priority: 'high'
    },
    {
      stepId: 'deployment-preparation',
      name: 'Deployment Preparation',
      description: 'Prepare application for deployment',
      type: 'agent_task',
      agentType: 'executor',
      input: {
        required: [
          { name: 'frontend_code', type: 'object', description: 'Approved frontend code' },
          { name: 'backend_code', type: 'object', description: 'Approved backend code' },
          { name: 'deployment_config', type: 'object', description: 'Deployment configuration' }
        ],
        optional: [],
        sources: [
          { type: 'step_output', source: 'parallel-development', mapping: { 
            'frontend_code': 'frontend_code',
            'backend_code': 'backend_code'
          }}
        ]
      },
      output: {
        produces: [
          { name: 'deployment_package', type: 'object', description: 'Deployment package' },
          { name: 'deployment_instructions', type: 'object', description: 'Deployment instructions' }
        ],
        destinations: []
      },
      conditions: [],
      timeout: 300000, // 5 minutes
      retries: 2,
      priority: 'medium'
    }
  ],
  dependencies: [
    { fromStep: 'project-planning', toStep: 'task-planning', type: 'finish_to_start' },
    { fromStep: 'task-planning', toStep: 'parallel-development', type: 'finish_to_start' },
    { fromStep: 'parallel-development', toStep: 'integration-testing', type: 'finish_to_start' },
    { fromStep: 'integration-testing', toStep: 'code-review', type: 'finish_to_start' },
    { fromStep: 'code-review', toStep: 'deployment-preparation', type: 'finish_to_start', condition: 'approval_status === "approved"' }
  ],
  triggers: [
    {
      type: 'manual',
      configuration: {}
    }
  ],
  outputs: [
    {
      name: 'project_deliverables',
      type: 'object',
      description: 'Complete project deliverables including code, tests, and documentation',
      source: 'deployment-preparation',
      format: 'json'
    }
  ]
}

/**
 * Frontend Development Sub-workflow
 * Specialized workflow for frontend development tasks
 */
export const FrontendDevelopmentWorkflow: WorkflowDefinition = {
  workflowId: 'frontend-development',
  name: 'Frontend Development',
  description: 'Specialized frontend development workflow',
  version: '1.0.0',
  type: 'sequential',
  steps: [
    {
      stepId: 'ui-design-analysis',
      name: 'UI Design Analysis',
      description: 'Analyze UI/UX requirements and design specifications',
      type: 'agent_task',
      agentType: 'frontend-coder',
      input: {
        required: [
          { name: 'design_specs', type: 'object', description: 'UI/UX design specifications' },
          { name: 'requirements', type: 'object', description: 'Frontend requirements' }
        ],
        optional: [],
        sources: []
      },
      output: {
        produces: [
          { name: 'component_structure', type: 'object', description: 'Component architecture' },
          { name: 'styling_approach', type: 'object', description: 'Styling strategy' }
        ],
        destinations: []
      },
      conditions: [],
      timeout: 180000,
      retries: 2,
      priority: 'high'
    },
    {
      stepId: 'component-development',
      name: 'Component Development',
      description: 'Develop React/Vue components',
      type: 'agent_task',
      agentType: 'frontend-coder',
      input: {
        required: [
          { name: 'component_structure', type: 'object', description: 'Component architecture' },
          { name: 'styling_approach', type: 'object', description: 'Styling strategy' }
        ],
        optional: [],
        sources: []
      },
      output: {
        produces: [
          { name: 'components', type: 'array', description: 'Developed components' },
          { name: 'styles', type: 'object', description: 'Component styles' }
        ],
        destinations: []
      },
      conditions: [],
      timeout: 900000, // 15 minutes
      retries: 1,
      priority: 'critical'
    },
    {
      stepId: 'frontend-testing',
      name: 'Frontend Testing',
      description: 'Unit and integration testing for frontend components',
      type: 'agent_task',
      agentType: 'tester',
      input: {
        required: [
          { name: 'components', type: 'array', description: 'Components to test' },
          { name: 'test_requirements', type: 'object', description: 'Testing requirements' }
        ],
        optional: [],
        sources: []
      },
      output: {
        produces: [
          { name: 'test_results', type: 'object', description: 'Frontend test results' },
          { name: 'coverage_report', type: 'object', description: 'Test coverage report' }
        ],
        destinations: []
      },
      conditions: [],
      timeout: 300000,
      retries: 2,
      priority: 'high'
    }
  ],
  dependencies: [
    { fromStep: 'ui-design-analysis', toStep: 'component-development', type: 'finish_to_start' },
    { fromStep: 'component-development', toStep: 'frontend-testing', type: 'finish_to_start' }
  ],
  triggers: [],
  outputs: [
    {
      name: 'frontend_deliverables',
      type: 'object',
      description: 'Complete frontend implementation with tests',
      source: 'frontend-testing',
      format: 'json'
    }
  ]
}

/**
 * Backend Development Sub-workflow
 * Specialized workflow for backend development tasks
 */
export const BackendDevelopmentWorkflow: WorkflowDefinition = {
  workflowId: 'backend-development',
  name: 'Backend Development',
  description: 'Specialized backend development workflow',
  version: '1.0.0',
  type: 'sequential',
  steps: [
    {
      stepId: 'api-design',
      name: 'API Design',
      description: 'Design REST/GraphQL APIs and database schema',
      type: 'agent_task',
      agentType: 'backend-coder',
      input: {
        required: [
          { name: 'requirements', type: 'object', description: 'Backend requirements' },
          { name: 'architecture', type: 'object', description: 'System architecture' }
        ],
        optional: [],
        sources: []
      },
      output: {
        produces: [
          { name: 'api_specification', type: 'object', description: 'API specification' },
          { name: 'database_schema', type: 'object', description: 'Database schema design' }
        ],
        destinations: []
      },
      conditions: [],
      timeout: 240000,
      retries: 2,
      priority: 'high'
    },
    {
      stepId: 'backend-implementation',
      name: 'Backend Implementation',
      description: 'Implement APIs, services, and database integration',
      type: 'agent_task',
      agentType: 'backend-coder',
      input: {
        required: [
          { name: 'api_specification', type: 'object', description: 'API specification' },
          { name: 'database_schema', type: 'object', description: 'Database schema' }
        ],
        optional: [],
        sources: []
      },
      output: {
        produces: [
          { name: 'api_implementation', type: 'object', description: 'API implementation' },
          { name: 'database_migrations', type: 'array', description: 'Database migrations' }
        ],
        destinations: []
      },
      conditions: [],
      timeout: 1200000, // 20 minutes
      retries: 1,
      priority: 'critical'
    },
    {
      stepId: 'backend-testing',
      name: 'Backend Testing',
      description: 'API testing, unit tests, and integration tests',
      type: 'agent_task',
      agentType: 'tester',
      input: {
        required: [
          { name: 'api_implementation', type: 'object', description: 'API implementation to test' },
          { name: 'test_requirements', type: 'object', description: 'Testing requirements' }
        ],
        optional: [],
        sources: []
      },
      output: {
        produces: [
          { name: 'test_results', type: 'object', description: 'Backend test results' },
          { name: 'api_documentation', type: 'object', description: 'API documentation' }
        ],
        destinations: []
      },
      conditions: [],
      timeout: 360000,
      retries: 2,
      priority: 'high'
    }
  ],
  dependencies: [
    { fromStep: 'api-design', toStep: 'backend-implementation', type: 'finish_to_start' },
    { fromStep: 'backend-implementation', toStep: 'backend-testing', type: 'finish_to_start' }
  ],
  triggers: [],
  outputs: [
    {
      name: 'backend_deliverables',
      type: 'object',
      description: 'Complete backend implementation with tests and documentation',
      source: 'backend-testing',
      format: 'json'
    }
  ]
}

/**
 * Feature Development Workflow
 * Adds new features to existing projects with full coordination
 */
export const FeatureDevelopmentWorkflow: WorkflowDefinition = {
  workflowId: 'feature-development',
  name: 'Feature Development',
  description: 'Add new features to existing projects with multi-agent coordination',
  version: '1.0.0',
  type: 'hybrid',
  steps: [
    {
      stepId: 'context-analysis',
      name: 'Context Analysis',
      description: 'Analyze existing codebase and feature requirements',
      type: 'agent_task',
      agentType: 'context-engine',
      coordination: {
        pattern: 'workflow_handoff',
        enableStateValidation: true,
        enableCheckpoints: true
      },
      input: {
        required: [
          { name: 'feature_requirements', type: 'object', description: 'Feature requirements and specifications' },
          { name: 'codebase_context', type: 'object', description: 'Current codebase context' }
        ]
      },
      output: {
        produces: [
          { name: 'impact_analysis', type: 'object', description: 'Feature impact analysis' },
          { name: 'implementation_strategy', type: 'object', description: 'Implementation approach' }
        ]
      }
    },
    {
      stepId: 'task-planning',
      name: 'Task Planning',
      description: 'Break down feature into implementable tasks',
      type: 'agent_task',
      agentType: 'task-planner',
      coordination: {
        pattern: 'consensus_decision',
        stakeholders: ['task-planner', 'frontend-coder', 'backend-coder'],
        votingTimeout: 180000
      },
      dependencies: ['context-analysis'],
      input: {
        sources: [
          { type: 'step_output', source: 'context-analysis', mapping: { 'impact_analysis': 'analysis' } }
        ]
      },
      output: {
        produces: [
          { name: 'task_breakdown', type: 'array', description: 'Detailed task breakdown' },
          { name: 'implementation_plan', type: 'object', description: 'Implementation timeline and dependencies' }
        ]
      }
    },
    {
      stepId: 'parallel-implementation',
      name: 'Parallel Implementation',
      description: 'Implement frontend and backend components in parallel',
      type: 'parallel',
      coordination: {
        pattern: 'hierarchical_delegation',
        delegationType: 'peer',
        enableLoadBalancing: true
      },
      dependencies: ['task-planning'],
      subSteps: [
        {
          stepId: 'frontend-implementation',
          name: 'Frontend Implementation',
          description: 'Implement frontend components',
          type: 'agent_task',
          agentType: 'frontend-coder',
          input: {
            sources: [
              { type: 'step_output', source: 'task-planning', mapping: { 'task_breakdown': 'frontend_tasks' } }
            ]
          },
          output: {
            produces: [
              { name: 'frontend_code', type: 'object', description: 'Frontend implementation' },
              { name: 'frontend_tests', type: 'array', description: 'Frontend test cases' }
            ]
          }
        },
        {
          stepId: 'backend-implementation',
          name: 'Backend Implementation',
          description: 'Implement backend components',
          type: 'agent_task',
          agentType: 'backend-coder',
          input: {
            sources: [
              { type: 'step_output', source: 'task-planning', mapping: { 'task_breakdown': 'backend_tasks' } }
            ]
          },
          output: {
            produces: [
              { name: 'backend_code', type: 'object', description: 'Backend implementation' },
              { name: 'backend_tests', type: 'array', description: 'Backend test cases' }
            ]
          }
        }
      ]
    },
    {
      stepId: 'integration-testing',
      name: 'Integration Testing',
      description: 'Test integrated feature implementation',
      type: 'agent_task',
      agentType: 'tester',
      coordination: {
        pattern: 'workflow_handoff',
        enableRollback: true,
        rollbackTriggers: ['test_failure', 'quality_threshold']
      },
      dependencies: ['parallel-implementation'],
      input: {
        sources: [
          { type: 'step_output', source: 'frontend-implementation', mapping: { 'frontend_code': 'frontend' } },
          { type: 'step_output', source: 'backend-implementation', mapping: { 'backend_code': 'backend' } }
        ]
      },
      output: {
        produces: [
          { name: 'test_results', type: 'object', description: 'Comprehensive test results' },
          { name: 'quality_metrics', type: 'object', description: 'Code quality metrics' }
        ]
      }
    },
    {
      stepId: 'code-review',
      name: 'Code Review',
      description: 'Comprehensive code review and quality assurance',
      type: 'agent_task',
      agentType: 'reviewer',
      coordination: {
        pattern: 'consensus_decision',
        stakeholders: ['reviewer', 'frontend-coder', 'backend-coder'],
        requiresApproval: true
      },
      dependencies: ['integration-testing'],
      input: {
        sources: [
          { type: 'step_output', source: 'parallel-implementation', mapping: { 'all_code': 'implementation' } },
          { type: 'step_output', source: 'integration-testing', mapping: { 'test_results': 'tests' } }
        ]
      },
      output: {
        produces: [
          { name: 'review_results', type: 'object', description: 'Code review results and recommendations' },
          { name: 'approval_status', type: 'boolean', description: 'Review approval status' }
        ]
      }
    },
    {
      stepId: 'deployment',
      name: 'Feature Deployment',
      description: 'Deploy feature to target environment',
      type: 'agent_task',
      agentType: 'devops',
      coordination: {
        pattern: 'hierarchical_delegation',
        authorityLevel: 8,
        requiresConfirmation: true
      },
      dependencies: ['code-review'],
      conditions: [
        { field: 'approval_status', operator: 'equals', value: true }
      ],
      input: {
        sources: [
          { type: 'step_output', source: 'code-review', mapping: { 'approved_code': 'deployment_package' } }
        ]
      },
      output: {
        produces: [
          { name: 'deployment_result', type: 'object', description: 'Deployment status and details' },
          { name: 'monitoring_setup', type: 'object', description: 'Monitoring and alerting configuration' }
        ]
      }
    }
  ],
  errorHandling: {
    strategy: 'rollback_and_retry',
    maxRetries: 3,
    rollbackPoints: ['context-analysis', 'task-planning', 'integration-testing'],
    escalationPath: ['human_intervention', 'emergency_workflow']
  },
  monitoring: {
    enableRealTimeTracking: true,
    enablePerformanceMetrics: true,
    enableQualityGates: true,
    alertThresholds: {
      executionTime: 3600000, // 1 hour
      errorRate: 0.1,
      qualityScore: 0.8
    }
  }
}

/**
 * Bug Fix Workflow
 * Systematic approach to identifying and fixing bugs
 */
export const BugFixWorkflow: WorkflowDefinition = {
  workflowId: 'bug-fix',
  name: 'Bug Fix Workflow',
  description: 'Systematic bug identification, analysis, and resolution',
  version: '1.0.0',
  type: 'sequential',
  steps: [
    {
      stepId: 'bug-analysis',
      name: 'Bug Analysis',
      description: 'Analyze bug reports and identify root cause',
      type: 'agent_task',
      agentType: 'context-engine',
      coordination: {
        pattern: 'workflow_handoff',
        enableStateValidation: true
      },
      input: {
        required: [
          { name: 'bug_report', type: 'object', description: 'Bug report with symptoms and context' },
          { name: 'reproduction_steps', type: 'array', description: 'Steps to reproduce the bug' }
        ]
      },
      output: {
        produces: [
          { name: 'root_cause_analysis', type: 'object', description: 'Root cause analysis' },
          { name: 'affected_components', type: 'array', description: 'List of affected system components' }
        ]
      }
    },
    {
      stepId: 'fix-planning',
      name: 'Fix Planning',
      description: 'Plan the bug fix approach and implementation',
      type: 'agent_task',
      agentType: 'task-planner',
      coordination: {
        pattern: 'consensus_decision',
        stakeholders: ['task-planner', 'relevant-coder'],
        decisionCriteria: ['impact', 'complexity', 'risk']
      },
      dependencies: ['bug-analysis'],
      input: {
        sources: [
          { type: 'step_output', source: 'bug-analysis', mapping: { 'root_cause_analysis': 'analysis' } }
        ]
      },
      output: {
        produces: [
          { name: 'fix_strategy', type: 'object', description: 'Bug fix strategy and approach' },
          { name: 'implementation_tasks', type: 'array', description: 'Specific implementation tasks' }
        ]
      }
    },
    {
      stepId: 'fix-implementation',
      name: 'Fix Implementation',
      description: 'Implement the bug fix',
      type: 'conditional',
      coordination: {
        pattern: 'hierarchical_delegation',
        delegationType: 'capability_based'
      },
      dependencies: ['fix-planning'],
      conditions: [
        {
          field: 'affected_components',
          operator: 'contains',
          value: 'frontend',
          then: { agentType: 'frontend-coder' }
        },
        {
          field: 'affected_components',
          operator: 'contains',
          value: 'backend',
          then: { agentType: 'backend-coder' }
        },
        {
          field: 'affected_components',
          operator: 'contains',
          value: 'database',
          then: { agentType: 'backend-coder' }
        }
      ],
      input: {
        sources: [
          { type: 'step_output', source: 'fix-planning', mapping: { 'implementation_tasks': 'tasks' } }
        ]
      },
      output: {
        produces: [
          { name: 'fix_implementation', type: 'object', description: 'Bug fix implementation' },
          { name: 'fix_tests', type: 'array', description: 'Tests for the bug fix' }
        ]
      }
    },
    {
      stepId: 'fix-testing',
      name: 'Fix Testing',
      description: 'Test the bug fix thoroughly',
      type: 'agent_task',
      agentType: 'tester',
      coordination: {
        pattern: 'workflow_handoff',
        enableRollback: true,
        rollbackTriggers: ['regression_detected', 'fix_ineffective']
      },
      dependencies: ['fix-implementation'],
      input: {
        sources: [
          { type: 'step_output', source: 'fix-implementation', mapping: { 'fix_implementation': 'fix' } },
          { type: 'step_output', source: 'bug-analysis', mapping: { 'reproduction_steps': 'original_steps' } }
        ]
      },
      output: {
        produces: [
          { name: 'test_results', type: 'object', description: 'Comprehensive test results' },
          { name: 'regression_analysis', type: 'object', description: 'Regression test analysis' }
        ]
      }
    },
    {
      stepId: 'fix-review',
      name: 'Fix Review',
      description: 'Review the bug fix for quality and completeness',
      type: 'agent_task',
      agentType: 'reviewer',
      coordination: {
        pattern: 'consensus_decision',
        stakeholders: ['reviewer', 'original-implementer'],
        requiresApproval: true
      },
      dependencies: ['fix-testing'],
      input: {
        sources: [
          { type: 'step_output', source: 'fix-implementation', mapping: { 'fix_implementation': 'fix' } },
          { type: 'step_output', source: 'fix-testing', mapping: { 'test_results': 'tests' } }
        ]
      },
      output: {
        produces: [
          { name: 'review_results', type: 'object', description: 'Fix review results' },
          { name: 'approval_status', type: 'boolean', description: 'Fix approval status' }
        ]
      }
    }
  ],
  errorHandling: {
    strategy: 'escalate_and_retry',
    maxRetries: 2,
    escalationPath: ['senior_developer', 'team_lead', 'human_intervention']
  }
}

// Export all workflows
export const MultiAgentWorkflows = {
  CompleteProjectWorkflow,
  FrontendDevelopmentWorkflow,
  BackendDevelopmentWorkflow,
  FeatureDevelopmentWorkflow,
  BugFixWorkflow
}

export default MultiAgentWorkflows
