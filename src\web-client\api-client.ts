/**
 * HTTP API Client for Dyad Web Frontend
 * This replaces the IPC client for web-based usage
 */

export class DyadApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = 'http://localhost:3002') {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      credentials: 'include',
      ...options,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Unknown error' }));
      throw new Error(error.error || `HTTP ${response.status}`);
    }

    return response.json();
  }

  // Apps API
  async listApps() {
    return this.request('/api/apps');
  }

  async getApp(id: number) {
    return this.request(`/api/apps/${id}`);
  }

  async createApp(data: { name: string; template?: string }) {
    return this.request('/api/apps', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateApp(id: number, data: { name?: string; supabaseProjectId?: string }) {
    return this.request(`/api/apps/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteApp(id: number) {
    return this.request(`/api/apps/${id}`, {
      method: 'DELETE',
    });
  }

  async startApp(id: number) {
    return this.request(`/api/apps/${id}/start`, {
      method: 'POST',
    });
  }

  async stopApp(id: number) {
    return this.request(`/api/apps/${id}/stop`, {
      method: 'POST',
    });
  }

  async readAppFile(appId: number, filePath: string) {
    return this.request(`/api/apps/${appId}/files?path=${encodeURIComponent(filePath)}`);
  }

  async writeAppFile(appId: number, filePath: string, content: string) {
    return this.request(`/api/apps/${appId}/files?path=${encodeURIComponent(filePath)}`, {
      method: 'PUT',
      body: JSON.stringify({ content }),
    });
  }

  // Chats API
  async listChats(appId?: number) {
    const query = appId ? `?appId=${appId}` : '';
    return this.request(`/api/chats${query}`);
  }

  async getChat(id: number) {
    return this.request(`/api/chats/${id}`);
  }

  async createChat(data: { appId: number; title?: string }) {
    return this.request('/api/chats', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async addMessage(chatId: number, data: { content: string; role?: 'user' | 'assistant' }) {
    return this.request(`/api/chats/${chatId}/messages`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async deleteChat(id: number) {
    return this.request(`/api/chats/${id}`, {
      method: 'DELETE',
    });
  }

  async updateChat(id: number, data: { title: string }) {
    return this.request(`/api/chats/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // Streaming chat
  async streamChat(
    chatId: number, 
    data: { 
      message: string; 
      model: { name: string; provider: string }; 
      appId: number; 
    },
    onChunk: (chunk: { content: string; done: boolean }) => void
  ) {
    const response = await fetch(`${this.baseUrl}/api/chats/${chatId}/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('No response body');
    }

    const decoder = new TextDecoder();
    
    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              onChunk(data);
              if (data.done) return;
            } catch (e) {
              // Ignore malformed JSON
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  // Settings API
  async getSettings() {
    return this.request('/api/settings');
  }

  async updateSettings(data: any) {
    return this.request('/api/settings', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async getEnvVars() {
    return this.request('/api/settings/env-vars');
  }

  async getProviderSettings(provider: string) {
    return this.request(`/api/settings/provider/${provider}`);
  }

  async updateProviderSettings(provider: string, data: any) {
    return this.request(`/api/settings/provider/${provider}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // Language Models API
  async getLanguageModelProviders() {
    return this.request('/api/language-models/providers');
  }

  async getLanguageModelsByProviders() {
    return this.request('/api/language-models/by-providers');
  }

  async getAllLanguageModels() {
    return this.request('/api/language-models/all');
  }

  async getProviderModels(provider: string) {
    return this.request(`/api/language-models/provider/${provider}`);
  }

  // Node.js API
  async getNodeStatus() {
    return this.request('/api/node/status');
  }

  async getNodeVersion() {
    return this.request('/api/node/version');
  }

  async getPnpmVersion() {
    return this.request('/api/node/pnpm-version');
  }

  async installPnpm() {
    return this.request('/api/node/install-pnpm', {
      method: 'POST',
    });
  }

  // Health check
  async healthCheck() {
    return this.request('/health');
  }
}

// Export a default instance
export const apiClient = new DyadApiClient();

// Export types for the frontend to use
export interface App {
  id: number;
  name: string;
  path: string;
  createdAt: string;
  updatedAt: string;
  supabaseProjectId?: string;
}

export interface Chat {
  id: number;
  appId: number;
  title?: string;
  createdAt: string;
  updatedAt: string;
  messages?: ChatMessage[];
}

export interface ChatMessage {
  id: number;
  chatId: number;
  content: string;
  role: 'user' | 'assistant';
  createdAt: string;
}

export interface LanguageModel {
  name: string;
  displayName: string;
  description: string;
  maxOutputTokens: number;
  contextWindow: number;
  provider?: string;
}

export interface LanguageModelProvider {
  id: string;
  name: string;
  hasFreeTier: boolean;
  websiteUrl: string;
}
