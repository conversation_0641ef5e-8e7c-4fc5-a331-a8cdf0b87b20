- dialog:
  - heading "Codebase Context" [level=3]
  - paragraph:
    - text: Select the files to use as context.
    - img
  - textbox "src/**/*.tsx"
  - button "Add"
  - text: /src\/\*\*\/\*\.ts 4 files, ~\d+ tokens/
  - button:
    - img
  - text: /src\/sub\/\*\* 2 files, ~\d+ tokens/
  - button:
    - img
  - heading "Smart Context Auto-includes" [level=3]
  - paragraph:
    - text: These files will always be included in the context.
    - img
  - textbox "src/**/*.config.ts"
  - button "Add"
  - text: /a\.ts 1 files, ~\d+ tokens/
  - button:
    - img
  - text: /manual\/\*\* 2 files, ~\d+ tokens/
  - button:
    - img