"use client"

import React, { useState } from 'react';
import { apiClient } from '@/lib/dyad-client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';

export default function DyadTest() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testConnection = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const health = await apiClient.healthCheck();
      setResult(health);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Connection failed');
    } finally {
      setIsLoading(false);
    }
  };

  const testApps = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const apps = await apiClient.listApps();
      setResult({ apps, count: apps.length });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch apps');
    } finally {
      setIsLoading(false);
    }
  };

  const testModels = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const models = await apiClient.getAllLanguageModels();
      setResult({ models: models.slice(0, 5), total: models.length });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch models');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="bg-[#0a0a0a] border-[#1a1a1a]">
      <CardHeader>
        <CardTitle className="text-white">Dyad Integration Test</CardTitle>
        <CardDescription className="text-[#666]">
          Test the connection and functionality with Dyad backend
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button
            onClick={testConnection}
            disabled={isLoading}
            size="sm"
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isLoading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : null}
            Test Health
          </Button>
          <Button
            onClick={testApps}
            disabled={isLoading}
            size="sm"
            variant="outline"
            className="bg-[#1a1a1a] border-[#333] text-white hover:bg-[#2a2a2a]"
          >
            {isLoading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : null}
            Test Apps
          </Button>
          <Button
            onClick={testModels}
            disabled={isLoading}
            size="sm"
            variant="outline"
            className="bg-[#1a1a1a] border-[#333] text-white hover:bg-[#2a2a2a]"
          >
            {isLoading ? <Loader2 className="w-4 h-4 mr-2 animate-spin" /> : null}
            Test Models
          </Button>
        </div>

        {error && (
          <div className="flex items-center gap-2 p-3 bg-red-900/20 border border-red-800 rounded-lg">
            <XCircle className="w-4 h-4 text-red-500" />
            <span className="text-red-400 text-sm">{error}</span>
          </div>
        )}

        {result && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              <Badge variant="secondary" className="bg-green-900/20 text-green-400 border-green-800">
                Success
              </Badge>
            </div>
            <div className="bg-[#111111] border border-[#1a1a1a] rounded-lg p-3">
              <pre className="text-xs text-[#e5e5e5] overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
