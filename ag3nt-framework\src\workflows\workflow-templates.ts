/**
 * AG3NT Framework - Workflow Templates
 * 
 * Comprehensive collection of workflow templates for different development scenarios.
 * Templates can be customized and instantiated for specific projects.
 */

import { WorkflowDefinition } from "../agents/workflow-agent"

export interface WorkflowTemplate {
  templateId: string
  name: string
  description: string
  category: 'development' | 'maintenance' | 'emergency' | 'analysis' | 'deployment'
  complexity: 'simple' | 'moderate' | 'complex' | 'enterprise'
  estimatedDuration: number
  requiredAgents: string[]
  optionalAgents: string[]
  parameters: TemplateParameter[]
  workflow: WorkflowDefinition
  usageExamples: UsageExample[]
}

export interface TemplateParameter {
  name: string
  type: 'string' | 'number' | 'boolean' | 'array' | 'object'
  description: string
  required: boolean
  defaultValue?: any
  validation?: ParameterValidation
}

export interface ParameterValidation {
  pattern?: string
  minLength?: number
  maxLength?: number
  min?: number
  max?: number
  allowedValues?: any[]
}

export interface UsageExample {
  title: string
  description: string
  parameters: Record<string, any>
  expectedOutcome: string
}

/**
 * Complete Web Application Development Template
 */
export const WebApplicationTemplate: WorkflowTemplate = {
  templateId: 'web-application-development',
  name: 'Complete Web Application Development',
  description: 'End-to-end development of a modern web application with frontend, backend, testing, and deployment',
  category: 'development',
  complexity: 'complex',
  estimatedDuration: 14400000, // 4 hours
  requiredAgents: ['planning', 'context-engine', 'task-planner', 'frontend-coder', 'backend-coder', 'tester', 'reviewer', 'devops'],
  optionalAgents: ['security', 'documentation', 'analytics'],
  parameters: [
    {
      name: 'projectName',
      type: 'string',
      description: 'Name of the web application project',
      required: true,
      validation: { minLength: 3, maxLength: 50, pattern: '^[a-zA-Z0-9-_]+$' }
    },
    {
      name: 'projectDescription',
      type: 'string',
      description: 'Detailed description of the project requirements',
      required: true,
      validation: { minLength: 10, maxLength: 1000 }
    },
    {
      name: 'frontendFramework',
      type: 'string',
      description: 'Frontend framework to use',
      required: true,
      defaultValue: 'react',
      validation: { allowedValues: ['react', 'vue', 'angular', 'svelte'] }
    },
    {
      name: 'backendFramework',
      type: 'string',
      description: 'Backend framework to use',
      required: true,
      defaultValue: 'express',
      validation: { allowedValues: ['express', 'fastify', 'nestjs', 'koa'] }
    },
    {
      name: 'database',
      type: 'string',
      description: 'Database technology to use',
      required: true,
      defaultValue: 'postgresql',
      validation: { allowedValues: ['postgresql', 'mysql', 'mongodb', 'sqlite'] }
    },
    {
      name: 'features',
      type: 'array',
      description: 'List of features to implement',
      required: true
    },
    {
      name: 'deploymentTarget',
      type: 'string',
      description: 'Deployment target platform',
      required: false,
      defaultValue: 'vercel',
      validation: { allowedValues: ['vercel', 'netlify', 'aws', 'docker', 'heroku'] }
    }
  ],
  workflow: {
    workflowId: 'web-app-development',
    name: 'Web Application Development',
    description: 'Complete web application development workflow',
    version: '1.0.0',
    type: 'hybrid',
    steps: [
      {
        stepId: 'project-planning',
        name: 'Project Planning',
        description: 'Comprehensive project planning and architecture design',
        type: 'agent_task',
        agentType: 'planning',
        coordination: {
          pattern: 'consensus_decision',
          stakeholders: ['planning', 'frontend-coder', 'backend-coder'],
          votingTimeout: 300000
        },
        input: {
          required: [
            { name: 'projectName', type: 'string', description: 'Project name' },
            { name: 'projectDescription', type: 'string', description: 'Project description' },
            { name: 'features', type: 'array', description: 'Required features' }
          ]
        },
        output: {
          produces: [
            { name: 'project_plan', type: 'object', description: 'Comprehensive project plan' },
            { name: 'architecture_design', type: 'object', description: 'System architecture design' },
            { name: 'technology_stack', type: 'object', description: 'Selected technology stack' }
          ]
        }
      },
      {
        stepId: 'context-analysis',
        name: 'Context Analysis',
        description: 'Deep analysis of project context and requirements',
        type: 'agent_task',
        agentType: 'context-engine',
        coordination: {
          pattern: 'workflow_handoff',
          enableStateValidation: true,
          enableCheckpoints: true
        },
        dependencies: ['project-planning'],
        input: {
          sources: [
            { type: 'step_output', source: 'project-planning', mapping: { 'project_plan': 'plan' } }
          ]
        },
        output: {
          produces: [
            { name: 'context_analysis', type: 'object', description: 'Detailed context analysis' },
            { name: 'requirements_specification', type: 'object', description: 'Technical requirements' }
          ]
        }
      },
      {
        stepId: 'task-breakdown',
        name: 'Task Breakdown',
        description: 'Break down project into detailed implementation tasks',
        type: 'agent_task',
        agentType: 'task-planner',
        coordination: {
          pattern: 'hierarchical_delegation',
          delegationType: 'peer',
          enableLoadBalancing: true
        },
        dependencies: ['context-analysis'],
        input: {
          sources: [
            { type: 'step_output', source: 'context-analysis', mapping: { 'requirements_specification': 'requirements' } }
          ]
        },
        output: {
          produces: [
            { name: 'frontend_tasks', type: 'array', description: 'Frontend implementation tasks' },
            { name: 'backend_tasks', type: 'array', description: 'Backend implementation tasks' },
            { name: 'integration_tasks', type: 'array', description: 'Integration tasks' }
          ]
        }
      },
      {
        stepId: 'parallel-development',
        name: 'Parallel Development',
        description: 'Parallel frontend and backend development',
        type: 'parallel',
        coordination: {
          pattern: 'hierarchical_delegation',
          delegationType: 'capability_based',
          enableLoadBalancing: true
        },
        dependencies: ['task-breakdown'],
        subSteps: [
          {
            stepId: 'frontend-development',
            name: 'Frontend Development',
            description: 'Develop frontend application',
            type: 'agent_task',
            agentType: 'frontend-coder',
            input: {
              sources: [
                { type: 'step_output', source: 'task-breakdown', mapping: { 'frontend_tasks': 'tasks' } },
                { type: 'parameter', source: 'frontendFramework', mapping: { 'frontendFramework': 'framework' } }
              ]
            },
            output: {
              produces: [
                { name: 'frontend_code', type: 'object', description: 'Frontend application code' },
                { name: 'frontend_components', type: 'array', description: 'Reusable components' },
                { name: 'frontend_tests', type: 'array', description: 'Frontend test suite' }
              ]
            }
          },
          {
            stepId: 'backend-development',
            name: 'Backend Development',
            description: 'Develop backend API and services',
            type: 'agent_task',
            agentType: 'backend-coder',
            input: {
              sources: [
                { type: 'step_output', source: 'task-breakdown', mapping: { 'backend_tasks': 'tasks' } },
                { type: 'parameter', source: 'backendFramework', mapping: { 'backendFramework': 'framework' } },
                { type: 'parameter', source: 'database', mapping: { 'database': 'database' } }
              ]
            },
            output: {
              produces: [
                { name: 'backend_code', type: 'object', description: 'Backend application code' },
                { name: 'api_endpoints', type: 'array', description: 'API endpoint definitions' },
                { name: 'database_schema', type: 'object', description: 'Database schema' },
                { name: 'backend_tests', type: 'array', description: 'Backend test suite' }
              ]
            }
          }
        ]
      },
      {
        stepId: 'integration',
        name: 'System Integration',
        description: 'Integrate frontend and backend components',
        type: 'agent_task',
        agentType: 'executor',
        coordination: {
          pattern: 'workflow_handoff',
          enableStateValidation: true,
          enableRollback: true
        },
        dependencies: ['parallel-development'],
        input: {
          sources: [
            { type: 'step_output', source: 'frontend-development', mapping: { 'frontend_code': 'frontend' } },
            { type: 'step_output', source: 'backend-development', mapping: { 'backend_code': 'backend' } }
          ]
        },
        output: {
          produces: [
            { name: 'integrated_application', type: 'object', description: 'Fully integrated application' },
            { name: 'integration_tests', type: 'array', description: 'Integration test suite' }
          ]
        }
      },
      {
        stepId: 'comprehensive-testing',
        name: 'Comprehensive Testing',
        description: 'Full testing suite including unit, integration, and e2e tests',
        type: 'agent_task',
        agentType: 'tester',
        coordination: {
          pattern: 'workflow_handoff',
          enableRollback: true,
          rollbackTriggers: ['test_failure', 'quality_threshold']
        },
        dependencies: ['integration'],
        input: {
          sources: [
            { type: 'step_output', source: 'integration', mapping: { 'integrated_application': 'application' } }
          ]
        },
        output: {
          produces: [
            { name: 'test_results', type: 'object', description: 'Comprehensive test results' },
            { name: 'coverage_report', type: 'object', description: 'Code coverage analysis' },
            { name: 'performance_metrics', type: 'object', description: 'Performance test results' }
          ]
        }
      },
      {
        stepId: 'code-review',
        name: 'Code Review and Quality Assurance',
        description: 'Comprehensive code review and quality assessment',
        type: 'agent_task',
        agentType: 'reviewer',
        coordination: {
          pattern: 'consensus_decision',
          stakeholders: ['reviewer', 'frontend-coder', 'backend-coder'],
          requiresApproval: true
        },
        dependencies: ['comprehensive-testing'],
        input: {
          sources: [
            { type: 'step_output', source: 'integration', mapping: { 'integrated_application': 'code' } },
            { type: 'step_output', source: 'comprehensive-testing', mapping: { 'test_results': 'tests' } }
          ]
        },
        output: {
          produces: [
            { name: 'review_results', type: 'object', description: 'Code review results' },
            { name: 'quality_score', type: 'number', description: 'Overall quality score' },
            { name: 'approval_status', type: 'boolean', description: 'Review approval status' }
          ]
        }
      },
      {
        stepId: 'deployment',
        name: 'Application Deployment',
        description: 'Deploy application to target platform',
        type: 'agent_task',
        agentType: 'devops',
        coordination: {
          pattern: 'hierarchical_delegation',
          authorityLevel: 8,
          requiresConfirmation: true
        },
        dependencies: ['code-review'],
        conditions: [
          { field: 'approval_status', operator: 'equals', value: true }
        ],
        input: {
          sources: [
            { type: 'step_output', source: 'integration', mapping: { 'integrated_application': 'application' } },
            { type: 'parameter', source: 'deploymentTarget', mapping: { 'deploymentTarget': 'target' } }
          ]
        },
        output: {
          produces: [
            { name: 'deployment_result', type: 'object', description: 'Deployment status and URLs' },
            { name: 'monitoring_setup', type: 'object', description: 'Monitoring and alerting' },
            { name: 'deployment_documentation', type: 'object', description: 'Deployment documentation' }
          ]
        }
      }
    ],
    errorHandling: {
      strategy: 'rollback_and_retry',
      maxRetries: 3,
      rollbackPoints: ['project-planning', 'task-breakdown', 'integration', 'comprehensive-testing'],
      escalationPath: ['human_intervention', 'emergency_workflow']
    },
    monitoring: {
      enableRealTimeTracking: true,
      enablePerformanceMetrics: true,
      enableQualityGates: true,
      alertThresholds: {
        executionTime: 14400000, // 4 hours
        errorRate: 0.05,
        qualityScore: 0.85
      }
    }
  },
  usageExamples: [
    {
      title: 'E-commerce Platform',
      description: 'Build a complete e-commerce platform with product catalog, shopping cart, and payment processing',
      parameters: {
        projectName: 'ecommerce-platform',
        projectDescription: 'Modern e-commerce platform with React frontend, Node.js backend, and PostgreSQL database',
        frontendFramework: 'react',
        backendFramework: 'express',
        database: 'postgresql',
        features: ['product-catalog', 'shopping-cart', 'user-authentication', 'payment-processing', 'order-management'],
        deploymentTarget: 'vercel'
      },
      expectedOutcome: 'Fully functional e-commerce platform deployed to Vercel with comprehensive testing and monitoring'
    },
    {
      title: 'Task Management App',
      description: 'Build a collaborative task management application',
      parameters: {
        projectName: 'task-manager',
        projectDescription: 'Collaborative task management app with real-time updates and team collaboration features',
        frontendFramework: 'vue',
        backendFramework: 'nestjs',
        database: 'mongodb',
        features: ['task-creation', 'team-collaboration', 'real-time-updates', 'file-attachments', 'notifications'],
        deploymentTarget: 'aws'
      },
      expectedOutcome: 'Production-ready task management application with real-time collaboration features'
    }
  ]
}

/**
 * Microservice Development Template
 */
export const MicroserviceTemplate: WorkflowTemplate = {
  templateId: 'microservice-development',
  name: 'Microservice Development',
  description: 'Development of a single microservice with API, testing, and containerization',
  category: 'development',
  complexity: 'moderate',
  estimatedDuration: 7200000, // 2 hours
  requiredAgents: ['context-engine', 'task-planner', 'backend-coder', 'tester', 'reviewer', 'devops'],
  optionalAgents: ['security', 'documentation'],
  parameters: [
    {
      name: 'serviceName',
      type: 'string',
      description: 'Name of the microservice',
      required: true,
      validation: { minLength: 3, maxLength: 30, pattern: '^[a-zA-Z0-9-]+$' }
    },
    {
      name: 'serviceDescription',
      type: 'string',
      description: 'Description of the microservice functionality',
      required: true,
      validation: { minLength: 10, maxLength: 500 }
    },
    {
      name: 'apiEndpoints',
      type: 'array',
      description: 'List of API endpoints to implement',
      required: true
    },
    {
      name: 'framework',
      type: 'string',
      description: 'Backend framework to use',
      required: true,
      defaultValue: 'express',
      validation: { allowedValues: ['express', 'fastify', 'nestjs', 'koa', 'spring-boot'] }
    },
    {
      name: 'database',
      type: 'string',
      description: 'Database technology',
      required: false,
      defaultValue: 'postgresql',
      validation: { allowedValues: ['postgresql', 'mysql', 'mongodb', 'redis', 'none'] }
    }
  ],
  workflow: {
    workflowId: 'microservice-development',
    name: 'Microservice Development',
    description: 'Complete microservice development workflow',
    version: '1.0.0',
    type: 'sequential',
    steps: [
      {
        stepId: 'service-design',
        name: 'Service Design',
        description: 'Design microservice architecture and API specification',
        type: 'agent_task',
        agentType: 'context-engine',
        coordination: {
          pattern: 'workflow_handoff',
          enableStateValidation: true
        },
        input: {
          required: [
            { name: 'serviceName', type: 'string', description: 'Service name' },
            { name: 'serviceDescription', type: 'string', description: 'Service description' },
            { name: 'apiEndpoints', type: 'array', description: 'API endpoints' }
          ]
        },
        output: {
          produces: [
            { name: 'service_specification', type: 'object', description: 'Service specification' },
            { name: 'api_design', type: 'object', description: 'API design and documentation' }
          ]
        }
      },
      {
        stepId: 'implementation',
        name: 'Service Implementation',
        description: 'Implement the microservice',
        type: 'agent_task',
        agentType: 'backend-coder',
        coordination: {
          pattern: 'hierarchical_delegation',
          delegationType: 'capability_based'
        },
        dependencies: ['service-design'],
        input: {
          sources: [
            { type: 'step_output', source: 'service-design', mapping: { 'service_specification': 'spec' } },
            { type: 'parameter', source: 'framework', mapping: { 'framework': 'framework' } }
          ]
        },
        output: {
          produces: [
            { name: 'service_code', type: 'object', description: 'Microservice implementation' },
            { name: 'unit_tests', type: 'array', description: 'Unit test suite' }
          ]
        }
      },
      {
        stepId: 'testing',
        name: 'Service Testing',
        description: 'Comprehensive testing of the microservice',
        type: 'agent_task',
        agentType: 'tester',
        coordination: {
          pattern: 'workflow_handoff',
          enableRollback: true
        },
        dependencies: ['implementation'],
        input: {
          sources: [
            { type: 'step_output', source: 'implementation', mapping: { 'service_code': 'service' } }
          ]
        },
        output: {
          produces: [
            { name: 'test_results', type: 'object', description: 'Test results' },
            { name: 'api_tests', type: 'array', description: 'API test suite' }
          ]
        }
      },
      {
        stepId: 'containerization',
        name: 'Containerization',
        description: 'Create Docker container for the microservice',
        type: 'agent_task',
        agentType: 'devops',
        coordination: {
          pattern: 'hierarchical_delegation',
          authorityLevel: 7
        },
        dependencies: ['testing'],
        input: {
          sources: [
            { type: 'step_output', source: 'implementation', mapping: { 'service_code': 'service' } }
          ]
        },
        output: {
          produces: [
            { name: 'docker_image', type: 'object', description: 'Docker container image' },
            { name: 'deployment_config', type: 'object', description: 'Deployment configuration' }
          ]
        }
      }
    ],
    errorHandling: {
      strategy: 'retry_and_escalate',
      maxRetries: 2,
      escalationPath: ['team_lead', 'human_intervention']
    }
  },
  usageExamples: [
    {
      title: 'User Authentication Service',
      description: 'Microservice for user authentication and authorization',
      parameters: {
        serviceName: 'auth-service',
        serviceDescription: 'User authentication and authorization microservice with JWT tokens',
        apiEndpoints: ['/login', '/register', '/verify', '/refresh'],
        framework: 'nestjs',
        database: 'postgresql'
      },
      expectedOutcome: 'Containerized authentication microservice with comprehensive API testing'
    }
  ]
}

/**
 * Emergency Response Workflow Template
 */
export const EmergencyResponseTemplate: WorkflowTemplate = {
  templateId: 'emergency-response',
  name: 'Emergency Response',
  description: 'Rapid response to critical system issues and security incidents',
  category: 'emergency',
  complexity: 'complex',
  estimatedDuration: 3600000, // 1 hour
  requiredAgents: ['security', 'devops', 'context-engine', 'maintenance'],
  optionalAgents: ['tester', 'reviewer'],
  parameters: [
    {
      name: 'incidentType',
      type: 'string',
      description: 'Type of emergency incident',
      required: true,
      validation: { allowedValues: ['security_breach', 'system_outage', 'data_corruption', 'performance_degradation', 'critical_bug'] }
    },
    {
      name: 'severity',
      type: 'string',
      description: 'Incident severity level',
      required: true,
      validation: { allowedValues: ['critical', 'high', 'medium'] }
    },
    {
      name: 'affectedSystems',
      type: 'array',
      description: 'List of affected systems or components',
      required: true
    },
    {
      name: 'incidentDescription',
      type: 'string',
      description: 'Detailed description of the incident',
      required: true,
      validation: { minLength: 20, maxLength: 1000 }
    }
  ],
  workflow: {
    workflowId: 'emergency-response',
    name: 'Emergency Response',
    description: 'Rapid emergency response workflow',
    version: '1.0.0',
    type: 'sequential',
    steps: [
      {
        stepId: 'incident-assessment',
        name: 'Incident Assessment',
        description: 'Rapid assessment of the incident scope and impact',
        type: 'agent_task',
        agentType: 'security',
        coordination: {
          pattern: 'hierarchical_delegation',
          authorityLevel: 9,
          delegationType: 'emergency'
        },
        input: {
          required: [
            { name: 'incidentType', type: 'string', description: 'Incident type' },
            { name: 'severity', type: 'string', description: 'Severity level' },
            { name: 'affectedSystems', type: 'array', description: 'Affected systems' }
          ]
        },
        output: {
          produces: [
            { name: 'impact_assessment', type: 'object', description: 'Impact assessment' },
            { name: 'containment_strategy', type: 'object', description: 'Containment strategy' }
          ]
        }
      },
      {
        stepId: 'immediate-containment',
        name: 'Immediate Containment',
        description: 'Contain the incident to prevent further damage',
        type: 'agent_task',
        agentType: 'devops',
        coordination: {
          pattern: 'hierarchical_delegation',
          authorityLevel: 8,
          delegationType: 'emergency'
        },
        dependencies: ['incident-assessment'],
        input: {
          sources: [
            { type: 'step_output', source: 'incident-assessment', mapping: { 'containment_strategy': 'strategy' } }
          ]
        },
        output: {
          produces: [
            { name: 'containment_actions', type: 'array', description: 'Actions taken for containment' },
            { name: 'system_status', type: 'object', description: 'Current system status' }
          ]
        }
      },
      {
        stepId: 'root-cause-analysis',
        name: 'Root Cause Analysis',
        description: 'Identify the root cause of the incident',
        type: 'agent_task',
        agentType: 'context-engine',
        coordination: {
          pattern: 'workflow_handoff',
          enableStateValidation: true
        },
        dependencies: ['immediate-containment'],
        input: {
          sources: [
            { type: 'step_output', source: 'incident-assessment', mapping: { 'impact_assessment': 'assessment' } },
            { type: 'step_output', source: 'immediate-containment', mapping: { 'system_status': 'status' } }
          ]
        },
        output: {
          produces: [
            { name: 'root_cause', type: 'object', description: 'Root cause analysis' },
            { name: 'fix_recommendations', type: 'array', description: 'Recommended fixes' }
          ]
        }
      },
      {
        stepId: 'emergency-fix',
        name: 'Emergency Fix Implementation',
        description: 'Implement emergency fix to restore service',
        type: 'agent_task',
        agentType: 'maintenance',
        coordination: {
          pattern: 'consensus_decision',
          stakeholders: ['maintenance', 'devops', 'security'],
          votingTimeout: 180000 // 3 minutes for emergency
        },
        dependencies: ['root-cause-analysis'],
        input: {
          sources: [
            { type: 'step_output', source: 'root-cause-analysis', mapping: { 'fix_recommendations': 'fixes' } }
          ]
        },
        output: {
          produces: [
            { name: 'emergency_fix', type: 'object', description: 'Emergency fix implementation' },
            { name: 'service_restoration', type: 'object', description: 'Service restoration status' }
          ]
        }
      },
      {
        stepId: 'verification',
        name: 'Fix Verification',
        description: 'Verify that the emergency fix resolves the issue',
        type: 'agent_task',
        agentType: 'devops',
        coordination: {
          pattern: 'workflow_handoff',
          enableRollback: true,
          rollbackTriggers: ['fix_ineffective', 'new_issues_detected']
        },
        dependencies: ['emergency-fix'],
        input: {
          sources: [
            { type: 'step_output', source: 'emergency-fix', mapping: { 'emergency_fix': 'fix' } }
          ]
        },
        output: {
          produces: [
            { name: 'verification_results', type: 'object', description: 'Fix verification results' },
            { name: 'monitoring_status', type: 'object', description: 'System monitoring status' }
          ]
        }
      }
    ],
    errorHandling: {
      strategy: 'escalate_immediately',
      maxRetries: 1,
      escalationPath: ['senior_engineer', 'incident_commander', 'executive_team']
    },
    monitoring: {
      enableRealTimeTracking: true,
      enablePerformanceMetrics: true,
      alertThresholds: {
        executionTime: 3600000, // 1 hour max
        errorRate: 0.01 // Very low tolerance for errors
      }
    }
  },
  usageExamples: [
    {
      title: 'Security Breach Response',
      description: 'Respond to a detected security breach',
      parameters: {
        incidentType: 'security_breach',
        severity: 'critical',
        affectedSystems: ['user-database', 'authentication-service'],
        incidentDescription: 'Unauthorized access detected in user database with potential data exfiltration'
      },
      expectedOutcome: 'Security breach contained, systems secured, and service restored within 1 hour'
    }
  ]
}

/**
 * System Maintenance Workflow Template
 */
export const MaintenanceTemplate: WorkflowTemplate = {
  templateId: 'system-maintenance',
  name: 'System Maintenance',
  description: 'Scheduled system maintenance including updates, optimizations, and health checks',
  category: 'maintenance',
  complexity: 'moderate',
  estimatedDuration: 5400000, // 1.5 hours
  requiredAgents: ['maintenance', 'security', 'devops', 'tester'],
  optionalAgents: ['analytics'],
  parameters: [
    {
      name: 'maintenanceType',
      type: 'string',
      description: 'Type of maintenance to perform',
      required: true,
      validation: { allowedValues: ['security_updates', 'dependency_updates', 'performance_optimization', 'database_maintenance', 'comprehensive'] }
    },
    {
      name: 'systems',
      type: 'array',
      description: 'Systems to maintain',
      required: true
    },
    {
      name: 'maintenanceWindow',
      type: 'string',
      description: 'Maintenance window duration',
      required: true,
      validation: { allowedValues: ['1hour', '2hours', '4hours', '8hours'] }
    }
  ],
  workflow: {
    workflowId: 'system-maintenance',
    name: 'System Maintenance',
    description: 'Comprehensive system maintenance workflow',
    version: '1.0.0',
    type: 'sequential',
    steps: [
      {
        stepId: 'pre-maintenance-check',
        name: 'Pre-Maintenance Health Check',
        description: 'Comprehensive system health check before maintenance',
        type: 'agent_task',
        agentType: 'maintenance',
        coordination: {
          pattern: 'workflow_handoff',
          enableCheckpoints: true
        },
        input: {
          required: [
            { name: 'systems', type: 'array', description: 'Systems to check' }
          ]
        },
        output: {
          produces: [
            { name: 'health_report', type: 'object', description: 'System health report' },
            { name: 'maintenance_plan', type: 'object', description: 'Detailed maintenance plan' }
          ]
        }
      },
      {
        stepId: 'security-updates',
        name: 'Security Updates',
        description: 'Apply security patches and updates',
        type: 'agent_task',
        agentType: 'security',
        coordination: {
          pattern: 'hierarchical_delegation',
          authorityLevel: 7,
          requiresConfirmation: true
        },
        dependencies: ['pre-maintenance-check'],
        input: {
          sources: [
            { type: 'step_output', source: 'pre-maintenance-check', mapping: { 'maintenance_plan': 'plan' } }
          ]
        },
        output: {
          produces: [
            { name: 'security_updates', type: 'array', description: 'Applied security updates' },
            { name: 'security_status', type: 'object', description: 'Updated security status' }
          ]
        }
      },
      {
        stepId: 'system-optimization',
        name: 'System Optimization',
        description: 'Optimize system performance and resource usage',
        type: 'agent_task',
        agentType: 'maintenance',
        coordination: {
          pattern: 'workflow_handoff',
          enableStateValidation: true
        },
        dependencies: ['security-updates'],
        input: {
          sources: [
            { type: 'step_output', source: 'pre-maintenance-check', mapping: { 'health_report': 'baseline' } }
          ]
        },
        output: {
          produces: [
            { name: 'optimization_results', type: 'object', description: 'System optimization results' },
            { name: 'performance_improvements', type: 'array', description: 'Performance improvements made' }
          ]
        }
      },
      {
        stepId: 'post-maintenance-testing',
        name: 'Post-Maintenance Testing',
        description: 'Comprehensive testing after maintenance',
        type: 'agent_task',
        agentType: 'tester',
        coordination: {
          pattern: 'workflow_handoff',
          enableRollback: true,
          rollbackTriggers: ['test_failure', 'performance_regression']
        },
        dependencies: ['system-optimization'],
        input: {
          sources: [
            { type: 'step_output', source: 'system-optimization', mapping: { 'optimization_results': 'changes' } }
          ]
        },
        output: {
          produces: [
            { name: 'test_results', type: 'object', description: 'Post-maintenance test results' },
            { name: 'system_validation', type: 'object', description: 'System validation report' }
          ]
        }
      }
    ],
    errorHandling: {
      strategy: 'rollback_and_retry',
      maxRetries: 2,
      rollbackPoints: ['pre-maintenance-check', 'security-updates'],
      escalationPath: ['maintenance_lead', 'operations_manager']
    }
  },
  usageExamples: [
    {
      title: 'Monthly Security Maintenance',
      description: 'Monthly security updates and system optimization',
      parameters: {
        maintenanceType: 'comprehensive',
        systems: ['web-servers', 'databases', 'load-balancers'],
        maintenanceWindow: '2hours'
      },
      expectedOutcome: 'All systems updated with latest security patches and optimized for performance'
    }
  ]
}

// Export all templates
export const WorkflowTemplates = {
  WebApplicationTemplate,
  MicroserviceTemplate,
  EmergencyResponseTemplate,
  MaintenanceTemplate
}

export default WorkflowTemplates
