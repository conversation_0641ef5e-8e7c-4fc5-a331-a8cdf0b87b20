{"version": 3, "file": "failover-manager.d.ts", "sourceRoot": "", "sources": ["failover-manager.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAA;AACrC,OAAO,EAAiB,qBAAqB,EAAE,MAAM,2BAA2B,CAAA;AAChF,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAA;AAE9C,MAAM,WAAW,cAAc;IAC7B,kBAAkB,EAAE,OAAO,CAAA;IAC3B,sBAAsB,EAAE,OAAO,CAAA;IAC/B,wBAAwB,EAAE,MAAM,CAAA;IAChC,mBAAmB,EAAE,MAAM,CAAA;IAC3B,eAAe,EAAE,MAAM,CAAA;IACvB,gBAAgB,EAAE,MAAM,CAAA;IACxB,qBAAqB,EAAE,OAAO,CAAA;IAC9B,mBAAmB,EAAE,OAAO,CAAA;CAC7B;AAED,MAAM,WAAW,aAAa;IAC5B,OAAO,EAAE,MAAM,CAAA;IACf,IAAI,EAAE,eAAe,GAAG,gBAAgB,GAAG,oBAAoB,GAAG,oBAAoB,CAAA;IACtF,SAAS,EAAE,MAAM,CAAA;IACjB,YAAY,EAAE,MAAM,CAAA;IACpB,WAAW,CAAC,EAAE,MAAM,CAAA;IACpB,MAAM,EAAE,MAAM,CAAA;IACd,MAAM,EAAE,cAAc,CAAA;IACtB,QAAQ,EAAE,gBAAgB,CAAA;CAC3B;AAED,MAAM,WAAW,cAAc;IAC7B,gBAAgB,EAAE,MAAM,CAAA;IACxB,iBAAiB,EAAE,MAAM,CAAA;IACzB,QAAQ,EAAE,OAAO,CAAA;IACjB,iBAAiB,EAAE,MAAM,GAAG,SAAS,GAAG,UAAU,GAAG,QAAQ,CAAA;CAC9D;AAED,MAAM,WAAW,gBAAgB;IAC/B,QAAQ,EAAE,WAAW,GAAG,UAAU,GAAG,QAAQ,CAAA;IAC7C,aAAa,EAAE,MAAM,CAAA;IACrB,oBAAoB,EAAE,OAAO,CAAA;IAC7B,YAAY,EAAE,MAAM,EAAE,CAAA;CACvB;AAED,MAAM,WAAW,WAAW;IAC1B,OAAO,EAAE,MAAM,CAAA;IACf,YAAY,EAAE,MAAM,CAAA;IACpB,cAAc,EAAE,MAAM,GAAG,MAAM,GAAG,KAAK,CAAA;IACvC,YAAY,EAAE,MAAM,CAAA;IACpB,eAAe,EAAE,MAAM,CAAA;IACvB,cAAc,EAAE,MAAM,CAAA;CACvB;AAED,MAAM,WAAW,YAAY;IAC3B,MAAM,EAAE,MAAM,CAAA;IACd,YAAY,EAAE,MAAM,CAAA;IACpB,YAAY,EAAE,WAAW,EAAE,CAAA;IAC3B,gBAAgB,EAAE,eAAe,EAAE,CAAA;IACnC,aAAa,EAAE,YAAY,EAAE,CAAA;IAC7B,aAAa,EAAE,YAAY,EAAE,CAAA;IAC7B,YAAY,EAAE,YAAY,CAAA;CAC3B;AAED,MAAM,WAAW,eAAe;IAC9B,IAAI,EAAE,sBAAsB,GAAG,kBAAkB,GAAG,sBAAsB,GAAG,QAAQ,CAAA;IACrF,SAAS,EAAE,GAAG,CAAA;IACd,mBAAmB,EAAE,MAAM,CAAA;IAC3B,UAAU,EAAE,MAAM,CAAA;CACnB;AAED,MAAM,WAAW,YAAY;IAC3B,MAAM,EAAE,MAAM,CAAA;IACd,KAAK,EAAE,MAAM,CAAA;IACb,MAAM,EAAE,iBAAiB,GAAG,gBAAgB,GAAG,kBAAkB,GAAG,qBAAqB,CAAA;IACzF,UAAU,EAAE,GAAG,CAAA;IACf,OAAO,EAAE,MAAM,CAAA;IACf,iBAAiB,EAAE,OAAO,CAAA;CAC3B;AAED,MAAM,WAAW,YAAY;IAC3B,MAAM,EAAE,MAAM,CAAA;IACd,KAAK,EAAE,MAAM,CAAA;IACb,MAAM,EAAE,iBAAiB,GAAG,qBAAqB,GAAG,uBAAuB,CAAA;IAC3E,UAAU,EAAE,GAAG,CAAA;IACf,OAAO,EAAE,MAAM,CAAA;CAChB;AAED,MAAM,WAAW,YAAY;IAC3B,SAAS,EAAE,OAAO,GAAG,QAAQ,GAAG,SAAS,CAAA;IACzC,QAAQ,EAAE,MAAM,CAAA;IAChB,QAAQ,CAAC,EAAE,MAAM,CAAA;IACjB,WAAW,EAAE,UAAU,EAAE,CAAA;CAC1B;AAED,MAAM,WAAW,UAAU;IACzB,MAAM,EAAE,MAAM,CAAA;IACd,SAAS,EAAE,MAAM,CAAA;IACjB,OAAO,EAAE,OAAO,CAAA;IAChB,YAAY,EAAE,MAAM,CAAA;IACpB,MAAM,EAAE,MAAM,EAAE,CAAA;IAChB,eAAe,EAAE,MAAM,EAAE,CAAA;CAC1B;AAED,MAAM,WAAW,eAAe;IAC9B,cAAc,EAAE,MAAM,CAAA;IACtB,mBAAmB,EAAE,MAAM,CAAA;IAC3B,mBAAmB,EAAE,MAAM,CAAA;IAC3B,mBAAmB,EAAE,MAAM,CAAA;IAC3B,iBAAiB,EAAE,MAAM,CAAA;IACzB,eAAe,EAAE,MAAM,CAAA;IACvB,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,EAAE,MAAM,CAAA;CACb;AAED;;GAEG;AACH,qBAAa,eAAgB,SAAQ,YAAY;IAC/C,OAAO,CAAC,MAAM,CAAgB;IAC9B,OAAO,CAAC,gBAAgB,CAAuB;IAC/C,OAAO,CAAC,YAAY,CAAc;IAClC,OAAO,CAAC,aAAa,CAAuC;IAC5D,OAAO,CAAC,YAAY,CAAwC;IAC5D,OAAO,CAAC,eAAe,CAAsB;IAC7C,OAAO,CAAC,eAAe,CAAwC;IAC/D,OAAO,CAAC,OAAO,CAAiB;IAChC,OAAO,CAAC,eAAe,CAAC,CAAgB;gBAGtC,gBAAgB,EAAE,qBAAqB,EACvC,YAAY,EAAE,YAAY,EAC1B,MAAM,GAAE,OAAO,CAAC,cAAc,CAAM;IAgCtC;;OAEG;IACH,KAAK,IAAI,IAAI;IAUb;;OAEG;IACG,kBAAkB,CAAC,cAAc,EAAE,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC;IAgCvE;;OAEG;IACG,eAAe,CAAC,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAE,OAAe,GAAG,OAAO,CAAC,aAAa,CAAC;IA6D9G;;OAEG;IACG,mBAAmB,CAAC,cAAc,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IA4ChE;;OAEG;IACG,gBAAgB,CAAC,cAAc,EAAE,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC;IAyEnE;;OAEG;IACH,kBAAkB,IAAI,eAAe;IAIrC;;OAEG;IACH,gBAAgB,IAAI,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC;IAI7C;;OAEG;YACW,gBAAgB;IAyB9B,OAAO,CAAC,qBAAqB;IAuB7B,OAAO,CAAC,mBAAmB;IA6B3B,OAAO,CAAC,mBAAmB;YA0Bb,oBAAoB;YAmBpB,mBAAmB;YA4BnB,mBAAmB;IAsBjC,OAAO,CAAC,qBAAqB;IAM7B,OAAO,CAAC,cAAc;IAetB,OAAO,CAAC,qBAAqB;IAc7B,OAAO,CAAC,aAAa;IAiCrB,OAAO,CAAC,kBAAkB;IAa1B;;OAEG;IACH,QAAQ,IAAI,IAAI;CAYjB;AAED,eAAe,eAAe,CAAA"}