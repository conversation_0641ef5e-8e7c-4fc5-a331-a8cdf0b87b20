{"version": 3, "file": "coordination-pattern-registry.d.ts", "sourceRoot": "", "sources": ["coordination-pattern-registry.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAA;AAErC,MAAM,WAAW,mBAAmB;IAClC,SAAS,EAAE,MAAM,CAAA;IACjB,IAAI,EAAE,MAAM,CAAA;IACZ,WAAW,EAAE,MAAM,CAAA;IACnB,IAAI,EAAE,YAAY,GAAG,WAAW,GAAG,SAAS,GAAG,QAAQ,CAAA;IACvD,QAAQ,EAAE,cAAc,GAAG,cAAc,GAAG,YAAY,GAAG,cAAc,GAAG,UAAU,CAAA;IACtF,aAAa,EAAE,oBAAoB,CAAA;IACnC,aAAa,EAAE,oBAAoB,CAAA;IACnC,OAAO,EAAE,cAAc,CAAA;IACvB,cAAc,EAAE,qBAAqB,CAAA;CACtC;AAED,MAAM,WAAW,oBAAoB;IACnC,SAAS,EAAE,MAAM,EAAE,CAAA;IACnB,UAAU,EAAE,MAAM,EAAE,CAAA;IACpB,SAAS,EAAE;QAAE,GAAG,EAAE,MAAM,CAAC;QAAC,GAAG,EAAE,MAAM,CAAA;KAAE,CAAA;IACvC,UAAU,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAA;IACrC,eAAe,EAAE,OAAO,GAAG,UAAU,GAAG,UAAU,CAAA;IAClD,mBAAmB,EAAE,OAAO,GAAG,UAAU,GAAG,MAAM,GAAG,UAAU,CAAA;IAC/D,aAAa,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAA;CACzC;AAED,MAAM,WAAW,oBAAoB;IACnC,UAAU,EAAE,gBAAgB,EAAE,CAAA;IAC9B,WAAW,EAAE,iBAAiB,EAAE,CAAA;IAChC,aAAa,EAAE,MAAM,EAAE,CAAA;IACvB,YAAY,EAAE,MAAM,EAAE,CAAA;IACtB,gBAAgB,EAAE,MAAM,EAAE,CAAA;CAC3B;AAED,MAAM,WAAW,gBAAgB;IAC/B,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,EAAE,QAAQ,GAAG,SAAS,GAAG,QAAQ,GAAG,MAAM,GAAG,QAAQ,CAAA;IACzD,YAAY,EAAE,GAAG,CAAA;IACjB,KAAK,CAAC,EAAE;QAAE,GAAG,CAAC,EAAE,MAAM,CAAC;QAAC,GAAG,CAAC,EAAE,MAAM,CAAA;KAAE,CAAA;IACtC,OAAO,CAAC,EAAE,MAAM,EAAE,CAAA;IAClB,QAAQ,EAAE,OAAO,CAAA;IACjB,WAAW,EAAE,MAAM,CAAA;CACpB;AAED,MAAM,WAAW,iBAAiB;IAChC,IAAI,EAAE,UAAU,GAAG,MAAM,GAAG,SAAS,GAAG,UAAU,GAAG,YAAY,CAAA;IACjE,SAAS,EAAE,MAAM,CAAA;IACjB,WAAW,EAAE,QAAQ,GAAG,UAAU,GAAG,UAAU,CAAA;IAC/C,OAAO,EAAE,MAAM,CAAA;CAChB;AAED,MAAM,WAAW,cAAc;IAC7B,WAAW,EAAE,MAAM,CAAA;IACnB,oBAAoB,EAAE,MAAM,CAAA;IAC5B,kBAAkB,EAAE,MAAM,CAAA;IAC1B,YAAY,EAAE,MAAM,CAAA;IACpB,WAAW,EAAE,MAAM,CAAA;IACnB,YAAY,EAAE,MAAM,CAAA;IACpB,UAAU,EAAE,MAAM,CAAA;IAClB,QAAQ,EAAE,MAAM,CAAA;CACjB;AAED,MAAM,WAAW,qBAAqB;IACpC,iBAAiB,EAAE,CAAC,MAAM,EAAE,GAAG,KAAK,OAAO,CAAC,GAAG,CAAC,CAAA;IAChD,cAAc,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,OAAO,CAAC,GAAG,CAAC,CAAA;IAC9C,cAAc,EAAE,CAAC,SAAS,EAAE,GAAG,KAAK,OAAO,CAAC,GAAG,CAAC,CAAA;IAChD,YAAY,EAAE,CAAC,QAAQ,EAAE,GAAG,KAAK,OAAO,CAAC,GAAG,CAAC,CAAA;IAC7C,cAAc,EAAE,CAAC,SAAS,EAAE,GAAG,KAAK,OAAO,CAAC,IAAI,CAAC,CAAA;CAClD;AAED,MAAM,WAAW,oBAAoB;IACnC,UAAU,EAAE,MAAM,CAAA;IAClB,IAAI,EAAE,MAAM,CAAA;IACZ,WAAW,EAAE,MAAM,CAAA;IACnB,QAAQ,EAAE,MAAM,EAAE,CAAA;IAClB,iBAAiB,EAAE,yBAAyB,CAAA;IAC5C,eAAe,EAAE,cAAc,EAAE,CAAA;IACjC,WAAW,EAAE,mBAAmB,CAAA;CACjC;AAED,MAAM,WAAW,yBAAyB;IACxC,cAAc,EAAE,aAAa,EAAE,CAAA;IAC/B,eAAe,EAAE,eAAe,CAAA;IAChC,iBAAiB,EAAE,MAAM,CAAA;IACzB,gBAAgB,CAAC,EAAE,MAAM,CAAA;CAC1B;AAED,MAAM,WAAW,aAAa;IAC5B,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,EAAE,aAAa,GAAG,WAAW,GAAG,SAAS,CAAA;IAC7C,MAAM,EAAE,MAAM,CAAA;IACd,MAAM,EAAE,GAAG,EAAE,CAAA;IACb,YAAY,CAAC,EAAE,GAAG,CAAA;CACnB;AAED,MAAM,WAAW,eAAe;IAC9B,cAAc,EAAE,MAAM,CAAA;IACtB,QAAQ,EAAE,MAAM,CAAA;IAChB,eAAe,EAAE,MAAM,CAAA;IACvB,mBAAmB,EAAE,MAAM,CAAA;IAC3B,SAAS,EAAE,MAAM,CAAA;IACjB,oBAAoB,EAAE,MAAM,CAAA;CAC7B;AAED,MAAM,WAAW,cAAc;IAC7B,MAAM,EAAE,MAAM,CAAA;IACd,SAAS,EAAE,MAAM,CAAA;IACjB,MAAM,EAAE,gBAAgB,GAAG,mBAAmB,GAAG,UAAU,GAAG,UAAU,CAAA;IACxE,UAAU,EAAE,GAAG,CAAA;IACf,QAAQ,EAAE,MAAM,CAAA;CACjB;AAED,MAAM,WAAW,mBAAmB;IAClC,YAAY,EAAE,MAAM,CAAA;IACpB,oBAAoB,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IACzC,iBAAiB,EAAE,MAAM,CAAA;IACzB,eAAe,EAAE,MAAM,CAAA;IACvB,YAAY,EAAE,MAAM,CAAA;CACrB;AAED,MAAM,WAAW,qBAAqB;IACpC,SAAS,EAAE,MAAM,CAAA;IACjB,UAAU,EAAE,MAAM,CAAA;IAClB,SAAS,EAAE,MAAM,EAAE,CAAA;IACnB,gBAAgB,EAAE,MAAM,EAAE,CAAA;IAC1B,cAAc,EAAE,MAAM,EAAE,CAAA;IACxB,mBAAmB,EAAE,MAAM,EAAE,CAAA;CAC9B;AAED;;GAEG;AACH,qBAAa,2BAA4B,SAAQ,YAAY;IAC3D,OAAO,CAAC,QAAQ,CAA8C;IAC9D,OAAO,CAAC,UAAU,CAA+C;IACjE,OAAO,CAAC,gBAAgB,CAA2C;IACnE,OAAO,CAAC,cAAc,CAAyB;;IAQ/C;;OAEG;IACH,eAAe,CAAC,OAAO,EAAE,mBAAmB,GAAG,IAAI;IAMnD;;OAEG;IACH,gBAAgB,CAAC,QAAQ,EAAE,oBAAoB,GAAG,IAAI;IAMtD;;OAEG;IACH,gBAAgB,CAAC,OAAO,EAAE,mBAAmB,GAAG,qBAAqB,EAAE;IAsBvE;;OAEG;IACG,cAAc,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,mBAAmB,EAAE,MAAM,GAAE,GAAQ,GAAG,OAAO,CAAC,gBAAgB,CAAC;IA8DlH;;OAEG;IACG,YAAY,CAAC,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC;IAqBjF;;OAEG;IACH,mBAAmB,IAAI,gBAAgB;IAgBvC;;OAEG;IACH,OAAO,CAAC,qBAAqB;IAmC7B,OAAO,CAAC,kBAAkB;IAQ1B,OAAO,CAAC,sBAAsB;IAQ9B,OAAO,CAAC,eAAe;IAQvB,OAAO,CAAC,+BAA+B;IAoBvC,OAAO,CAAC,wBAAwB;IAkBhC,OAAO,CAAC,sBAAsB;IAc9B,OAAO,CAAC,uBAAuB;IAc/B,OAAO,CAAC,oBAAoB;IAe5B,OAAO,CAAC,6BAA6B;IAQrC,OAAO,CAAC,mBAAmB;IAO3B,OAAO,CAAC,6BAA6B;IAWrC,OAAO,CAAC,0BAA0B;IAQlC,OAAO,CAAC,yBAAyB;IAyIjC,OAAO,CAAC,2BAA2B;CA2CpC;AAGD,UAAU,mBAAmB;IAC3B,QAAQ,EAAE,MAAM,CAAA;IAChB,YAAY,EAAE,MAAM,EAAE,CAAA;IACtB,UAAU,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAA;IACrC,eAAe,EAAE,OAAO,GAAG,UAAU,GAAG,UAAU,CAAA;IAClD,mBAAmB,EAAE,OAAO,GAAG,UAAU,GAAG,MAAM,GAAG,UAAU,CAAA;IAC/D,aAAa,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAA;IACxC,mBAAmB,EAAE,GAAG,CAAA;IACxB,YAAY,EAAE,MAAM,EAAE,CAAA;CACvB;AAED,UAAU,gBAAgB;IACxB,WAAW,EAAE,MAAM,CAAA;IACnB,SAAS,EAAE,MAAM,CAAA;IACjB,OAAO,EAAE,mBAAmB,CAAA;IAC5B,MAAM,EAAE,GAAG,CAAA;IACX,MAAM,EAAE,cAAc,GAAG,SAAS,GAAG,WAAW,GAAG,QAAQ,CAAA;IAC3D,SAAS,EAAE,MAAM,CAAA;IACjB,OAAO,CAAC,EAAE,MAAM,CAAA;IAChB,eAAe,CAAC,EAAE,GAAG,CAAA;IACrB,MAAM,CAAC,EAAE,GAAG,CAAA;IACZ,KAAK,CAAC,EAAE,MAAM,CAAA;IACd,OAAO,EAAE;QACP,aAAa,EAAE,MAAM,CAAA;QACrB,aAAa,EAAE,MAAM,CAAA;QACrB,YAAY,EAAE,MAAM,CAAA;QACpB,WAAW,EAAE,MAAM,CAAA;QACnB,eAAe,EAAE,MAAM,CAAA;KACxB,CAAA;IACD,MAAM,EAAE,GAAG,EAAE,CAAA;CACd;AAED,UAAU,eAAe;IACvB,WAAW,EAAE,MAAM,CAAA;IACnB,OAAO,EAAE,MAAM,CAAA;IACf,UAAU,EAAE,MAAM,CAAA;IAClB,YAAY,EAAE,MAAM,CAAA;IACpB,MAAM,EAAE,MAAM,EAAE,CAAA;IAChB,WAAW,EAAE,MAAM,EAAE,CAAA;CACtB;AAED,UAAU,gBAAgB;IACxB,aAAa,EAAE,MAAM,CAAA;IACrB,eAAe,EAAE,MAAM,CAAA;IACvB,eAAe,EAAE,MAAM,CAAA;IACvB,WAAW,EAAE,MAAM,CAAA;IACnB,oBAAoB,EAAE,MAAM,CAAA;IAC5B,gBAAgB,EAAE,KAAK,CAAC;QAAE,SAAS,EAAE,MAAM,CAAC;QAAC,UAAU,EAAE,MAAM,CAAA;KAAE,CAAC,CAAA;IAClE,oBAAoB,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IACzC,iBAAiB,EAAE,MAAM,CAAA;CAC1B;AAED,eAAe,2BAA2B,CAAA"}