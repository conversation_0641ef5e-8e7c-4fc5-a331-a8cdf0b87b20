/**
 * AG3NT Framework - Intelligent Load Balancer
 *
 * Advanced load balancing system with multiple algorithms, health-aware routing,
 * and adaptive performance optimization for agent workload distribution.
 */
import { EventEmitter } from "events";
import { AgentInstance, AgentDiscoveryService } from "./agent-discovery-service";
export interface LoadBalancerConfig {
    algorithm: 'round_robin' | 'weighted_round_robin' | 'least_connections' | 'least_response_time' | 'adaptive' | 'consistent_hash';
    enableHealthChecks: boolean;
    enableCircuitBreaker: boolean;
    enableStickySessions: boolean;
    maxRetries: number;
    retryDelay: number;
    circuitBreakerThreshold: number;
    circuitBreakerTimeout: number;
    adaptiveWindowSize: number;
}
export interface LoadBalancingRequest {
    requestId: string;
    agentType: string;
    capabilities?: string[];
    sessionId?: string;
    priority: 'low' | 'medium' | 'high' | 'critical';
    estimatedDuration?: number;
    resourceRequirements?: {
        cpu: number;
        memory: number;
        network: number;
    };
    metadata?: Record<string, any>;
}
export interface LoadBalancingResult {
    selectedAgent: AgentInstance;
    algorithm: string;
    selectionTime: number;
    reasoning: string[];
    alternativeAgents: AgentInstance[];
    loadDistribution: LoadDistribution;
}
export interface LoadDistribution {
    totalAgents: number;
    availableAgents: number;
    averageLoad: number;
    loadVariance: number;
    hotspots: AgentHotspot[];
}
export interface AgentHotspot {
    agentId: string;
    loadPercentage: number;
    severity: 'warning' | 'critical';
    recommendation: string;
}
export interface CircuitBreakerState {
    agentId: string;
    state: 'closed' | 'open' | 'half_open';
    failureCount: number;
    lastFailureTime: number;
    nextRetryTime: number;
}
export interface StickySession {
    sessionId: string;
    agentId: string;
    createdAt: number;
    lastUsed: number;
    requestCount: number;
}
export interface LoadBalancingMetrics {
    totalRequests: number;
    successfulRoutes: number;
    failedRoutes: number;
    averageSelectionTime: number;
    algorithmUsage: Map<string, number>;
    agentUtilization: Map<string, number>;
    circuitBreakerTrips: number;
    stickySessionHits: number;
}
/**
 * Intelligent Load Balancer
 */
export declare class LoadBalancer extends EventEmitter {
    private config;
    private discoveryService;
    private circuitBreakers;
    private stickySessions;
    private requestHistory;
    private metrics;
    private roundRobinIndex;
    private adaptiveWeights;
    constructor(discoveryService: AgentDiscoveryService, config?: Partial<LoadBalancerConfig>);
    /**
     * Route request to optimal agent
     */
    routeRequest(request: LoadBalancingRequest): Promise<LoadBalancingResult>;
    /**
     * Get load distribution statistics
     */
    getLoadDistribution(): LoadDistribution;
    /**
     * Get load balancing metrics
     */
    getMetrics(): LoadBalancingMetrics;
    /**
     * Update load balancer configuration
     */
    updateConfig(config: Partial<LoadBalancerConfig>): void;
    /**
     * Reset circuit breaker for agent
     */
    resetCircuitBreaker(agentId: string): void;
    /**
     * Clear sticky session
     */
    clearStickySession(sessionId: string): void;
    /**
     * Private helper methods
     */
    private getAvailableAgents;
    private selectAgent;
    private roundRobinSelection;
    private weightedRoundRobinSelection;
    private leastConnectionsSelection;
    private leastResponseTimeSelection;
    private consistentHashSelection;
    private adaptiveSelection;
    private calculateAdaptiveScore;
    private updateAdaptiveWeights;
    private checkStickySession;
    private updateStickySession;
    private updateCircuitBreaker;
    private createResult;
    private generateSelectionReasoning;
    private updateMetrics;
    private hashString;
    private setupEventHandlers;
    /**
     * Shutdown load balancer
     */
    shutdown(): void;
}
export default LoadBalancer;
//# sourceMappingURL=load-balancer.d.ts.map