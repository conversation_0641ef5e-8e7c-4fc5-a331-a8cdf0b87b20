{"name": "fake-llm-server", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "Fake OpenAI API server for testing", "dependencies": {"cors": "^2.8.5", "express": "^4.18.2", "stream": "0.0.2"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^4.17.21", "@types/node": "^20.17.46", "git-http-mock-server": "^2.0.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}