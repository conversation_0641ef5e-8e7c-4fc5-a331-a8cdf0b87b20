"use strict";
/**
 * AG3NT Framework - Planning Agent
 *
 * Concrete implementation of BaseAgent for project planning.
 * Extracted from the sophisticated planning-graph.ts implementation.
 *
 * Features:
 * - Complete project analysis and planning workflow
 * - MCP-enhanced execution with context enrichment
 * - Sequential thinking for complex decisions
 * - Dynamic step sequencing based on project type
 * - Interactive clarification handling
 * - Progress tracking and session management
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = exports.PlanningAgent = void 0;
const base_agent_1 = require("../core/base-agent");
const ai_service_1 = require("../../ai-service");
/**
 * Planning Agent - Sophisticated project planning with MCP enhancement
 */
class PlanningAgent extends base_agent_1.BaseAgent {
    constructor(config = {}) {
        super('planning-agent', {
            capabilities: {
                requiredCapabilities: [
                    'requirements_analysis',
                    'tech_stack_selection',
                    'architecture_design',
                    'cross_validation'
                ],
                contextFilters: ['all'], // Planning agent needs full context
                mcpEnhanced: true,
                sequentialThinking: true,
                contextEnrichment: true,
                ragIntegration: true
            },
            ...config
        });
        this.planningSteps = [
            'analyze', 'clarify', 'summary', 'techstack', 'prd',
            'context-profile', 'wireframes', 'design', 'database',
            'filesystem', 'workflow', 'tasks', 'scaffold'
        ];
    }
    /**
     * Execute planning workflow - extracted from planning-graph.ts execute()
     */
    async executeWorkflow(state) {
        const input = state.input;
        console.log(`🚀 Starting planning workflow for: ${input.prompt.substring(0, 100)}...`);
        // Always start with analyze step
        let currentStep = 'analyze';
        const result = await this.executeStepWithContext(currentStep, input);
        if (result.needsInput) {
            // Interactive mode - return for user input
            return state;
        }
        // Determine dynamic step sequence based on project type
        const projectType = state.results.analyze?.projectType;
        const stepSequence = this.getStepsForProjectType(projectType);
        console.log(`📋 Executing ${stepSequence.length} steps for ${projectType} project`);
        // Execute remaining steps sequentially
        for (const stepId of stepSequence.slice(1)) {
            if (state.needsInput) {
                break; // Stop if user input is needed
            }
            await this.executeStepWithContext(stepId);
            if (state.needsInput) {
                break;
            }
        }
        // Mark as completed if no input needed
        if (!state.needsInput) {
            state.completed = true;
        }
        return state;
    }
    /**
     * Execute individual planning step - extracted from planning-graph.ts patterns
     */
    async executeStep(stepId, context) {
        const { stepInput, mcpContext, agentState } = context;
        const input = stepInput;
        console.log(`🧠 Executing planning step: ${stepId}`);
        // Build enhanced context for AI execution
        const enhancedState = {
            ...agentState,
            mcpContext
        };
        // Execute step with MCP enhancement
        switch (stepId) {
            case 'analyze':
                return await this.analyzePromptWithMCP(enhancedState);
            case 'clarify':
                return await this.clarifyRequirementsWithMCP(enhancedState);
            case 'summary':
                return await this.generateSummaryWithMCP(enhancedState);
            case 'techstack':
                return await this.selectTechStackWithMCP(enhancedState);
            case 'prd':
                return await this.createPRDWithMCP(enhancedState);
            case 'context-profile':
                return await this.generateContextProfileWithMCP(enhancedState);
            case 'wireframes':
                return await this.designWireframesWithMCP(enhancedState);
            case 'design':
                return await this.createDesignGuidelinesWithMCP(enhancedState);
            case 'database':
                return await this.designDatabaseSchemaWithMCP(enhancedState);
            case 'filesystem':
                return await this.planFilesystemWithMCP(enhancedState);
            case 'workflow':
                return await this.defineWorkflowWithMCP(enhancedState);
            case 'tasks':
                return await this.breakdownTasksWithMCP(enhancedState);
            case 'scaffold':
                return await this.generateScaffoldWithMCP(enhancedState);
            default:
                throw new Error(`Unknown planning step: ${stepId}`);
        }
    }
    /**
     * Get total steps for progress tracking
     */
    getTotalSteps() {
        return this.planningSteps.length;
    }
    /**
     * Get relevant documentation for step
     */
    async getRelevantDocumentation(stepId) {
        const relevantDocs = {};
        if (!this.state?.results.techstack) {
            return relevantDocs;
        }
        const techStack = this.state.results.techstack;
        // Get documentation for frontend technology
        if (techStack.frontend && this.contextEngine) {
            const frontendTech = typeof techStack.frontend === 'string'
                ? techStack.frontend
                : techStack.frontend.framework || techStack.frontend.name || 'React';
            relevantDocs.frontend = await this.contextEngine.getDocumentation(frontendTech, `${stepId} implementation`);
        }
        // Get documentation for backend technology
        if (techStack.backend && this.contextEngine) {
            const backendTech = typeof techStack.backend === 'string'
                ? techStack.backend
                : techStack.backend.framework || techStack.backend.name || 'Node.js';
            relevantDocs.backend = await this.contextEngine.getDocumentation(backendTech, `${stepId} implementation`);
        }
        return relevantDocs;
    }
    /**
     * Determine step sequence based on project type
     */
    getStepsForProjectType(projectType) {
        const baseSteps = ['analyze', 'clarify', 'summary', 'techstack', 'prd'];
        switch (projectType) {
            case 'web_app':
            case 'mobile_app':
                return [
                    ...baseSteps,
                    'context-profile',
                    'wireframes',
                    'design',
                    'database',
                    'filesystem',
                    'workflow',
                    'tasks',
                    'scaffold'
                ];
            case 'api':
            case 'microservice':
                return [
                    ...baseSteps,
                    'context-profile',
                    'database',
                    'filesystem',
                    'workflow',
                    'tasks',
                    'scaffold'
                ];
            case 'cli_tool':
            case 'library':
                return [
                    ...baseSteps,
                    'context-profile',
                    'filesystem',
                    'workflow',
                    'tasks',
                    'scaffold'
                ];
            default:
                return this.planningSteps; // Full sequence for unknown types
        }
    }
    // MCP-Enhanced step implementations (delegating to aiService for now)
    async analyzePromptWithMCP(state) {
        const input = state.input;
        const analysis = await ai_service_1.aiService.analyzePrompt(input.prompt, {
            designStyleGuide: input.designStyleGuide,
            hasImages: input.hasImages
        });
        return {
            results: analysis,
            needsInput: false,
            completed: false
        };
    }
    async clarifyRequirementsWithMCP(state) {
        const input = state.input;
        const analysis = state.results.analyze;
        const userAnswers = input.userAnswers || {};
        // Check if we have all required answers
        const requiredAnswers = ['target_users', 'platform', 'timeline', 'budget'];
        const missingAnswers = requiredAnswers.filter(key => !userAnswers[key]);
        if (missingAnswers.length === 0) {
            return {
                results: {
                    targetUsers: userAnswers.target_users,
                    platform: userAnswers.platform,
                    timeline: userAnswers.timeline,
                    budget: userAnswers.budget,
                    completed: true
                },
                needsInput: false,
                completed: false
            };
        }
        // Generate clarification questions
        const clarificationData = await ai_service_1.aiService.generateClarificationQuestions(input.prompt, analysis);
        const nextQuestion = clarificationData.questions.find((q) => !userAnswers[q.id] && missingAnswers.includes(q.id)) || clarificationData.questions[0];
        return {
            results: clarificationData,
            question: nextQuestion,
            needsInput: true,
            completed: false
        };
    }
    async generateSummaryWithMCP(state) {
        const input = state.input;
        const analysis = state.results.analyze;
        const clarifications = state.results.clarify;
        const summary = await ai_service_1.aiService.generateSummary(input.prompt, analysis, clarifications);
        return {
            results: summary,
            needsInput: false,
            completed: false
        };
    }
    async selectTechStackWithMCP(state) {
        const input = state.input;
        const analysis = state.results.analyze;
        const clarifications = state.results.clarify;
        const techStack = await ai_service_1.aiService.selectTechStack(input.prompt, analysis, clarifications);
        return {
            results: techStack,
            needsInput: false,
            completed: false
        };
    }
    async createPRDWithMCP(state) {
        const input = state.input;
        const previousResults = state.results;
        const prd = await ai_service_1.aiService.createPRD(input.prompt, previousResults.analyze, previousResults.techstack, previousResults.clarify);
        return {
            results: prd,
            needsInput: false,
            completed: false
        };
    }
    async generateContextProfileWithMCP(state) {
        const input = state.input;
        const previousResults = state.results;
        const contextProfile = await ai_service_1.aiService.generateContextProfile(input.prompt, previousResults.analyze, previousResults.summary, previousResults.prd);
        return {
            results: contextProfile,
            needsInput: false,
            completed: false
        };
    }
    async designWireframesWithMCP(state) {
        const input = state.input;
        const previousResults = state.results;
        const wireframes = await ai_service_1.aiService.designWireframes(input.prompt, previousResults.analyze, previousResults.prd);
        return {
            results: wireframes,
            needsInput: false,
            completed: false
        };
    }
    async createDesignGuidelinesWithMCP(state) {
        // For now, return basic design guidelines
        const designGuidelines = {
            colorPalette: ["#1f2937", "#3b82f6", "#10b981", "#f59e0b"],
            typography: "Modern sans-serif fonts",
            spacing: "8px grid system",
            components: "Consistent button styles, form inputs, cards"
        };
        return {
            results: designGuidelines,
            needsInput: false,
            completed: false
        };
    }
    async designDatabaseSchemaWithMCP(state) {
        const input = state.input;
        const previousResults = state.results;
        const schema = await ai_service_1.aiService.designDatabaseSchema(input.prompt, previousResults.analyze, previousResults.prd, previousResults.techstack);
        return {
            results: schema,
            needsInput: false,
            completed: false
        };
    }
    async planFilesystemWithMCP(state) {
        const input = state.input;
        const previousResults = state.results;
        const filesystem = await ai_service_1.aiService.planFileSystem(input.prompt, previousResults.techstack, previousResults.analyze, previousResults.design);
        return {
            results: filesystem,
            needsInput: false,
            completed: false
        };
    }
    async defineWorkflowWithMCP(state) {
        const input = state.input;
        const previousResults = state.results;
        const workflow = await ai_service_1.aiService.defineWorkflow(input.prompt, previousResults.analyze, previousResults.prd, previousResults.wireframes, previousResults.design);
        return {
            results: workflow,
            needsInput: false,
            completed: false
        };
    }
    async breakdownTasksWithMCP(state) {
        const input = state.input;
        const previousResults = state.results;
        const tasks = await ai_service_1.aiService.breakdownTasks(input.prompt, previousResults, previousResults.design);
        return {
            results: tasks,
            needsInput: false,
            completed: false
        };
    }
    async generateScaffoldWithMCP(state) {
        const input = state.input;
        const previousResults = state.results;
        const scaffold = await ai_service_1.aiService.generateProjectScaffold(input.prompt, previousResults.analyze, previousResults.techstack, previousResults.filesystem, previousResults.database);
        return {
            results: scaffold,
            needsInput: false,
            completed: true // Final step
        };
    }
}
exports.PlanningAgent = PlanningAgent;
exports.default = PlanningAgent;
//# sourceMappingURL=planning-agent.js.map