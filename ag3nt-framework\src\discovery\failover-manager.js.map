{"version": 3, "file": "failover-manager.js", "sourceRoot": "", "sources": ["failover-manager.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;AAEH,mCAAqC;AA8GrC;;GAEG;AACH,MAAa,eAAgB,SAAQ,qBAAY;IAW/C,YACE,gBAAuC,EACvC,YAA0B,EAC1B,SAAkC,EAAE;QAEpC,KAAK,EAAE,CAAA;QAZD,kBAAa,GAA8B,IAAI,GAAG,EAAE,CAAA;QACpD,iBAAY,GAA+B,IAAI,GAAG,EAAE,CAAA;QACpD,oBAAe,GAAoB,EAAE,CAAA;QACrC,oBAAe,GAA+B,IAAI,GAAG,EAAE,CAAA;QAW7D,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAA;QACxC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;QAChC,IAAI,CAAC,MAAM,GAAG;YACZ,kBAAkB,EAAE,IAAI;YACxB,sBAAsB,EAAE,IAAI;YAC5B,wBAAwB,EAAE,IAAI,EAAE,YAAY;YAC5C,mBAAmB,EAAE,CAAC;YACtB,eAAe,EAAE,KAAK,EAAE,aAAa;YACrC,gBAAgB,EAAE,GAAG,EAAE,oBAAoB;YAC3C,qBAAqB,EAAE,IAAI;YAC3B,mBAAmB,EAAE,IAAI;YACzB,GAAG,MAAM;SACV,CAAA;QAED,IAAI,CAAC,OAAO,GAAG;YACb,cAAc,EAAE,CAAC;YACjB,mBAAmB,EAAE,CAAC;YACtB,mBAAmB,EAAE,CAAC;YACtB,mBAAmB,EAAE,CAAC;YACtB,iBAAiB,EAAE,CAAC;YACpB,eAAe,EAAE,CAAC;YAClB,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,CAAC;SACR,CAAA;QAED,IAAI,CAAC,kBAAkB,EAAE,CAAA;IAC3B,CAAC;IAED;;OAEG;IACH,KAAK;QACH,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAA;QAE5C,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;YACnC,IAAI,CAAC,qBAAqB,EAAE,CAAA;QAC9B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,cAAsB;QAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAA;QACnE,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CAAC,iBAAiB,cAAc,YAAY,CAAC,CAAA;QAC9D,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,yCAAyC,cAAc,EAAE,CAAC,CAAA;QAEtE,8BAA8B;QAC9B,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAA;QAE9D,MAAM,IAAI,GAAiB;YACzB,MAAM,EAAE,YAAY,cAAc,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;YAClD,YAAY,EAAE,cAAc;YAC5B,YAAY;YACZ,gBAAgB,EAAE,IAAI,CAAC,qBAAqB,EAAE;YAC9C,aAAa,EAAE,IAAI,CAAC,mBAAmB,CAAC,cAAc,EAAE,YAAY,CAAC;YACrE,aAAa,EAAE,IAAI,CAAC,mBAAmB,CAAC,cAAc,EAAE,YAAY,CAAC;YACrE,YAAY,EAAE;gBACZ,SAAS,EAAE,QAAQ;gBACnB,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,YAAY;gBAC5D,WAAW,EAAE,EAAE;aAChB;SACF,CAAA;QAED,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;QAC5C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,EAAE,YAAY,CAAC,CAAA;QAEnD,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAA;QACxC,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CAAC,cAAsB,EAAE,MAAc,EAAE,SAAkB,KAAK;QACnF,OAAO,CAAC,GAAG,CAAC,qCAAqC,cAAc,EAAE,CAAC,CAAA;QAClE,OAAO,CAAC,GAAG,CAAC,cAAc,MAAM,EAAE,CAAC,CAAA;QAEnC,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;QACnD,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,oCAAoC,cAAc,EAAE,CAAC,CAAA;QACvE,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAC5B,MAAM,aAAa,GAAkB;YACnC,OAAO,EAAE,YAAY,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC5E,IAAI,EAAE,oBAAoB;YAC1B,SAAS,EAAE,SAAS;YACpB,YAAY,EAAE,cAAc;YAC5B,MAAM;YACN,MAAM,EAAE;gBACN,gBAAgB,EAAE,CAAC;gBACnB,iBAAiB,EAAE,CAAC;gBACpB,QAAQ,EAAE,KAAK;gBACf,iBAAiB,EAAE,SAAS;aAC7B;YACD,QAAQ,EAAE;gBACR,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,WAAW;gBACzC,aAAa,EAAE,KAAK;gBACpB,oBAAoB,EAAE,MAAM;gBAC5B,YAAY,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;aACpD;SACF,CAAA;QAED,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,cAAc,EAAE,aAAa,CAAC,CAAA;QAEvD,IAAI,CAAC;YACH,yBAAyB;YACzB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,aAAa,CAAC,CAAA;YAExE,aAAa,CAAC,WAAW,GAAG,WAAW,CAAC,OAAO,CAAA;YAC/C,aAAa,CAAC,IAAI,GAAG,oBAAoB,CAAA;YAEzC,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;YAC3C,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,YAAY,EAAE,IAAI,CAAC,CAAA;YAErD,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,aAAa,CAAC,CAAA;YAC9C,OAAO,CAAC,GAAG,CAAC,2BAA2B,YAAY,IAAI,CAAC,CAAA;YAExD,OAAO,aAAa,CAAA;QAEtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,aAAa,CAAC,IAAI,GAAG,eAAe,CAAA;YACpC,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EAAE,KAAK,CAAC,CAAA;YAEhE,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC,CAAA;YACtD,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAA;YAE1C,MAAM,KAAK,CAAA;QACb,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,cAAc,CAAC,CAAA;YAC3C,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;QAC1C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,cAAsB;QAC9C,OAAO,CAAC,GAAG,CAAC,gCAAgC,cAAc,EAAE,CAAC,CAAA;QAE7D,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;QACnD,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,oCAAoC,cAAc,EAAE,CAAC,CAAA;QACvE,CAAC;QAED,oCAAoC;QACpC,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAA;QACnE,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACvD,MAAM,IAAI,KAAK,CAAC,iBAAiB,cAAc,iBAAiB,CAAC,CAAA;QACnE,CAAC;QAED,yBAAyB;QACzB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,cAAc,CAAC,CAAA;QACtD,CAAC;QAED,MAAM,aAAa,GAAkB;YACnC,OAAO,EAAE,YAAY,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAC5E,IAAI,EAAE,gBAAgB;YACtB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,YAAY,EAAE,cAAc;YAC5B,MAAM,EAAE,yBAAyB;YACjC,MAAM,EAAE;gBACN,gBAAgB,EAAE,CAAC;gBACnB,iBAAiB,EAAE,CAAC;gBACpB,QAAQ,EAAE,KAAK;gBACf,iBAAiB,EAAE,MAAM;aAC1B;YACD,QAAQ,EAAE;gBACR,QAAQ,EAAE,UAAU;gBACpB,aAAa,EAAE,CAAC;gBAChB,oBAAoB,EAAE,KAAK;gBAC3B,YAAY,EAAE,EAAE;aACjB;SACF,CAAA;QAED,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;QACxC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAA;QAC3C,OAAO,CAAC,GAAG,CAAC,mBAAmB,cAAc,YAAY,CAAC,CAAA;IAC5D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,cAAsB;QAC3C,OAAO,CAAC,GAAG,CAAC,uCAAuC,cAAc,EAAE,CAAC,CAAA;QAEpE,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;QACnD,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,oCAAoC,cAAc,EAAE,CAAC,CAAA;QACvE,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAC5B,MAAM,UAAU,GAAe;YAC7B,MAAM,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE;YAC5B,SAAS,EAAE,SAAS;YACpB,OAAO,EAAE,KAAK;YACd,YAAY,EAAE,CAAC;YACf,MAAM,EAAE,EAAE;YACV,eAAe,EAAE,EAAE;SACpB,CAAA;QAED,IAAI,CAAC;YACH,uDAAuD;YACvD,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,CAAA;YAEhE,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC9B,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAA;gBACpD,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAA;YAC5D,CAAC;YAED,8BAA8B;YAC9B,KAAK,MAAM,MAAM,IAAI,YAAY,EAAE,CAAC;gBAClC,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;gBAClE,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;oBACrD,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,MAAM,CAAC,OAAO,iBAAiB,CAAC,CAAA;oBACvE,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,sBAAsB,MAAM,CAAC,OAAO,SAAS,CAAC,CAAA;gBAChF,CAAC;YACH,CAAC;YAED,wBAAwB;YACxB,IAAI,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE,CAAC;gBACtC,KAAK,MAAM,MAAM,IAAI,YAAY,EAAE,CAAC;oBAClC,IAAI,MAAM,CAAC,eAAe,GAAG,IAAI,EAAE,CAAC;wBAClC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAmC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAA;wBAC3E,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAA;oBAC7D,CAAC;gBACH,CAAC;YACH,CAAC;YAED,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAA;YAChD,UAAU,CAAC,OAAO,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAA;YAEnD,uBAAuB;YACvB,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YACvC,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA;YACpF,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YAE9C,iCAAiC;YACjC,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;gBAC9C,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAA;YAC1E,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE,UAAU,CAAC,CAAA;YAChD,OAAO,CAAC,GAAG,CAAC,+BAA+B,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAA;YAEtF,OAAO,UAAU,CAAA;QAEnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAA;YAC5G,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAA;YAEpE,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC,CAAA;YACxD,OAAO,UAAU,CAAA;QACnB,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;IACpC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,YAA2B;QACxD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC;YAC3D,SAAS,EAAE,YAAY,CAAC,SAAS;YACjC,YAAY,EAAE,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YACxD,aAAa,EAAE,CAAC,YAAY,CAAC,OAAO,CAAC;SACtC,CAAC,CAAA;QAEF,MAAM,YAAY,GAAkB,EAAE,CAAA;QACtC,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAA;QAElG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YACvE,MAAM,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;YACjC,YAAY,CAAC,IAAI,CAAC;gBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,YAAY,EAAE,YAAY,CAAC,OAAO;gBAClC,cAAc,EAAE,MAAM;gBACtB,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;gBACxB,eAAe,EAAE,IAAI;gBACrB,cAAc,EAAE,IAAI,CAAC,YAAY;aAClC,CAAC,CAAA;QACJ,CAAC;QAED,OAAO,YAAY,CAAA;IACrB,CAAC;IAEO,qBAAqB;QAC3B,OAAO;YACL;gBACE,IAAI,EAAE,sBAAsB;gBAC5B,SAAS,EAAE,IAAI;gBACf,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,mBAAmB;gBACpD,UAAU,EAAE,KAAK,CAAC,WAAW;aAC9B;YACD;gBACE,IAAI,EAAE,kBAAkB;gBACxB,SAAS,EAAE,KAAK,EAAE,aAAa;gBAC/B,mBAAmB,EAAE,CAAC;gBACtB,UAAU,EAAE,MAAM,CAAC,YAAY;aAChC;YACD;gBACE,IAAI,EAAE,sBAAsB;gBAC5B,SAAS,EAAE,GAAG,EAAE,iBAAiB;gBACjC,mBAAmB,EAAE,CAAC;gBACtB,UAAU,EAAE,MAAM,CAAC,YAAY;aAChC;SACF,CAAA;IACH,CAAC;IAEO,mBAAmB,CAAC,cAAsB,EAAE,YAA2B;QAC7E,OAAO;YACL;gBACE,MAAM,EAAE,iBAAiB;gBACzB,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,iBAAiB;gBACzB,UAAU,EAAE,EAAE,YAAY,EAAE;gBAC5B,OAAO,EAAE,KAAK;gBACd,iBAAiB,EAAE,IAAI;aACxB;YACD;gBACE,MAAM,EAAE,gBAAgB;gBACxB,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,gBAAgB;gBACxB,UAAU,EAAE,EAAE,YAAY,EAAE,cAAc,EAAE;gBAC5C,OAAO,EAAE,KAAK;gBACd,iBAAiB,EAAE,IAAI;aACxB;YACD;gBACE,MAAM,EAAE,kBAAkB;gBAC1B,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,kBAAkB;gBAC1B,UAAU,EAAE,EAAE,SAAS,EAAE,cAAc,EAAE;gBACzC,OAAO,EAAE,IAAI;gBACb,iBAAiB,EAAE,KAAK;aACzB;SACF,CAAA;IACH,CAAC;IAEO,mBAAmB,CAAC,cAAsB,EAAE,YAA2B;QAC7E,OAAO;YACL;gBACE,MAAM,EAAE,iBAAiB;gBACzB,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,iBAAiB;gBACzB,UAAU,EAAE,EAAE,YAAY,EAAE,cAAc,EAAE;gBAC5C,OAAO,EAAE,KAAK;aACf;YACD;gBACE,MAAM,EAAE,qBAAqB;gBAC7B,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,qBAAqB;gBAC7B,UAAU,EAAE,EAAE,YAAY,EAAE;gBAC5B,OAAO,EAAE,KAAK;aACf;YACD;gBACE,MAAM,EAAE,uBAAuB;gBAC/B,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,uBAAuB;gBAC/B,UAAU,EAAE,EAAE,OAAO,EAAE,cAAc,EAAE;gBACvC,OAAO,EAAE,IAAI;aACd;SACF,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,IAAkB,EAAE,aAA4B;QACjF,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAA;QACnE,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;QAC/C,CAAC;QAED,2BAA2B;QAC3B,MAAM,cAAc,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,CAC3D,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAChE,CAAA;QAED,yBAAyB;QACzB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACtC,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE,cAAc,CAAC,CAAA;QACzE,CAAC;QAED,OAAO,cAAc,CAAA;IACvB,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,IAAkB,EAAE,cAAsB,EAAE,WAAwB;QACpG,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,CAAC,MAAM,EAAE,CAAC,CAAA;QAEzD,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YACpB,KAAK,iBAAiB;gBACpB,wBAAwB;gBACxB,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,WAAW,CAAC,OAAO,EAAE,SAAS,CAAC,CAAA;gBACvE,MAAK;YAEP,KAAK,gBAAgB;gBACnB,wCAAwC;gBACxC,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC;oBACpC,0BAA0B;oBAC1B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAA;gBACzD,CAAC;gBACD,MAAK;YAEP,KAAK,kBAAkB;gBACrB,2CAA2C;gBAC3C,8CAA8C;gBAC9C,MAAK;YAEP,KAAK,qBAAqB;gBACxB,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,cAAc,EAAE,WAAW,EAAE,WAAW,CAAC,OAAO,EAAE,CAAC,CAAA;gBACxF,MAAK;QACT,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,IAAkB,EAAE,cAAsB;QAC1E,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,CAAC,MAAM,EAAE,CAAC,CAAA;QAEzD,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YACpB,KAAK,iBAAiB;gBACpB,wBAAwB;gBACxB,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,cAAc,EAAE,SAAS,CAAC,CAAA;gBAClE,MAAK;YAEP,KAAK,qBAAqB;gBACxB,iCAAiC;gBACjC,IAAI,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC;oBACpC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAA;gBACzD,CAAC;gBACD,MAAK;YAEP,KAAK,uBAAuB;gBAC1B,mCAAmC;gBACnC,MAAK;QACT,CAAC;IACH,CAAC;IAEO,qBAAqB;QAC3B,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;YACtC,IAAI,CAAC,cAAc,EAAE,CAAA;QACvB,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAA;IAC1C,CAAC;IAEO,cAAc;QACpB,sDAAsD;QACtD,KAAK,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;YAClE,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAA;YAE5D,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gBAC3C,sCAAsC;gBACtC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC;oBAC9C,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,2BAA2B,EAAE,KAAK,CAAC;yBACrE,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC,CAAA;gBAClE,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,qBAAqB,CAAC,SAAoC;QAChE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QACtB,QAAQ,SAAS,EAAE,CAAC;YAClB,KAAK,OAAO;gBACV,OAAO,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA;YAClC,KAAK,QAAQ;gBACX,OAAO,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA;YACtC,KAAK,SAAS;gBACZ,OAAO,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA;YACvC;gBACE,OAAO,GAAG,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA;QACxC,CAAC;IACH,CAAC;IAEO,aAAa,CAAC,aAA4B,EAAE,YAAoB,EAAE,OAAgB;QACxF,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAA;QAE7B,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAA;QACpC,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,mBAAmB;YAC9B,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,CAAC,CAAC,GAAG,YAAY,CAAC;gBACrF,IAAI,CAAC,OAAO,CAAC,cAAc,CAAA;QAE7B,IAAI,aAAa,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAClC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAA;QAClC,CAAC;QAED,0BAA0B;QAC1B,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC,CAAA;YAC7E,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxB,MAAM,mBAAmB,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CACnE,OAAO,CAAC,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,SAAS,CAC9C,CAAA;gBACD,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,mBAAmB,CAAC,MAAM,CAAA;YAC3G,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,oBAAoB,CAAC,CAAA;YACpF,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1B,MAAM,aAAa,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAA;gBACnE,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,CAAA;YAC/F,CAAC;QACH,CAAC;IACH,CAAC;IAEO,kBAAkB;QACxB,kCAAkC;QAClC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC,sBAAsB,EAAE,CAAC,KAAK,EAAE,EAAE;YACzD,IAAI,KAAK,CAAC,SAAS,KAAK,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;gBACtE,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;gBAClD,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;oBACrD,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,OAAO,EAAE,mCAAmC,EAAE,KAAK,CAAC;yBAC5E,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC,CAAA;gBAClE,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACzB,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;QACrC,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAA;QAC1B,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;QACzB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAA;QAC5B,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAEzB,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAA;IACvD,CAAC;CACF;AAtjBD,0CAsjBC;AAED,kBAAe,eAAe,CAAA"}