{"version": 3, "file": "user-interaction-agent.js", "sourceRoot": "", "sources": ["user-interaction-agent.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;GAaG;;;AAEH,mDAAuE;AACvE,iDAA4C;AAuhB5C;;GAEG;AACH,MAAa,oBAAqB,SAAQ,sBAAS;IAOjD,YAAY,SAA+B,EAAE;QAC3C,KAAK,CAAC,kBAAkB,EAAE;YACxB,YAAY,EAAE;gBACZ,oBAAoB,EAAE;oBACpB,6BAA6B;oBAC7B,eAAe;oBACf,iBAAiB;oBACjB,wBAAwB;oBACxB,qBAAqB;oBACrB,qBAAqB;oBACrB,0BAA0B;iBAC3B;gBACD,cAAc,EAAE,CAAC,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,UAAU,EAAE,aAAa,CAAC;gBACnF,WAAW,EAAE,IAAI;gBACjB,kBAAkB,EAAE,IAAI;gBACxB,iBAAiB,EAAE,IAAI;gBACvB,cAAc,EAAE,IAAI;aACrB;YACD,GAAG,MAAM;SACV,CAAC,CAAA;QAzBa,qBAAgB,GAAG;YAClC,cAAc,EAAE,mBAAmB,EAAE,mBAAmB;YACxD,qBAAqB,EAAE,kBAAkB,EAAE,mBAAmB;YAC9D,sBAAsB,EAAE,sBAAsB,EAAE,gBAAgB;SACjE,CAAA;IAsBD,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,eAAe,CAAC,KAAiB;QAC/C,MAAM,KAAK,GAAG,KAAK,CAAC,KAA6B,CAAA;QAEjD,OAAO,CAAC,GAAG,CAAC,iCAAiC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;QAEhE,yCAAyC;QACzC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3C,OAAO,CAAC,GAAG,CAAC,sBAAsB,MAAM,EAAE,CAAC,CAAA;YAE3C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;YAEnE,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC1B,KAAK,CAAC,UAAU,GAAG,IAAI,CAAA;gBACvB,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAA;gBACpC,MAAK;YACP,CAAC;YAED,kBAAkB;YAClB,KAAK,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAA;YAC/B,KAAK,CAAC,QAAQ,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAA;YAErD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBAC1B,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,MAAM;gBACN,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,cAAc,GAAG,KAAK,CAAC,QAAQ,CAAC,UAAU;aACpE,CAAC,CAAA;QACJ,CAAC;QAED,uCAAuC;QACvC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YACtB,KAAK,CAAC,SAAS,GAAG,IAAI,CAAA;YACtB,OAAO,CAAC,GAAG,CAAC,iCAAiC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;QAClE,CAAC;QAED,OAAO,KAAK,CAAA;IACd,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,MAAc,EAAE,KAAW;QAC9D,2CAA2C;QAC3C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAA;QAE3D,oCAAoC;QACpC,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,cAAc;gBACjB,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAA;YAC5D,KAAK,mBAAmB;gBACtB,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAA;YAC1D,KAAK,mBAAmB;gBACtB,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAA;YAC1D,KAAK,qBAAqB;gBACxB,OAAO,MAAM,IAAI,CAAC,yBAAyB,CAAC,aAAa,CAAC,CAAA;YAC5D,KAAK,kBAAkB;gBACrB,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAA;YACzD,KAAK,mBAAmB;gBACtB,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAA;YAC1D,KAAK,sBAAsB;gBACzB,OAAO,MAAM,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,CAAA;YAC7D,KAAK,sBAAsB;gBACzB,OAAO,MAAM,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,CAAA;YAC7D,KAAK,gBAAgB;gBACnB,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAA;YACvD;gBACE,MAAM,IAAI,KAAK,CAAC,6BAA6B,MAAM,EAAE,CAAC,CAAA;QAC1D,CAAC;IACH,CAAC;IAED;;OAEG;IACO,aAAa;QACrB,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAA;IACrC,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,wBAAwB;QACtC,OAAO;YACL,eAAe,EAAE,yDAAyD;YAC1E,eAAe,EAAE,8CAA8C;YAC/D,QAAQ,EAAE,6CAA6C;YACvD,aAAa,EAAE,+CAA+C;YAC9D,aAAa,EAAE,oDAAoD;YACnE,QAAQ,EAAE,gDAAgD;SAC3D,CAAA;IACH,CAAC;IAED,+EAA+E;IAC/E,4CAA4C;IAC5C,+EAA+E;IAEvE,KAAK,CAAC,kBAAkB,CAAC,KAAU,EAAE,KAA2B;QACtE,MAAM,YAAY,GAAG,MAAM,sBAAS,CAAC,kBAAkB,CACrD,KAAK,CAAC,IAAI,EACV,KAAK,CAAC,OAAO,EACb,KAAK,CAAC,IAAI,CAAC,KAAK,CACjB,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,YAAY,GAAG,YAAY,CAAA;QAE/C,OAAO;YACL,OAAO,EAAE,YAAY;YACrB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,KAAU;QAC9C,MAAM,YAAY,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,YAAY,CAAA;QAErD,MAAM,mBAAmB,GAAG,MAAM,sBAAS,CAAC,oBAAoB,CAC9D,YAAY,EACZ,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,OAAO,CAC1B,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,mBAAmB,GAAG,mBAAmB,CAAA;QAE7D,OAAO;YACL,OAAO,EAAE,mBAAmB;YAC5B,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,KAAU;QAC9C,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,mBAAmB,CAAA;QAEnE,MAAM,QAAQ,GAAG,MAAM,sBAAS,CAAC,oBAAoB,CACnD,mBAAmB,EACnB,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CACnC,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAA;QAEvC,OAAO;YACL,OAAO,EAAE,QAAQ;YACjB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,yBAAyB,CAAC,KAAU;QAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,QAAQ,CAAA;QAE7C,MAAM,eAAe,GAAG,MAAM,sBAAS,CAAC,sBAAsB,CAC5D,QAAQ,EACR,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,IAAI,EACtB,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,YAAY,CAAC,eAAe,CAC/C,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,eAAe,GAAG,eAAe,CAAA;QAErD,OAAO;YACL,OAAO,EAAE,eAAe;YACxB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,KAAU;QAC7C,MAAM,eAAe,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,eAAe,CAAA;QAE3D,MAAM,QAAQ,GAAG,MAAM,sBAAS,CAAC,mBAAmB,CAClD,eAAe,EACf,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CACnC,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAA;QAEvC,OAAO;YACL,OAAO,EAAE,QAAQ;YACjB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,KAAU;QAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,QAAQ,CAAA;QAE7C,MAAM,QAAQ,GAAG,MAAM,sBAAS,CAAC,oBAAoB,CACnD,QAAQ,EACR,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAC/B,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAA;QAEvC,OAAO;YACL,OAAO,EAAE,QAAQ;YACjB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,0BAA0B,CAAC,KAAU;QACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,QAAQ,CAAA;QAE7C,MAAM,YAAY,GAAG,MAAM,sBAAS,CAAC,uBAAuB,CAC1D,QAAQ,EACR,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CACvC,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,YAAY,GAAG,YAAY,CAAA;QAE/C,OAAO;YACL,OAAO,EAAE,YAAY;YACrB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,0BAA0B,CAAC,KAAU;QACjD,MAAM,YAAY,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,YAAY,CAAA;QAErD,MAAM,YAAY,GAAG,MAAM,sBAAS,CAAC,uBAAuB,CAC1D,YAAY,EACZ,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,IAAI,CACvB,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,YAAY,GAAG,YAAY,CAAA;QAE/C,OAAO;YACL,OAAO,EAAE,YAAY;YACrB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,KAAU;QAC3C,MAAM,YAAY,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,YAAY,CAAA;QAErD,MAAM,aAAa,GAAG,MAAM,sBAAS,CAAC,iBAAiB,CACrD,YAAY,EACZ,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,IAAI,CACvB,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,aAAa,GAAG,aAAa,CAAA;QAEjD,OAAO;YACL,OAAO,EAAE,aAAa;YACtB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,IAAI,CAAC,aAAa;SAC9B,CAAA;IACH,CAAC;CACF;AAtRD,oDAsRC;AAGgC,uCAAO"}