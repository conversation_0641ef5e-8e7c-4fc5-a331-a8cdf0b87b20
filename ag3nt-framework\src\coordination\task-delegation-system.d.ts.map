{"version": 3, "file": "task-delegation-system.d.ts", "sourceRoot": "", "sources": ["task-delegation-system.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAA;AAErC,MAAM,WAAW,gBAAgB;IAC/B,4BAA4B,EAAE,OAAO,CAAA;IACrC,oBAAoB,EAAE,OAAO,CAAA;IAC7B,kBAAkB,EAAE,MAAM,CAAA;IAC1B,iBAAiB,EAAE,MAAM,CAAA;IACzB,mBAAmB,EAAE,OAAO,CAAA;IAC5B,cAAc,EAAE,OAAO,CAAA;CACxB;AAED,MAAM,WAAW,cAAc;IAC7B,OAAO,EAAE,MAAM,CAAA;IACf,cAAc,EAAE,MAAM,CAAA;IACtB,YAAY,EAAE,eAAe,EAAE,CAAA;IAC/B,gBAAgB,EAAE,eAAe,EAAE,CAAA;IACnC,WAAW,EAAE,MAAM,CAAA;IACnB,OAAO,EAAE,MAAM,CAAA;IACf,UAAU,EAAE,MAAM,CAAA;IAClB,eAAe,EAAE,MAAM,EAAE,CAAA;CAC1B;AAED,MAAM,WAAW,eAAe;IAC9B,IAAI,EAAE,MAAM,CAAA;IACZ,WAAW,EAAE,MAAM,CAAA;IACnB,UAAU,EAAE,MAAM,CAAA;IAClB,QAAQ,EAAE,MAAM,CAAA;IAChB,WAAW,EAAE,MAAM,CAAA;IACnB,WAAW,EAAE,MAAM,CAAA;CACpB;AAED,MAAM,WAAW,eAAe;IAC9B,aAAa,EAAE,MAAM,EAAE,CAAA;IACvB,iBAAiB,EAAE,MAAM,CAAA;IACzB,gBAAgB,EAAE,MAAM,EAAE,CAAA;IAC1B,gBAAgB,EAAE,OAAO,CAAA;CAC1B;AAED,MAAM,WAAW,cAAc;IAC7B,YAAY,EAAE,MAAM,CAAA;IACpB,SAAS,EAAE,MAAM,CAAA;IACjB,OAAO,EAAE,MAAM,CAAA;IACf,IAAI,EAAE,aAAa,CAAA;IACnB,cAAc,EAAE,cAAc,GAAG,MAAM,GAAG,WAAW,GAAG,cAAc,CAAA;IACtE,SAAS,EAAE,mBAAmB,CAAA;IAC9B,MAAM,EAAE,SAAS,GAAG,UAAU,GAAG,UAAU,GAAG,aAAa,GAAG,WAAW,GAAG,QAAQ,GAAG,aAAa,CAAA;IACpG,SAAS,EAAE,MAAM,CAAA;IACjB,UAAU,CAAC,EAAE,MAAM,CAAA;IACnB,WAAW,CAAC,EAAE,MAAM,CAAA;IACpB,MAAM,CAAC,EAAE,gBAAgB,CAAA;IACzB,YAAY,CAAC,EAAE,YAAY,CAAA;CAC5B;AAED,MAAM,WAAW,aAAa;IAC5B,MAAM,EAAE,MAAM,CAAA;IACd,IAAI,EAAE,MAAM,CAAA;IACZ,WAAW,EAAE,MAAM,CAAA;IACnB,YAAY,EAAE,eAAe,EAAE,CAAA;IAC/B,WAAW,EAAE,cAAc,EAAE,CAAA;IAC7B,QAAQ,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAA;IAChD,QAAQ,CAAC,EAAE,MAAM,CAAA;IACjB,OAAO,EAAE,GAAG,CAAA;IACZ,YAAY,EAAE,MAAM,EAAE,CAAA;IACtB,cAAc,EAAE,GAAG,CAAA;CACpB;AAED,MAAM,WAAW,eAAe;IAC9B,UAAU,EAAE,MAAM,CAAA;IAClB,kBAAkB,EAAE,MAAM,CAAA;IAC1B,QAAQ,EAAE,OAAO,CAAA;IACjB,MAAM,EAAE,MAAM,CAAA;CACf;AAED,MAAM,WAAW,cAAc;IAC7B,IAAI,EAAE,MAAM,GAAG,UAAU,GAAG,SAAS,GAAG,UAAU,CAAA;IAClD,KAAK,EAAE,GAAG,CAAA;IACV,MAAM,EAAE,OAAO,CAAA;CAChB;AAED,MAAM,WAAW,mBAAmB;IAClC,KAAK,EAAE,MAAM,CAAA;IACb,KAAK,EAAE,MAAM,EAAE,CAAA;IACf,WAAW,EAAE,MAAM,EAAE,CAAA;IACrB,cAAc,EAAE,OAAO,CAAA;IACvB,iBAAiB,EAAE,OAAO,CAAA;CAC3B;AAED,MAAM,WAAW,gBAAgB;IAC/B,OAAO,EAAE,OAAO,CAAA;IAChB,MAAM,EAAE,GAAG,CAAA;IACX,OAAO,EAAE,iBAAiB,CAAA;IAC1B,QAAQ,EAAE,MAAM,CAAA;IAChB,MAAM,EAAE,eAAe,EAAE,CAAA;IACzB,eAAe,EAAE,MAAM,EAAE,CAAA;CAC1B;AAED,MAAM,WAAW,iBAAiB;IAChC,aAAa,EAAE,MAAM,CAAA;IACrB,YAAY,EAAE,MAAM,CAAA;IACpB,aAAa,EAAE,GAAG,CAAA;IAClB,UAAU,EAAE,MAAM,CAAA;IAClB,gBAAgB,EAAE,MAAM,CAAA;CACzB;AAED,MAAM,WAAW,eAAe;IAC9B,IAAI,EAAE,qBAAqB,GAAG,qBAAqB,GAAG,cAAc,GAAG,eAAe,CAAA;IACtF,QAAQ,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAA;IAChD,WAAW,EAAE,MAAM,CAAA;IACnB,MAAM,EAAE,MAAM,CAAA;IACd,UAAU,CAAC,EAAE,MAAM,CAAA;CACpB;AAED,MAAM,WAAW,YAAY;IAC3B,QAAQ,EAAE,eAAe,EAAE,CAAA;IAC3B,KAAK,EAAE,YAAY,EAAE,CAAA;IACrB,aAAa,CAAC,EAAE,MAAM,CAAA;IACtB,YAAY,EAAE,OAAO,CAAA;IACrB,oBAAoB,EAAE,OAAO,CAAA;CAC9B;AAED,MAAM,WAAW,eAAe;IAC9B,SAAS,EAAE,MAAM,CAAA;IACjB,SAAS,EAAE,GAAG,CAAA;IACd,UAAU,EAAE,MAAM,CAAA;CACnB;AAED,MAAM,WAAW,YAAY;IAC3B,KAAK,EAAE,MAAM,CAAA;IACb,MAAM,EAAE,MAAM,CAAA;IACd,UAAU,EAAE,GAAG,CAAA;IACf,UAAU,EAAE,MAAM,CAAA;CACnB;AAED,MAAM,WAAW,kBAAkB;IACjC,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,EAAE,kBAAkB,GAAG,YAAY,GAAG,iBAAiB,GAAG,QAAQ,CAAA;IACtE,iBAAiB,EAAE,iBAAiB,CAAA;IACpC,gBAAgB,CAAC,EAAE,MAAM,CAAA;IACzB,iBAAiB,EAAE,MAAM,EAAE,CAAA;CAC5B;AAED,MAAM,WAAW,iBAAiB;IAChC,gBAAgB,EAAE,MAAM,CAAA;IACxB,UAAU,EAAE,MAAM,CAAA;IAClB,WAAW,EAAE,MAAM,CAAA;IACnB,gBAAgB,EAAE,MAAM,CAAA;IACxB,kBAAkB,EAAE,MAAM,CAAA;IAC1B,UAAU,EAAE,MAAM,CAAA;CACnB;AAED,MAAM,WAAW,mBAAmB;IAClC,gBAAgB,EAAE,MAAM,CAAA;IACxB,WAAW,EAAE,MAAM,CAAA;IACnB,oBAAoB,EAAE,MAAM,CAAA;IAC5B,aAAa,EAAE,gBAAgB,EAAE,CAAA;IACjC,WAAW,EAAE,oBAAoB,EAAE,CAAA;IACnC,eAAe,EAAE,0BAA0B,EAAE,CAAA;CAC9C;AAED,MAAM,WAAW,gBAAgB;IAC/B,OAAO,EAAE,MAAM,CAAA;IACf,mBAAmB,EAAE,MAAM,CAAA;IAC3B,WAAW,EAAE,MAAM,CAAA;IACnB,WAAW,EAAE,MAAM,CAAA;IACnB,YAAY,EAAE,MAAM,CAAA;IACpB,UAAU,EAAE,MAAM,CAAA;CACnB;AAED,MAAM,WAAW,oBAAoB;IACnC,IAAI,EAAE,gBAAgB,GAAG,UAAU,GAAG,oBAAoB,GAAG,qBAAqB,CAAA;IAClF,WAAW,EAAE,MAAM,CAAA;IACnB,MAAM,EAAE,MAAM,CAAA;IACd,iBAAiB,EAAE,MAAM,CAAA;CAC1B;AAED,MAAM,WAAW,0BAA0B;IACzC,IAAI,EAAE,UAAU,GAAG,gBAAgB,GAAG,sBAAsB,GAAG,wBAAwB,CAAA;IACvF,WAAW,EAAE,MAAM,CAAA;IACnB,eAAe,EAAE,MAAM,CAAA;IACvB,oBAAoB,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAA;CAChD;AAED;;GAEG;AACH,qBAAa,oBAAqB,SAAQ,YAAY;IACpD,OAAO,CAAC,MAAM,CAAkB;IAChC,OAAO,CAAC,gBAAgB,CAAyC;IACjE,OAAO,CAAC,iBAAiB,CAAyC;IAClE,OAAO,CAAC,iBAAiB,CAAuB;IAChD,OAAO,CAAC,oBAAoB,CAA6C;gBAE7D,MAAM,GAAE,OAAO,CAAC,gBAAgB,CAAM;IAelD;;OAEG;IACH,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,cAAc,CAAC,GAAG,IAAI;IAgBxE;;OAEG;IACG,YAAY,CAChB,SAAS,EAAE,MAAM,EACjB,IAAI,EAAE,aAAa,EACnB,cAAc,GAAE,cAAc,CAAC,gBAAgB,CAAkB,EACjE,WAAW,CAAC,EAAE,MAAM,GACnB,OAAO,CAAC,cAAc,CAAC;IAiD1B;;OAEG;IACG,mBAAmB,CAAC,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,GAAG,QAAQ,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAkB9G;;OAEG;IACG,kBAAkB,CAAC,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC;IA0BvF;;OAEG;IACG,kBAAkB,CAAC,YAAY,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAwB7E;;OAEG;IACH,sBAAsB,IAAI,mBAAmB;IAc7C;;OAEG;YACW,kBAAkB;IAuBhC,OAAO,CAAC,mBAAmB;IAqB3B,OAAO,CAAC,wBAAwB;IAoBhC,OAAO,CAAC,wBAAwB;IAUhC,OAAO,CAAC,4BAA4B;IAUpC,OAAO,CAAC,kBAAkB;YAeZ,6BAA6B;IAY3C,OAAO,CAAC,eAAe;YAOT,uBAAuB;IAuBrC,OAAO,CAAC,gBAAgB;YAYV,mBAAmB;IAKjC,OAAO,CAAC,6BAA6B;IAQrC,OAAO,CAAC,sBAAsB;IAkC9B,OAAO,CAAC,mBAAmB;IAkB3B,OAAO,CAAC,mCAAmC;IAkB3C,OAAO,CAAC,2BAA2B;CA6BpC;AAED,eAAe,oBAAoB,CAAA"}