/**
 * AG3NT Framework - Agent Discovery Service
 *
 * Intelligent agent discovery system that automatically finds, registers,
 * and manages agent instances across distributed environments.
 */
import { EventEmitter } from "events";
export interface AgentDiscoveryConfig {
    enableAutoDiscovery: boolean;
    discoveryInterval: number;
    healthCheckInterval: number;
    maxRetries: number;
    timeoutMs: number;
    enableServiceMesh: boolean;
    enableLoadBalancing: boolean;
}
export interface AgentInstance {
    agentId: string;
    agentType: string;
    version: string;
    capabilities: AgentCapability[];
    endpoint: string;
    status: 'healthy' | 'unhealthy' | 'unknown' | 'maintenance';
    metadata: AgentMetadata;
    performance: AgentPerformance;
    lastSeen: number;
    registeredAt: number;
}
export interface AgentCapability {
    name: string;
    version: string;
    proficiency: number;
    maxConcurrency: number;
    estimatedLatency: number;
    resourceRequirements: ResourceRequirements;
}
export interface ResourceRequirements {
    cpu: number;
    memory: number;
    network: number;
    storage: number;
}
export interface AgentMetadata {
    hostname: string;
    region: string;
    zone: string;
    environment: 'development' | 'staging' | 'production';
    tags: string[];
    customProperties: Record<string, any>;
}
export interface AgentPerformance {
    averageResponseTime: number;
    successRate: number;
    currentLoad: number;
    maxLoad: number;
    throughput: number;
    errorRate: number;
    lastUpdated: number;
}
export interface DiscoveryQuery {
    agentType?: string;
    capabilities?: string[];
    region?: string;
    environment?: string;
    tags?: string[];
    minProficiency?: number;
    maxLoad?: number;
    excludeAgents?: string[];
}
export interface DiscoveryResult {
    agents: AgentInstance[];
    totalFound: number;
    queryTime: number;
    recommendations: AgentRecommendation[];
}
export interface AgentRecommendation {
    agentId: string;
    score: number;
    reasoning: string[];
    estimatedPerformance: {
        responseTime: number;
        successProbability: number;
        loadImpact: number;
    };
}
export interface ServiceMeshConfig {
    enableServiceDiscovery: boolean;
    enableCircuitBreaker: boolean;
    enableRetries: boolean;
    enableLoadBalancing: boolean;
    meshProvider: 'istio' | 'linkerd' | 'consul' | 'custom';
}
/**
 * Agent Discovery Service
 */
export declare class AgentDiscoveryService extends EventEmitter {
    private config;
    private agents;
    private discoveryTimer?;
    private healthCheckTimer?;
    private serviceMesh?;
    constructor(config?: Partial<AgentDiscoveryConfig>);
    /**
     * Start discovery service
     */
    start(): Promise<void>;
    /**
     * Register agent instance
     */
    registerAgent(agent: Partial<AgentInstance>): Promise<void>;
    /**
     * Discover agents based on query
     */
    discoverAgents(query?: DiscoveryQuery): Promise<DiscoveryResult>;
    /**
     * Find best agent for task
     */
    findBestAgent(query: DiscoveryQuery): Promise<AgentInstance | null>;
    /**
     * Update agent performance metrics
     */
    updateAgentPerformance(agentId: string, performance: Partial<AgentPerformance>): void;
    /**
     * Update agent status
     */
    updateAgentStatus(agentId: string, status: AgentInstance['status']): void;
    /**
     * Unregister agent
     */
    unregisterAgent(agentId: string): void;
    /**
     * Get all registered agents
     */
    getAllAgents(): AgentInstance[];
    /**
     * Get agent by ID
     */
    getAgent(agentId: string): AgentInstance | undefined;
    /**
     * Get discovery statistics
     */
    getDiscoveryStats(): any;
    /**
     * Private helper methods
     */
    private startAutoDiscovery;
    private startHealthChecks;
    private performAutoDiscovery;
    private performHealthChecks;
    private checkAgentHealth;
    private pingAgent;
    private matchesQuery;
    private generateRecommendations;
    private calculateAgentScore;
    private calculateCapabilityScore;
    private generateRecommendationReasoning;
    /**
     * Shutdown discovery service
     */
    shutdown(): Promise<void>;
}
export default AgentDiscoveryService;
//# sourceMappingURL=agent-discovery-service.d.ts.map