/**
 * AG3NT Framework - Failover Manager
 *
 * Advanced failover management system with automatic detection, recovery,
 * and intelligent backup agent selection for high availability.
 */
import { EventEmitter } from "events";
import { AgentDiscoveryService } from "./agent-discovery-service";
import { LoadBalancer } from "./load-balancer";
export interface FailoverConfig {
    enableAutoFailover: boolean;
    enableGracefulShutdown: boolean;
    failureDetectionInterval: number;
    maxFailureThreshold: number;
    recoveryTimeout: number;
    backupAgentRatio: number;
    enableDataReplication: boolean;
    enableStateTransfer: boolean;
}
export interface FailoverEvent {
    eventId: string;
    type: 'agent_failure' | 'agent_recovery' | 'failover_triggered' | 'failover_completed';
    timestamp: number;
    primaryAgent: string;
    backupAgent?: string;
    reason: string;
    impact: FailoverImpact;
    recovery: FailoverRecovery;
}
export interface FailoverImpact {
    affectedRequests: number;
    estimatedDowntime: number;
    dataLoss: boolean;
    serviceDisruption: 'none' | 'minimal' | 'moderate' | 'severe';
}
export interface FailoverRecovery {
    strategy: 'immediate' | 'graceful' | 'manual';
    estimatedTime: number;
    requiresIntervention: boolean;
    rollbackPlan: string[];
}
export interface BackupAgent {
    agentId: string;
    primaryAgent: string;
    readinessLevel: 'cold' | 'warm' | 'hot';
    lastSyncTime: number;
    dataConsistency: number;
    activationTime: number;
}
export interface FailoverPlan {
    planId: string;
    primaryAgent: string;
    backupAgents: BackupAgent[];
    failoverTriggers: FailoverTrigger[];
    recoverySteps: RecoveryStep[];
    rollbackSteps: RollbackStep[];
    testSchedule: TestSchedule;
}
export interface FailoverTrigger {
    type: 'health_check_failure' | 'response_timeout' | 'error_rate_threshold' | 'manual';
    threshold: any;
    consecutiveFailures: number;
    timeWindow: number;
}
export interface RecoveryStep {
    stepId: string;
    order: number;
    action: 'activate_backup' | 'transfer_state' | 'redirect_traffic' | 'notify_stakeholders';
    parameters: any;
    timeout: number;
    rollbackOnFailure: boolean;
}
export interface RollbackStep {
    stepId: string;
    order: number;
    action: 'restore_primary' | 'transfer_state_back' | 'redirect_traffic_back';
    parameters: any;
    timeout: number;
}
export interface TestSchedule {
    frequency: 'daily' | 'weekly' | 'monthly';
    nextTest: number;
    lastTest?: number;
    testResults: TestResult[];
}
export interface TestResult {
    testId: string;
    timestamp: number;
    success: boolean;
    failoverTime: number;
    issues: string[];
    recommendations: string[];
}
export interface FailoverMetrics {
    totalFailovers: number;
    successfulFailovers: number;
    averageFailoverTime: number;
    averageRecoveryTime: number;
    dataLossIncidents: number;
    testSuccessRate: number;
    mtbf: number;
    mttr: number;
}
/**
 * Failover Manager
 */
export declare class FailoverManager extends EventEmitter {
    private config;
    private discoveryService;
    private loadBalancer;
    private failoverPlans;
    private backupAgents;
    private failoverHistory;
    private activeFailovers;
    private metrics;
    private monitoringTimer?;
    constructor(discoveryService: AgentDiscoveryService, loadBalancer: LoadBalancer, config?: Partial<FailoverConfig>);
    /**
     * Start failover monitoring
     */
    start(): void;
    /**
     * Create failover plan for agent
     */
    createFailoverPlan(primaryAgentId: string): Promise<FailoverPlan>;
    /**
     * Trigger failover for agent
     */
    triggerFailover(primaryAgentId: string, reason: string, manual?: boolean): Promise<FailoverEvent>;
    /**
     * Recover primary agent
     */
    recoverPrimaryAgent(primaryAgentId: string): Promise<void>;
    /**
     * Test failover plan
     */
    testFailoverPlan(primaryAgentId: string): Promise<TestResult>;
    /**
     * Get failover metrics
     */
    getFailoverMetrics(): FailoverMetrics;
    /**
     * Get failover plans
     */
    getFailoverPlans(): Map<string, FailoverPlan>;
    /**
     * Private helper methods
     */
    private findBackupAgents;
    private createDefaultTriggers;
    private createRecoverySteps;
    private createRollbackSteps;
    private executeRecoverySteps;
    private executeRecoveryStep;
    private executeRollbackStep;
    private startFailureDetection;
    private detectFailures;
    private calculateNextTestTime;
    private updateMetrics;
    private setupEventHandlers;
    /**
     * Shutdown failover manager
     */
    shutdown(): void;
}
export default FailoverManager;
//# sourceMappingURL=failover-manager.d.ts.map