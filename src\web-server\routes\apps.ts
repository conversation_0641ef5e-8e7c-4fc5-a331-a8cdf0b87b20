import { Router } from 'express';
import { Server } from 'socket.io';
import { z } from 'zod';
import log from 'electron-log';
import { db } from '../../db';
import { apps, chats } from '../../db/schema';
import { eq } from 'drizzle-orm';
import fs from 'fs';
import { getDyadAppPath } from '../../paths/paths';
import { copyDirectoryRecursive } from '../../ipc/utils/file_utils';
import git from 'isomorphic-git';
import { spawn, ChildProcess } from 'child_process';
import path from 'path';
import { killProcess } from '../../ipc/utils/process_manager';

const logger = log.scope('web-server:apps');

// Simple process management for web server
interface RunningAppInfo {
  process: ChildProcess;
  port: number;
  url: string;
}

const runningApps = new Map<number, RunningAppInfo>();

// Helper function to find available port starting from 3001
async function findAvailablePort(startPort: number = 3001): Promise<number> {
  const net = await import('net');

  return new Promise((resolve, reject) => {
    const server = net.createServer();
    server.listen(startPort, () => {
      const port = (server.address() as any)?.port;
      server.close(() => resolve(port));
    });
    server.on('error', () => {
      // Port is busy, try next one
      findAvailablePort(startPort + 1).then(resolve).catch(reject);
    });
  });
}

// Helper function to kill process
function killProcess(process: ChildProcess): Promise<void> {
  return new Promise((resolve) => {
    if (process.killed || process.exitCode !== null) {
      resolve();
      return;
    }

    process.kill('SIGTERM');

    // Give it 5 seconds to gracefully shut down
    const timeout = setTimeout(() => {
      if (!process.killed && process.exitCode === null) {
        process.kill('SIGKILL');
      }
    }, 5000);

    process.on('exit', () => {
      clearTimeout(timeout);
      resolve();
    });
  });
}

// Validation schemas
const CreateAppSchema = z.object({
  name: z.string().min(1),
  template: z.string().optional(),
});

const UpdateAppSchema = z.object({
  name: z.string().optional(),
  supabaseProjectId: z.string().optional(),
});

export function setupAppRoutes(io: Server) {
  const router = Router();

  // GET /api/apps - List all apps
  router.get('/', async (req, res) => {
    try {
      const allApps = await db.query.apps.findMany({
        orderBy: (apps, { desc }) => [desc(apps.createdAt)],
      });
      
      res.json(allApps);
    } catch (error) {
      logger.error('Error listing apps:', error);
      res.status(500).json({ error: 'Failed to list apps' });
    }
  });

  // GET /api/apps/:id - Get specific app
  router.get('/:id', async (req, res) => {
    try {
      const appId = parseInt(req.params.id);
      if (isNaN(appId)) {
        return res.status(400).json({ error: 'Invalid app ID' });
      }

      const app = await db.query.apps.findFirst({
        where: eq(apps.id, appId),
      });

      if (!app) {
        return res.status(404).json({ error: 'App not found' });
      }

      res.json(app);
    } catch (error) {
      logger.error('Error getting app:', error);
      res.status(500).json({ error: 'Failed to get app' });
    }
  });

  // POST /api/apps - Create new app
  router.post('/', async (req, res) => {
    try {
      const params = CreateAppSchema.parse(req.body);
      const appPath = params.name;
      const fullAppPath = getDyadAppPath(appPath);
      
      if (fs.existsSync(fullAppPath)) {
        return res.status(400).json({ error: `App already exists at: ${fullAppPath}` });
      }

      // Create a new app
      const [app] = await db
        .insert(apps)
        .values({
          name: params.name,
          path: appPath,
        })
        .returning();

      // Create app from template (simplified for web server)
      const scaffoldPath = path.join(__dirname, '..', '..', '..', 'scaffold');

      // Check if scaffold directory exists
      if (!fs.existsSync(scaffoldPath)) {
        throw new Error(`Scaffold directory not found at: ${scaffoldPath}`);
      }

      await copyDirectoryRecursive(scaffoldPath, fullAppPath);

      // Initialize git repo and create first commit
      await git.init({
        fs: fs,
        dir: fullAppPath,
        defaultBranch: "main",
      });

      // Stage all files
      await git.add({
        fs: fs,
        dir: fullAppPath,
        filepath: ".",
      });

      // Create initial commit
      const commitResult = await git.commit({
        fs: fs,
        dir: fullAppPath,
        author: {
          name: "Dyad",
          email: "<EMAIL>",
        },
        message: "Init Dyad app",
      });

      // DYAD-STYLE: Create primary chat for this app (1 project = 1 chat)
      const [primaryChat] = await db
        .insert(chats)
        .values({
          appId: app.id,
          title: app.name, // Use app name as chat title
          initialCommitHash: commitResult,
        })
        .returning();

      logger.info(`Created app: ${params.name} at ${fullAppPath} with primary chat: ${primaryChat.id}`);

      // Return app with chatId for compatibility with native Dyad
      res.status(201).json({
        ...app,
        chatId: primaryChat.id
      });
    } catch (error) {
      logger.error('Error creating app:', error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: 'Invalid request data', details: error.errors });
      } else {
        res.status(500).json({ error: 'Failed to create app' });
      }
    }
  });

  // PUT /api/apps/:id - Update app
  router.put('/:id', async (req, res) => {
    try {
      const appId = parseInt(req.params.id);
      if (isNaN(appId)) {
        return res.status(400).json({ error: 'Invalid app ID' });
      }

      const updates = UpdateAppSchema.parse(req.body);
      
      const [updatedApp] = await db
        .update(apps)
        .set(updates)
        .where(eq(apps.id, appId))
        .returning();

      if (!updatedApp) {
        return res.status(404).json({ error: 'App not found' });
      }

      res.json(updatedApp);
    } catch (error) {
      logger.error('Error updating app:', error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: 'Invalid request data', details: error.errors });
      } else {
        res.status(500).json({ error: 'Failed to update app' });
      }
    }
  });

  // DELETE /api/apps/:id - Delete app
  router.delete('/:id', async (req, res) => {
    try {
      const appId = parseInt(req.params.id);
      if (isNaN(appId)) {
        return res.status(400).json({ error: 'Invalid app ID' });
      }

      const app = await db.query.apps.findFirst({
        where: eq(apps.id, appId),
      });

      if (!app) {
        return res.status(404).json({ error: 'App not found' });
      }

      // Delete app from database
      await db.delete(apps).where(eq(apps.id, appId));

      // Delete app directory if it exists
      if (app.path) {
        const fullAppPath = getDyadAppPath(app.path);
        if (fs.existsSync(fullAppPath)) {
          fs.rmSync(fullAppPath, { recursive: true, force: true });
        }
      }

      logger.info(`Deleted app: ${app.name}`);
      res.json({ message: 'App deleted successfully' });
    } catch (error) {
      logger.error('Error deleting app:', error);
      res.status(500).json({ error: 'Failed to delete app' });
    }
  });

  // POST /api/apps/:id/start - Start app
  router.post('/:id/start', async (req, res) => {
    try {
      const appId = parseInt(req.params.id);
      if (isNaN(appId)) {
        return res.status(400).json({ error: 'Invalid app ID' });
      }

      // Check if app is already running
      if (runningApps.has(appId)) {
        const appInfo = runningApps.get(appId)!;
        return res.json({
          message: 'App already running',
          appId,
          url: appInfo.url
        });
      }

      const app = await db.query.apps.findFirst({
        where: eq(apps.id, appId),
      });

      if (!app) {
        return res.status(404).json({ error: 'App not found' });
      }

      const appPath = getDyadAppPath(app.path);

      if (!fs.existsSync(appPath)) {
        return res.status(404).json({ error: 'App directory not found' });
      }

      // Find available port
      const port = await findAvailablePort(3001 + appId);
      const url = `http://localhost:${port}`;

      logger.info(`Starting app: ${app.name} on port ${port}`);

      // Start the development server
      const process = spawn(
        `(pnpm install && pnpm run dev --port ${port}) || (npm install --legacy-peer-deps && npm run dev -- --port ${port})`,
        [],
        {
          cwd: appPath,
          shell: true,
          stdio: 'pipe',
          detached: false,
        }
      );

      if (!process.pid) {
        throw new Error('Failed to spawn process');
      }

      // Store the running app info
      runningApps.set(appId, { process, port, url });

      // Handle process events
      process.on('exit', (code, signal) => {
        logger.info(`App ${appId} process exited with code ${code}, signal ${signal}`);
        runningApps.delete(appId);
        // Notify clients via Socket.IO
        io.emit('app-status-changed', {
          appId,
          isRunning: false,
          url: null,
          exitCode: code,
          signal
        });
      });

      process.on('error', (error) => {
        logger.error(`App ${appId} process error:`, error);
        runningApps.delete(appId);
        // Notify clients via Socket.IO
        io.emit('app-status-changed', {
          appId,
          isRunning: false,
          url: null,
          error: error.message
        });
      });

      // Give the app a moment to start up
      setTimeout(() => {
        // Notify clients via Socket.IO
        io.emit('app-status-changed', {
          appId,
          isRunning: true,
          url,
          port
        });

        res.json({
          message: 'App started successfully',
          appId,
          url,
          port
        });
      }, 2000);

    } catch (error) {
      logger.error('Error starting app:', error);
      res.status(500).json({ error: 'Failed to start app' });
    }
  });

  // POST /api/apps/:id/stop - Stop app
  router.post('/:id/stop', async (req, res) => {
    try {
      const appId = parseInt(req.params.id);
      if (isNaN(appId)) {
        return res.status(400).json({ error: 'Invalid app ID' });
      }

      const appInfo = runningApps.get(appId);
      if (!appInfo) {
        return res.json({ message: 'App not running', appId });
      }

      logger.info(`Stopping app: ${appId}`);

      await killProcess(appInfo.process);
      runningApps.delete(appId);

      // Notify clients via Socket.IO
      io.emit('app-status-changed', {
        appId,
        isRunning: false,
        url: null
      });

      res.json({ message: 'App stopped successfully', appId });
    } catch (error) {
      logger.error('Error stopping app:', error);
      res.status(500).json({ error: 'Failed to stop app' });
    }
  });

  // GET /api/apps/:id/status - Get app status
  router.get('/:id/status', async (req, res) => {
    try {
      const appId = parseInt(req.params.id);
      if (isNaN(appId)) {
        return res.status(400).json({ error: 'Invalid app ID' });
      }

      const app = await db.query.apps.findFirst({
        where: eq(apps.id, appId),
      });

      if (!app) {
        return res.status(404).json({ error: 'App not found' });
      }

      const appInfo = runningApps.get(appId);

      if (appInfo) {
        // Check if process is still alive
        if (appInfo.process.killed || appInfo.process.exitCode !== null) {
          runningApps.delete(appId);
          res.json({
            isRunning: false,
            url: null,
            appId
          });
        } else {
          res.json({
            isRunning: true,
            url: appInfo.url,
            port: appInfo.port,
            appId
          });
        }
      } else {
        res.json({
          isRunning: false,
          url: null,
          appId
        });
      }
    } catch (error) {
      logger.error('Error getting app status:', error);
      res.status(500).json({ error: 'Failed to get app status' });
    }
  });

  // GET /api/apps/:id/files - List app files or read specific file
  router.get('/:id/files', async (req, res) => {
    try {
      const appId = parseInt(req.params.id);
      if (isNaN(appId)) {
        return res.status(400).json({ error: 'Invalid app ID' });
      }

      const filePath = req.query.path as string;
      logger.info(`File request - App ID: ${appId}, File Path: "${filePath}"`);

      const app = await db.query.apps.findFirst({
        where: eq(apps.id, appId),
      });

      if (!app || !app.path) {
        return res.status(404).json({ error: 'App not found' });
      }

      const appPath = getDyadAppPath(app.path);

      // If no file path provided, list all files in the app directory
      if (!filePath) {
        if (!fs.existsSync(appPath)) {
          return res.json([]); // Return empty array if app directory doesn't exist
        }

        // Recursively get all files in the app directory
        const getAllFiles = (dirPath: string, relativePath = ''): string[] => {
          const files: string[] = [];
          const items = fs.readdirSync(dirPath);

          for (const item of items) {
            const fullPath = require('path').join(dirPath, item);
            const relativeItemPath = relativePath ? `${relativePath}/${item}` : item;

            // Skip hidden files and node_modules
            if (item.startsWith('.') || item === 'node_modules') {
              continue;
            }

            const stat = fs.statSync(fullPath);
            if (stat.isDirectory()) {
              files.push(...getAllFiles(fullPath, relativeItemPath));
            } else {
              files.push(relativeItemPath);
            }
          }
          return files;
        };

        const allFiles = getAllFiles(appPath);
        return res.json(allFiles);
      }

      // If file path provided, read the specific file
      const appBasePath = getDyadAppPath(app.path);
      const fullFilePath = path.join(appBasePath, filePath);

      logger.info(`Attempting to read file: ${filePath} -> ${fullFilePath}`);

      if (!fs.existsSync(fullFilePath)) {
        logger.warn(`File not found: ${fullFilePath}`);
        return res.status(404).json({ error: 'File not found' });
      }

      // Check if it's a directory
      const stat = fs.statSync(fullFilePath);
      logger.info(`File stats for ${fullFilePath}: isDirectory=${stat.isDirectory()}, isFile=${stat.isFile()}`);

      if (stat.isDirectory()) {
        logger.warn(`Attempted to read directory as file: ${fullFilePath}`);
        return res.status(400).json({ error: 'Path is a directory, not a file' });
      }

      const content = fs.readFileSync(fullFilePath, 'utf-8');
      res.json({ content, path: filePath });
    } catch (error) {
      logger.error('Error reading app file:', error);
      res.status(500).json({ error: 'Failed to read file' });
    }
  });

  // PUT /api/apps/:id/files - Write app file (path in query param)
  router.put('/:id/files', async (req, res) => {
    try {
      const appId = parseInt(req.params.id);
      if (isNaN(appId)) {
        return res.status(400).json({ error: 'Invalid app ID' });
      }

      const filePath = req.query.path as string;
      if (!filePath) {
        return res.status(400).json({ error: 'File path is required' });
      }
      const { content } = req.body;

      if (typeof content !== 'string') {
        return res.status(400).json({ error: 'Content must be a string' });
      }

      const app = await db.query.apps.findFirst({
        where: eq(apps.id, appId),
      });

      if (!app || !app.path) {
        return res.status(404).json({ error: 'App not found' });
      }

      const appBasePath = getDyadAppPath(app.path);
      const fullFilePath = path.join(appBasePath, filePath);

      // Ensure directory exists
      const dir = require('path').dirname(fullFilePath);
      fs.mkdirSync(dir, { recursive: true });

      fs.writeFileSync(fullFilePath, content, 'utf-8');

      // Notify clients via Socket.IO
      io.emit('file-changed', {
        appId,
        filePath,
        action: 'updated'
      });

      logger.info(`Updated file: ${filePath} in app ${app.name}`);
      res.json({ message: 'File updated successfully', path: filePath });
    } catch (error) {
      logger.error('Error writing app file:', error);
      res.status(500).json({ error: 'Failed to write file' });
    }
  });

  return router;
}
