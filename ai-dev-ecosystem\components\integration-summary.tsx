"use client"

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  CheckCircle, 
  Zap, 
  Server, 
  Globe, 
  MessageSquare, 
  Folder,
  Code,
  Settings,
  ExternalLink
} from 'lucide-react';

export default function IntegrationSummary() {
  const features = [
    {
      name: 'Backend API Server',
      description: 'Express.js server with REST endpoints',
      status: 'active',
      url: 'http://localhost:3002',
      icon: Server,
    },
    {
      name: 'Frontend Web App',
      description: 'Next.js application with Dyad integration',
      status: 'active',
      url: 'http://localhost:3001',
      icon: Globe,
    },
    {
      name: 'App Management',
      description: 'Create, manage, and deploy applications',
      status: 'ready',
      icon: Folder,
    },
    {
      name: 'AI Chat Interface',
      description: 'Real-time chat with language models',
      status: 'ready',
      icon: MessageSquare,
    },
    {
      name: 'Code Editor',
      description: 'File browser and code editing (coming soon)',
      status: 'planned',
      icon: Code,
    },
    {
      name: 'Settings Management',
      description: 'Configure API keys and preferences',
      status: 'ready',
      icon: Settings,
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-900/20 text-green-400 border-green-800';
      case 'ready':
        return 'bg-blue-900/20 text-blue-400 border-blue-800';
      case 'planned':
        return 'bg-yellow-900/20 text-yellow-400 border-yellow-800';
      default:
        return 'bg-gray-900/20 text-gray-400 border-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Running';
      case 'ready':
        return 'Ready';
      case 'planned':
        return 'Planned';
      default:
        return 'Unknown';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-gradient-to-r from-blue-900/20 to-purple-900/20 border-blue-800/30">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Zap className="w-6 h-6 text-blue-500" />
            Dyad Integration Complete!
          </CardTitle>
          <CardDescription className="text-blue-200">
            Your ai-dev-ecosystem is now fully connected to the Dyad backend. 
            You can create apps, chat with AI, and manage your development workflow.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-500" />
            <span className="text-green-400 font-medium">Integration Successful</span>
            <Badge variant="secondary" className="bg-green-900/20 text-green-400 border-green-800">
              All Systems Operational
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Features Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {features.map((feature) => {
          const Icon = feature.icon;
          return (
            <Card key={feature.name} className="bg-[#0a0a0a] border-[#1a1a1a] hover:border-[#333] transition-colors">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Icon className="w-5 h-5 text-blue-500" />
                    <CardTitle className="text-sm font-medium text-white">
                      {feature.name}
                    </CardTitle>
                  </div>
                  <Badge 
                    variant="secondary" 
                    className={`text-xs ${getStatusColor(feature.status)}`}
                  >
                    {getStatusText(feature.status)}
                  </Badge>
                </div>
                <CardDescription className="text-xs text-[#666]">
                  {feature.description}
                </CardDescription>
              </CardHeader>
              {feature.url && (
                <CardContent className="pt-0">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => window.open(feature.url, '_blank')}
                    className="h-7 px-2 bg-[#1a1a1a] border-[#333] text-[#888] hover:text-white hover:bg-[#2a2a2a] text-xs"
                  >
                    <ExternalLink className="w-3 h-3 mr-1" />
                    Open
                  </Button>
                </CardContent>
              )}
            </Card>
          );
        })}
      </div>

      {/* Quick Start Guide */}
      <Card className="bg-[#0a0a0a] border-[#1a1a1a]">
        <CardHeader>
          <CardTitle className="text-white">Quick Start Guide</CardTitle>
          <CardDescription className="text-[#666]">
            Get started with your integrated development environment
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">
                1
              </div>
              <div>
                <p className="text-sm text-white font-medium">Test the Connection</p>
                <p className="text-xs text-[#666]">
                  Use the Test tab to verify the backend connection and API functionality
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">
                2
              </div>
              <div>
                <p className="text-sm text-white font-medium">Create Your First App</p>
                <p className="text-xs text-[#666]">
                  Go to the Apps tab and create a new project using one of the available templates
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">
                3
              </div>
              <div>
                <p className="text-sm text-white font-medium">Start Chatting with AI</p>
                <p className="text-xs text-[#666]">
                  Select your app and use the Chat tab to get AI assistance with development
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">
                4
              </div>
              <div>
                <p className="text-sm text-white font-medium">Explore Advanced Features</p>
                <p className="text-xs text-[#666]">
                  Use streaming responses, manage multiple projects, and customize your workflow
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Technical Details */}
      <Card className="bg-[#0a0a0a] border-[#1a1a1a]">
        <CardHeader>
          <CardTitle className="text-white text-sm">Technical Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-2 text-xs">
            <div className="flex justify-between">
              <span className="text-[#666]">Frontend:</span>
              <span className="text-white">Next.js 15.2.4 + React 19</span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#666]">Backend:</span>
              <span className="text-white">Express.js + Socket.IO</span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#666]">Database:</span>
              <span className="text-white">SQLite + Drizzle ORM</span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#666]">AI Models:</span>
              <span className="text-white">OpenRouter + Multiple Providers</span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#666]">Communication:</span>
              <span className="text-white">REST API + Server-Sent Events</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
