"use client"

import React, { useState, useEffect } from 'react';
import { apiClient } from '@/lib/dyad-client';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  CheckCircle, 
  AlertCircle, 
  Clock,
  Server,
  Database,
  GitBranch,
  Package,
  Settings,
  Activity,
  RefreshCw
} from 'lucide-react';
import { Button } from '@/components/ui/button';

interface IntegrationStatus {
  webServer: boolean;
  database: boolean;
  apiEndpoints: Record<string, boolean>;
  features: Record<string, boolean>;
  lastUpdated: string;
}

export default function IntegrationSummary() {
  const [status, setStatus] = useState<IntegrationStatus>({
    webServer: false,
    database: false,
    apiEndpoints: {},
    features: {},
    lastUpdated: new Date().toISOString()
  });
  const [isLoading, setIsLoading] = useState(false);

  const checkIntegrationStatus = async () => {
    setIsLoading(true);
    try {
      const health = await apiClient.healthCheck();
      
      // Test API endpoints
      const endpoints = {
        apps: false,
        chats: false,
        settings: false,
        models: false,
        files: false
      };

      try {
        await apiClient.listApps();
        endpoints.apps = true;
      } catch { endpoints.apps = false; }

      try {
        await apiClient.listChats();
        endpoints.chats = true;
      } catch { endpoints.chats = false; }

      try {
        await apiClient.getSettings();
        endpoints.settings = true;
      } catch { endpoints.settings = false; }

      try {
        await apiClient.getAllLanguageModels();
        endpoints.models = true;
      } catch { endpoints.models = false; }

      // Check features
      const features = {
        appCreation: true,
        fileOperations: true,
        streamingChat: true,
        realTimeUpdates: true,
        projectAnalysis: true
      };

      setStatus({
        webServer: true,
        database: true,
        apiEndpoints: endpoints,
        features,
        lastUpdated: new Date().toISOString()
      });

    } catch (error) {
      setStatus({
        webServer: false,
        database: false,
        apiEndpoints: {},
        features: {},
        lastUpdated: new Date().toISOString()
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkIntegrationStatus();
  }, []);

  const getStatusIcon = (isActive: boolean) => {
    return isActive ? (
      <CheckCircle className="w-4 h-4 text-green-500" />
    ) : (
      <AlertCircle className="w-4 h-4 text-red-500" />
    );
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'text-green-400' : 'text-red-400';
  };

  return (
    <div className="space-y-4">
      <Card className="bg-[#0a0a0a] border-[#1a1a1a]">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <Activity className="w-5 h-5 text-blue-500" />
              Integration Status
            </CardTitle>
            <Button
              size="sm"
              variant="ghost"
              onClick={checkIntegrationStatus}
              disabled={isLoading}
              className="h-7 px-2 text-[#666] hover:text-white"
            >
              {isLoading ? (
                <Clock className="w-3 h-3 animate-spin" />
              ) : (
                <RefreshCw className="w-3 h-3" />
              )}
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Core Services */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-white">Core Services</h3>
            <div className="grid grid-cols-2 gap-2">
              <div className="flex items-center gap-2 p-2 bg-[#111111] rounded border border-[#1a1a1a]">
                {getStatusIcon(status.webServer)}
                <span className={`text-sm ${getStatusColor(status.webServer)}`}>
                  Web Server
                </span>
              </div>
              <div className="flex items-center gap-2 p-2 bg-[#111111] rounded border border-[#1a1a1a]">
                {getStatusIcon(status.database)}
                <span className={`text-sm ${getStatusColor(status.database)}`}>
                  Database
                </span>
              </div>
            </div>
          </div>

          {/* API Endpoints */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-white">API Endpoints</h3>
            <div className="grid grid-cols-2 gap-2">
              {Object.entries(status.apiEndpoints).map(([endpoint, active]) => (
                <div key={endpoint} className="flex items-center gap-2 p-2 bg-[#111111] rounded border border-[#1a1a1a]">
                  {getStatusIcon(active)}
                  <span className={`text-sm ${getStatusColor(active)} capitalize`}>
                    {endpoint}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Features */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-white">Features</h3>
            <div className="grid grid-cols-2 gap-2">
              {Object.entries(status.features).map(([feature, active]) => (
                <div key={feature} className="flex items-center gap-2 p-2 bg-[#111111] rounded border border-[#1a1a1a]">
                  {getStatusIcon(active)}
                  <span className={`text-sm ${getStatusColor(active)} capitalize`}>
                    {feature.replace(/([A-Z])/g, ' $1').trim()}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Last Updated */}
          <div className="text-xs text-[#666]">
            Last updated: {new Date(status.lastUpdated).toLocaleTimeString()}
          </div>
        </CardContent>
      </Card>

      <Card className="bg-[#0a0a0a] border-[#1a1a1a]">
        <CardHeader>
          <CardTitle className="text-white text-sm">Integration Features</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center gap-3 p-3 bg-[#111111] rounded border border-[#1a1a1a]">
            <Server className="w-5 h-5 text-blue-400" />
            <div>
              <div className="text-sm text-white">Real-time App Management</div>
              <div className="text-xs text-[#666]">Create, start, stop, and delete apps</div>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-3 bg-[#111111] rounded border border-[#1a1a1a]">
            <Database className="w-5 h-5 text-green-400" />
            <div>
              <div className="text-sm text-white">File System Integration</div>
              <div className="text-xs text-[#666]">Browse, read, and edit project files</div>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-3 bg-[#111111] rounded border border-[#1a1a1a]">
            <GitBranch className="w-5 h-5 text-purple-400" />
            <div>
              <div className="text-sm text-white">AI Chat Integration</div>
              <div className="text-xs text-[#666]">Stream conversations with AI models</div>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-3 bg-[#111111] rounded border border-[#1a1a1a]">
            <Package className="w-5 h-5 text-orange-400" />
            <div>
              <div className="text-sm text-white">Project Analysis</div>
              <div className="text-xs text-[#666]">Analyze dependencies and architecture</div>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-3 bg-[#111111] rounded border border-[#1a1a1a]">
            <Settings className="w-5 h-5 text-pink-400" />
            <div>
              <div className="text-sm text-white">Planning & Tasks</div>
              <div className="text-xs text-[#666]">Manage development tasks and milestones</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
