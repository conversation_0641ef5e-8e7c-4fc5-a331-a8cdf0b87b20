{"version": 3, "file": "consensus-protocol-engine.d.ts", "sourceRoot": "", "sources": ["consensus-protocol-engine.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAA;AAErC,MAAM,WAAW,eAAe;IAC9B,eAAe,EAAE,UAAU,GAAG,eAAe,GAAG,WAAW,GAAG,UAAU,GAAG,WAAW,CAAA;IACtF,eAAe,EAAE,MAAM,CAAA;IACvB,aAAa,EAAE,MAAM,CAAA;IACrB,qBAAqB,EAAE,OAAO,CAAA;IAC9B,gBAAgB,EAAE,OAAO,CAAA;IACzB,0BAA0B,EAAE,YAAY,GAAG,WAAW,GAAG,UAAU,GAAG,QAAQ,CAAA;CAC/E;AAED,MAAM,WAAW,iBAAiB;IAChC,UAAU,EAAE,MAAM,CAAA;IAClB,UAAU,EAAE,MAAM,CAAA;IAClB,KAAK,EAAE,MAAM,CAAA;IACb,WAAW,EAAE,MAAM,CAAA;IACnB,IAAI,EAAE,UAAU,GAAG,qBAAqB,GAAG,iBAAiB,GAAG,eAAe,GAAG,WAAW,CAAA;IAC5F,OAAO,EAAE,cAAc,EAAE,CAAA;IACzB,WAAW,EAAE,WAAW,CAAA;IACxB,OAAO,EAAE,eAAe,CAAA;IACxB,MAAM,EAAE,OAAO,GAAG,QAAQ,GAAG,WAAW,GAAG,QAAQ,GAAG,WAAW,CAAA;IACjE,SAAS,EAAE,MAAM,CAAA;IACjB,cAAc,EAAE,MAAM,CAAA;IACtB,MAAM,CAAC,EAAE,eAAe,CAAA;CACzB;AAED,MAAM,WAAW,cAAc;IAC7B,QAAQ,EAAE,MAAM,CAAA;IAChB,KAAK,EAAE,MAAM,CAAA;IACb,WAAW,EAAE,MAAM,CAAA;IACnB,MAAM,EAAE,YAAY,CAAA;IACpB,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,CAAA;IAC/B,WAAW,EAAE,MAAM,CAAA;IACnB,cAAc,EAAE,GAAG,CAAA;CACpB;AAED,MAAM,WAAW,YAAY;IAC3B,KAAK,EAAE,MAAM,EAAE,CAAA;IACf,SAAS,EAAE,OAAO,GAAG,UAAU,GAAG,OAAO,GAAG,UAAU,CAAA;IACtD,aAAa,EAAE,OAAO,CAAA;IACtB,SAAS,EAAE,MAAM,CAAA;IACjB,YAAY,EAAE,MAAM,EAAE,CAAA;CACvB;AAED,MAAM,WAAW,WAAW;IAC1B,QAAQ,EAAE,UAAU,GAAG,eAAe,GAAG,WAAW,GAAG,UAAU,GAAG,WAAW,CAAA;IAC/E,MAAM,EAAE,MAAM,CAAA;IACd,eAAe,EAAE,OAAO,CAAA;IACxB,eAAe,EAAE,OAAO,CAAA;IACxB,iBAAiB,CAAC,EAAE,iBAAiB,CAAA;IACrC,UAAU,EAAE,SAAS,EAAE,CAAA;IACvB,iBAAiB,EAAE,QAAQ,GAAG,kBAAkB,GAAG,UAAU,GAAG,YAAY,CAAA;CAC7E;AAED,MAAM,WAAW,iBAAiB;IAChC,eAAe,EAAE,MAAM,CAAA;IACvB,eAAe,EAAE,MAAM,CAAA;IACvB,WAAW,EAAE,MAAM,CAAA;IACnB,WAAW,EAAE,MAAM,CAAA;IACnB,gBAAgB,EAAE,MAAM,CAAA;CACzB;AAED,MAAM,WAAW,SAAS;IACxB,OAAO,EAAE,MAAM,CAAA;IACf,KAAK,EAAE,MAAM,EAAE,CAAA;IACf,qBAAqB,EAAE,OAAO,CAAA;IAC9B,UAAU,EAAE,OAAO,CAAA;CACpB;AAED,MAAM,WAAW,eAAe;IAC9B,OAAO,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAA;IAC/C,YAAY,EAAE,MAAM,EAAE,CAAA;IACtB,eAAe,EAAE,MAAM,EAAE,CAAA;IACzB,aAAa,EAAE,MAAM,EAAE,CAAA;IACvB,WAAW,EAAE,GAAG,CAAA;IAChB,gBAAgB,EAAE,MAAM,EAAE,CAAA;CAC3B;AAED,MAAM,WAAW,SAAS;IACxB,MAAM,EAAE,MAAM,CAAA;IACd,OAAO,EAAE,MAAM,CAAA;IACf,UAAU,EAAE,MAAM,CAAA;IAClB,QAAQ,EAAE,MAAM,CAAA;IAChB,QAAQ,EAAE,SAAS,GAAG,QAAQ,GAAG,SAAS,GAAG,MAAM,CAAA;IACnD,MAAM,EAAE,MAAM,CAAA;IACd,UAAU,EAAE,MAAM,CAAA;IAClB,SAAS,EAAE,MAAM,CAAA;IACjB,UAAU,CAAC,EAAE,aAAa,EAAE,CAAA;IAC5B,SAAS,EAAE,MAAM,CAAA;IACjB,aAAa,CAAC,EAAE,MAAM,CAAA;CACvB;AAED,MAAM,WAAW,aAAa;IAC5B,IAAI,EAAE,aAAa,GAAG,YAAY,GAAG,cAAc,CAAA;IACnD,SAAS,EAAE,MAAM,CAAA;IACjB,SAAS,CAAC,EAAE,MAAM,CAAA;CACnB;AAED,MAAM,WAAW,eAAe;IAC9B,QAAQ,EAAE,MAAM,CAAA;IAChB,aAAa,EAAE,MAAM,CAAA;IACrB,aAAa,EAAE,aAAa,CAAA;IAC5B,cAAc,EAAE,MAAM,CAAA;IACtB,OAAO,EAAE,eAAe,CAAA;IACxB,cAAc,EAAE,kBAAkB,CAAA;IAClC,OAAO,EAAE,MAAM,EAAE,CAAA;CAClB;AAED,MAAM,WAAW,aAAa;IAC5B,mBAAmB,EAAE,MAAM,CAAA;IAC3B,UAAU,EAAE,MAAM,CAAA;IAClB,iBAAiB,EAAE,MAAM,CAAA;IACzB,aAAa,EAAE,YAAY,EAAE,CAAA;IAC7B,cAAc,EAAE,MAAM,CAAA;IACtB,cAAc,EAAE,MAAM,CAAA;CACvB;AAED,MAAM,WAAW,YAAY;IAC3B,QAAQ,EAAE,MAAM,CAAA;IAChB,KAAK,EAAE,MAAM,CAAA;IACb,aAAa,EAAE,MAAM,CAAA;IACrB,UAAU,EAAE,MAAM,CAAA;IAClB,OAAO,EAAE,SAAS,EAAE,CAAA;CACrB;AAED,MAAM,WAAW,eAAe;IAC9B,YAAY,EAAE,MAAM,CAAA;IACpB,aAAa,EAAE,MAAM,EAAE,CAAA;IACvB,gBAAgB,EAAE,MAAM,EAAE,CAAA;IAC1B,kBAAkB,EAAE,OAAO,CAAA;IAC3B,kBAAkB,EAAE,MAAM,EAAE,CAAA;CAC7B;AAED,MAAM,WAAW,kBAAkB;IACjC,MAAM,EAAE,mBAAmB,EAAE,CAAA;IAC7B,QAAQ,EAAE,MAAM,CAAA;IAChB,gBAAgB,EAAE,mBAAmB,EAAE,CAAA;IACvC,cAAc,EAAE,aAAa,EAAE,CAAA;IAC/B,YAAY,EAAE,YAAY,CAAA;CAC3B;AAED,MAAM,WAAW,mBAAmB;IAClC,OAAO,EAAE,MAAM,CAAA;IACf,IAAI,EAAE,MAAM,CAAA;IACZ,WAAW,EAAE,MAAM,CAAA;IACnB,eAAe,EAAE,MAAM,EAAE,CAAA;IACzB,YAAY,EAAE,MAAM,EAAE,CAAA;IACtB,QAAQ,EAAE,MAAM,CAAA;IAChB,YAAY,EAAE,MAAM,EAAE,CAAA;CACvB;AAED,MAAM,WAAW,mBAAmB;IAClC,OAAO,EAAE,MAAM,CAAA;IACf,IAAI,EAAE,MAAM,CAAA;IACZ,KAAK,EAAE,MAAM,EAAE,CAAA;IACf,SAAS,EAAE,MAAM,EAAE,CAAA;IACnB,cAAc,EAAE,MAAM,EAAE,CAAA;CACzB;AAED,MAAM,WAAW,aAAa;IAC5B,IAAI,EAAE,MAAM,CAAA;IACZ,MAAM,EAAE,MAAM,CAAA;IACd,WAAW,EAAE,MAAM,CAAA;IACnB,QAAQ,EAAE,MAAM,CAAA;CACjB;AAED,MAAM,WAAW,YAAY;IAC3B,QAAQ,EAAE,MAAM,EAAE,CAAA;IAClB,KAAK,EAAE,MAAM,EAAE,CAAA;IACf,SAAS,EAAE,MAAM,CAAA;IACjB,gBAAgB,EAAE,OAAO,CAAA;CAC1B;AAED,MAAM,WAAW,MAAM;IACrB,QAAQ,EAAE,MAAM,CAAA;IAChB,UAAU,EAAE,MAAM,CAAA;IAClB,OAAO,EAAE,MAAM,CAAA;IACf,QAAQ,EAAE,GAAG,CAAA;IACb,MAAM,EAAE,SAAS,GAAG,UAAU,GAAG,UAAU,CAAA;IAC3C,UAAU,CAAC,EAAE,MAAM,CAAA;CACpB;AAED,MAAM,WAAW,gBAAgB;IAC/B,cAAc,EAAE,MAAM,CAAA;IACtB,WAAW,EAAE,MAAM,CAAA;IACnB,oBAAoB,EAAE,MAAM,CAAA;IAC5B,iBAAiB,EAAE,MAAM,CAAA;IACzB,YAAY,EAAE,MAAM,CAAA;IACpB,UAAU,EAAE,MAAM,CAAA;IAClB,eAAe,EAAE,iBAAiB,EAAE,CAAA;CACrC;AAED,MAAM,WAAW,iBAAiB;IAChC,OAAO,EAAE,MAAM,CAAA;IACf,kBAAkB,EAAE,MAAM,CAAA;IAC1B,iBAAiB,EAAE,MAAM,CAAA;IACzB,qBAAqB,EAAE,MAAM,CAAA;IAC7B,mBAAmB,EAAE,MAAM,CAAA;CAC5B;AAED;;GAEG;AACH,qBAAa,uBAAwB,SAAQ,YAAY;IACvD,OAAO,CAAC,MAAM,CAAiB;IAC/B,OAAO,CAAC,eAAe,CAA4C;IACnE,OAAO,CAAC,eAAe,CAA0B;IACjD,OAAO,CAAC,iBAAiB,CAAuC;IAChE,OAAO,CAAC,gBAAgB,CAAkB;gBAE9B,MAAM,GAAE,OAAO,CAAC,eAAe,CAAM;IAejD;;OAEG;IACH,aAAa,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI;IAiBnE;;OAEG;IACG,cAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,iBAAiB,CAAC,GAAG,OAAO,CAAC,iBAAiB,CAAC;IAmCtF;;OAEG;IACG,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,SAAS,CAAC;IAmD5D;;OAEG;IACG,YAAY,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IA6B3G;;OAEG;IACG,YAAY,CAAC,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAwD7F;;OAEG;IACH,mBAAmB,IAAI,gBAAgB;IAIvC;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAexB,OAAO,CAAC,qBAAqB;IAW7B,OAAO,CAAC,iBAAiB;YAWX,oBAAoB;IAMlC,OAAO,CAAC,kBAAkB;IAS1B,OAAO,CAAC,mBAAmB;YAYb,qBAAqB;IA2BnC,OAAO,CAAC,sBAAsB;YAwDhB,iBAAiB;IAkC/B,OAAO,CAAC,cAAc;IAatB,OAAO,CAAC,wBAAwB;IAsChC,OAAO,CAAC,aAAa;IAMrB,OAAO,CAAC,aAAa;IAerB,OAAO,CAAC,iBAAiB;CAW1B;AAED,UAAU,YAAY;IACpB,OAAO,EAAE,MAAM,CAAA;IACf,MAAM,EAAE,MAAM,CAAA;IACd,SAAS,EAAE,MAAM,EAAE,CAAA;IACnB,SAAS,EAAE,MAAM,CAAA;IACjB,UAAU,EAAE,OAAO,CAAA;IACnB,OAAO,EAAE,OAAO,CAAA;IAChB,WAAW,EAAE,OAAO,CAAA;IACpB,UAAU,EAAE,MAAM,CAAA;CACnB;AAED,eAAe,uBAAuB,CAAA"}