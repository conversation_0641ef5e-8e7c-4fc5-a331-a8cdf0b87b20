/**
 * AG3NT Framework - Planning Agent
 *
 * Concrete implementation of BaseAgent for project planning.
 * Extracted from the sophisticated planning-graph.ts implementation.
 *
 * Features:
 * - Complete project analysis and planning workflow
 * - MCP-enhanced execution with context enrichment
 * - Sequential thinking for complex decisions
 * - Dynamic step sequencing based on project type
 * - Interactive clarification handling
 * - Progress tracking and session management
 */
import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent";
export interface PlanningInput {
    prompt: string;
    isInteractive?: boolean;
    userAnswers?: Record<string, string>;
    designStyleGuide?: any;
    hasImages?: boolean;
    userPreferences?: {
        model?: string;
        apiKey?: string;
    };
}
export interface PlanningResult {
    analyze?: any;
    clarify?: any;
    summary?: any;
    techstack?: any;
    prd?: any;
    'context-profile'?: any;
    wireframes?: any;
    design?: any;
    database?: any;
    filesystem?: any;
    workflow?: any;
    tasks?: any;
    scaffold?: any;
}
/**
 * Planning Agent - Sophisticated project planning with MCP enhancement
 */
export declare class PlanningAgent extends BaseAgent {
    private readonly planningSteps;
    constructor(config?: Partial<AgentConfig>);
    /**
     * Execute planning workflow - extracted from planning-graph.ts execute()
     */
    protected executeWorkflow(state: AgentState): Promise<AgentState>;
    /**
     * Execute individual planning step - extracted from planning-graph.ts patterns
     */
    protected executeStep(stepId: string, context: any): Promise<any>;
    /**
     * Get total steps for progress tracking
     */
    protected getTotalSteps(): number;
    /**
     * Get relevant documentation for step
     */
    protected getRelevantDocumentation(stepId: string): Promise<Record<string, any>>;
    /**
     * Determine step sequence based on project type
     */
    private getStepsForProjectType;
    private analyzePromptWithMCP;
    private clarifyRequirementsWithMCP;
    private generateSummaryWithMCP;
    private selectTechStackWithMCP;
    private createPRDWithMCP;
    private generateContextProfileWithMCP;
    private designWireframesWithMCP;
    private createDesignGuidelinesWithMCP;
    private designDatabaseSchemaWithMCP;
    private planFilesystemWithMCP;
    private defineWorkflowWithMCP;
    private breakdownTasksWithMCP;
    private generateScaffoldWithMCP;
}
export { PlanningAgent as default };
//# sourceMappingURL=planning-agent.d.ts.map