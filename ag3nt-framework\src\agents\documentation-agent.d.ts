/**
 * AG3NT Framework - Documentation Agent
 *
 * Specialized agent for maintaining and upgrading technical documentation.
 * Handles changelogs, architectural docs, API documentation, and user guides.
 *
 * Features:
 * - Automated documentation generation
 * - Documentation maintenance and updates
 * - Changelog generation from code changes
 * - API documentation from code analysis
 * - Architecture documentation
 * - User guide creation and updates
 */
import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent";
export interface DocumentationInput {
    task: DocumentationTask;
    codebase: DocumentationCodebase;
    existing: ExistingDocumentation;
    requirements: DocumentationRequirements;
}
export interface DocumentationTask {
    taskId: string;
    type: 'generate' | 'update' | 'maintain' | 'changelog' | 'api_docs' | 'architecture' | 'user_guide';
    title: string;
    description: string;
    priority: 'critical' | 'high' | 'medium' | 'low';
    scope: DocumentationScope;
    deadline?: string;
}
export interface DocumentationScope {
    modules: string[];
    features: string[];
    apis: string[];
    components: string[];
    includeInternal: boolean;
    includeExamples: boolean;
    includeTests: boolean;
}
export interface DocumentationCodebase {
    structure: CodeStructure;
    changes: CodeChange[];
    apis: APIDefinition[];
    components: ComponentDefinition[];
    architecture: ArchitectureInfo;
}
export interface CodeStructure {
    modules: ModuleDoc[];
    classes: ClassDoc[];
    functions: FunctionDoc[];
    interfaces: InterfaceDoc[];
    types: TypeDoc[];
}
export interface ModuleDoc {
    name: string;
    path: string;
    description: string;
    exports: ExportDoc[];
    dependencies: string[];
    examples: CodeExample[];
}
export interface ClassDoc {
    name: string;
    module: string;
    description: string;
    constructor: ConstructorDoc;
    methods: MethodDoc[];
    properties: PropertyDoc[];
    examples: CodeExample[];
}
export interface FunctionDoc {
    name: string;
    module: string;
    description: string;
    parameters: ParameterDoc[];
    returns: ReturnDoc;
    examples: CodeExample[];
    throws: ThrowsDoc[];
}
export interface InterfaceDoc {
    name: string;
    module: string;
    description: string;
    properties: PropertyDoc[];
    methods: MethodSignatureDoc[];
    extends: string[];
    examples: CodeExample[];
}
export interface TypeDoc {
    name: string;
    module: string;
    description: string;
    definition: string;
    examples: CodeExample[];
}
export interface ExportDoc {
    name: string;
    type: string;
    description: string;
}
export interface ConstructorDoc {
    parameters: ParameterDoc[];
    description: string;
    examples: CodeExample[];
}
export interface MethodDoc {
    name: string;
    description: string;
    parameters: ParameterDoc[];
    returns: ReturnDoc;
    visibility: 'public' | 'private' | 'protected';
    static: boolean;
    examples: CodeExample[];
    throws: ThrowsDoc[];
}
export interface PropertyDoc {
    name: string;
    type: string;
    description: string;
    visibility: 'public' | 'private' | 'protected';
    static: boolean;
    readonly: boolean;
    examples: CodeExample[];
}
export interface ParameterDoc {
    name: string;
    type: string;
    description: string;
    optional: boolean;
    default?: any;
}
export interface ReturnDoc {
    type: string;
    description: string;
}
export interface ThrowsDoc {
    type: string;
    description: string;
    when: string;
}
export interface MethodSignatureDoc {
    name: string;
    parameters: ParameterDoc[];
    returns: ReturnDoc;
    description: string;
}
export interface CodeExample {
    title: string;
    description: string;
    code: string;
    language: string;
    output?: string;
}
export interface CodeChange {
    file: string;
    type: 'added' | 'modified' | 'deleted' | 'renamed';
    description: string;
    impact: 'breaking' | 'feature' | 'fix' | 'refactor' | 'docs';
    author: string;
    timestamp: string;
}
export interface APIDefinition {
    endpoint: string;
    method: string;
    description: string;
    parameters: APIParameter[];
    responses: APIResponse[];
    examples: APIExample[];
    authentication: AuthenticationInfo;
}
export interface APIParameter {
    name: string;
    type: string;
    location: 'path' | 'query' | 'body' | 'header';
    required: boolean;
    description: string;
    schema?: any;
}
export interface APIResponse {
    status: number;
    description: string;
    schema?: any;
    examples: any[];
}
export interface APIExample {
    title: string;
    description: string;
    request: any;
    response: any;
}
export interface AuthenticationInfo {
    type: 'none' | 'bearer' | 'basic' | 'api_key' | 'oauth';
    description: string;
    examples: any[];
}
export interface ComponentDefinition {
    name: string;
    type: 'react' | 'vue' | 'angular' | 'web_component';
    description: string;
    props: ComponentProp[];
    events: ComponentEvent[];
    slots: ComponentSlot[];
    examples: ComponentExample[];
}
export interface ComponentProp {
    name: string;
    type: string;
    required: boolean;
    default?: any;
    description: string;
}
export interface ComponentEvent {
    name: string;
    description: string;
    payload: any;
}
export interface ComponentSlot {
    name: string;
    description: string;
    props?: any;
}
export interface ComponentExample {
    title: string;
    description: string;
    code: string;
    preview?: string;
}
export interface ArchitectureInfo {
    overview: string;
    patterns: ArchitecturePattern[];
    layers: ArchitectureLayer[];
    components: ArchitectureComponent[];
    dataFlow: DataFlowInfo[];
    decisions: ArchitectureDecision[];
}
export interface ArchitecturePattern {
    name: string;
    description: string;
    rationale: string;
    implementation: string;
}
export interface ArchitectureLayer {
    name: string;
    description: string;
    responsibilities: string[];
    dependencies: string[];
}
export interface ArchitectureComponent {
    name: string;
    type: string;
    description: string;
    responsibilities: string[];
    interfaces: string[];
    dependencies: string[];
}
export interface DataFlowInfo {
    name: string;
    description: string;
    steps: DataFlowStep[];
    diagram?: string;
}
export interface DataFlowStep {
    step: number;
    description: string;
    component: string;
    data: string;
}
export interface ArchitectureDecision {
    id: string;
    title: string;
    status: 'proposed' | 'accepted' | 'deprecated' | 'superseded';
    context: string;
    decision: string;
    consequences: string[];
    alternatives: string[];
}
export interface ExistingDocumentation {
    files: DocumentationFile[];
    structure: DocumentationStructure;
    metadata: DocumentationMetadata;
}
export interface DocumentationFile {
    path: string;
    type: 'markdown' | 'rst' | 'html' | 'pdf' | 'docx';
    title: string;
    content: string;
    lastModified: string;
    version: string;
}
export interface DocumentationStructure {
    sections: DocumentationSection[];
    navigation: NavigationItem[];
    index: DocumentationIndex;
}
export interface DocumentationSection {
    id: string;
    title: string;
    type: 'overview' | 'tutorial' | 'reference' | 'guide' | 'api' | 'changelog';
    files: string[];
    subsections: DocumentationSection[];
}
export interface NavigationItem {
    title: string;
    path: string;
    children: NavigationItem[];
}
export interface DocumentationIndex {
    terms: IndexTerm[];
    crossReferences: CrossReference[];
}
export interface IndexTerm {
    term: string;
    description: string;
    references: string[];
}
export interface CrossReference {
    from: string;
    to: string;
    type: 'see_also' | 'related' | 'prerequisite';
}
export interface DocumentationMetadata {
    title: string;
    version: string;
    authors: string[];
    lastUpdated: string;
    language: string;
    format: string;
}
export interface DocumentationRequirements {
    format: DocumentationFormat;
    style: DocumentationStyle;
    audience: DocumentationAudience;
    coverage: DocumentationCoverage;
    quality: DocumentationQuality;
}
export interface DocumentationFormat {
    primary: 'markdown' | 'rst' | 'html' | 'pdf' | 'docx';
    outputs: string[];
    templates: string[];
    styling: string[];
}
export interface DocumentationStyle {
    tone: 'formal' | 'casual' | 'technical' | 'friendly';
    voice: 'active' | 'passive';
    perspective: 'first_person' | 'second_person' | 'third_person';
    codeStyle: 'inline' | 'blocks' | 'both';
}
export interface DocumentationAudience {
    primary: 'developers' | 'users' | 'administrators' | 'mixed';
    experience: 'beginner' | 'intermediate' | 'advanced' | 'mixed';
    context: 'internal' | 'external' | 'both';
}
export interface DocumentationCoverage {
    apis: boolean;
    components: boolean;
    architecture: boolean;
    tutorials: boolean;
    examples: boolean;
    troubleshooting: boolean;
    changelog: boolean;
}
export interface DocumentationQuality {
    completeness: number;
    accuracy: number;
    clarity: number;
    consistency: number;
    upToDate: number;
}
export interface DocumentationResult {
    taskId: string;
    status: 'completed' | 'failed' | 'partial';
    documents: GeneratedDocument[];
    updates: DocumentationUpdate[];
    metrics: DocumentationMetrics;
    recommendations: DocumentationRecommendation[];
}
export interface GeneratedDocument {
    type: 'api' | 'component' | 'architecture' | 'tutorial' | 'changelog' | 'guide';
    title: string;
    path: string;
    content: string;
    format: string;
    metadata: any;
    sections: DocumentSection[];
}
export interface DocumentSection {
    id: string;
    title: string;
    content: string;
    level: number;
    subsections: DocumentSection[];
}
export interface DocumentationUpdate {
    file: string;
    type: 'created' | 'updated' | 'deleted';
    changes: string[];
    reason: string;
}
export interface DocumentationMetrics {
    coverage: CoverageMetrics;
    quality: QualityMetrics;
    maintenance: MaintenanceMetrics;
    usage: UsageMetrics;
}
export interface CoverageMetrics {
    apis: number;
    components: number;
    functions: number;
    classes: number;
    overall: number;
}
export interface QualityMetrics {
    completeness: number;
    accuracy: number;
    clarity: number;
    consistency: number;
    examples: number;
}
export interface MaintenanceMetrics {
    outdated: number;
    broken: number;
    missing: number;
    redundant: number;
}
export interface UsageMetrics {
    views: number;
    searches: number;
    feedback: number;
    issues: number;
}
export interface DocumentationRecommendation {
    type: 'coverage' | 'quality' | 'structure' | 'maintenance' | 'accessibility';
    priority: 'high' | 'medium' | 'low';
    description: string;
    action: string;
    effort: string;
    impact: string;
}
/**
 * Documentation Agent - Technical documentation management
 */
export declare class DocumentationAgent extends BaseAgent {
    private readonly documentationSteps;
    constructor(config?: Partial<AgentConfig>);
    /**
     * Execute documentation workflow
     */
    protected executeWorkflow(state: AgentState): Promise<AgentState>;
    /**
     * Execute individual documentation step with context enhancement
     */
    private executeStepWithContext;
    /**
     * Get total steps for progress tracking
     */
    protected getTotalSteps(): number;
    /**
     * Get relevant documentation for documentation agent
     */
    protected getRelevantDocumentation(): Promise<Record<string, any>>;
    private analyzeCodebaseWithMCP;
    private assessExistingDocsWithMCP;
    private planDocumentationWithMCP;
    private generateAPIDocsWithMCP;
    private createArchitectureDocsWithMCP;
    private writeUserGuidesWithMCP;
    private generateChangelogWithMCP;
    private updateExistingDocsWithMCP;
    private validateQualityWithMCP;
    private publishDocsWithMCP;
}
export { DocumentationAgent as default };
//# sourceMappingURL=documentation-agent.d.ts.map