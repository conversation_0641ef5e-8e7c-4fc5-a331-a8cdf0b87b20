"use strict";
/**
 * AG3NT Framework - Coordination Pattern Registry
 *
 * Registry for managing different coordination patterns and strategies
 * for sophisticated multi-agent collaboration.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoordinationPatternRegistry = void 0;
const events_1 = require("events");
/**
 * Coordination Pattern Registry
 */
class CoordinationPatternRegistry extends events_1.EventEmitter {
    constructor() {
        super();
        this.patterns = new Map();
        this.strategies = new Map();
        this.activeExecutions = new Map();
        this.patternHistory = [];
        this.initializeBuiltInPatterns();
        this.initializeBuiltInStrategies();
    }
    /**
     * Register coordination pattern
     */
    registerPattern(pattern) {
        this.patterns.set(pattern.patternId, pattern);
        this.emit('pattern_registered', pattern);
        console.log(`📋 Registered coordination pattern: ${pattern.name}`);
    }
    /**
     * Register coordination strategy
     */
    registerStrategy(strategy) {
        this.strategies.set(strategy.strategyId, strategy);
        this.emit('strategy_registered', strategy);
        console.log(`🎯 Registered coordination strategy: ${strategy.name}`);
    }
    /**
     * Recommend pattern for context
     */
    recommendPattern(context) {
        const recommendations = [];
        for (const [patternId, pattern] of this.patterns.entries()) {
            const score = this.calculatePatternScore(pattern, context);
            if (score > 0.5) {
                recommendations.push({
                    patternId,
                    confidence: score,
                    reasoning: this.generateRecommendationReasoning(pattern, context, score),
                    expectedBenefits: this.identifyExpectedBenefits(pattern, context),
                    potentialRisks: this.identifyPotentialRisks(pattern, context),
                    alternativePatterns: this.findAlternativePatterns(pattern, context)
                });
            }
        }
        // Sort by confidence
        recommendations.sort((a, b) => b.confidence - a.confidence);
        return recommendations.slice(0, 5); // Top 5 recommendations
    }
    /**
     * Execute coordination pattern
     */
    async executePattern(patternId, context, config = {}) {
        const pattern = this.patterns.get(patternId);
        if (!pattern) {
            throw new Error(`Pattern ${patternId} not found`);
        }
        console.log(`🚀 Executing coordination pattern: ${pattern.name}`);
        const execution = {
            executionId: `exec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            patternId,
            context,
            config,
            status: 'initializing',
            startTime: Date.now(),
            metrics: {
                executionTime: 0,
                resourceUsage: 0,
                qualityScore: 0,
                successRate: 0,
                adaptationCount: 0
            },
            events: []
        };
        this.activeExecutions.set(execution.executionId, execution);
        try {
            // Initialize pattern
            execution.status = 'running';
            const patternInstance = await pattern.implementation.initializePattern(config);
            execution.patternInstance = patternInstance;
            // Execute pattern
            const result = await pattern.implementation.executePattern(context);
            execution.result = result;
            execution.status = 'completed';
            execution.endTime = Date.now();
            execution.metrics.executionTime = execution.endTime - execution.startTime;
            // Update pattern metrics
            this.updatePatternMetrics(pattern, execution);
            this.emit('pattern_executed', execution);
            console.log(`✅ Pattern execution completed: ${execution.executionId}`);
        }
        catch (error) {
            execution.status = 'failed';
            execution.error = error instanceof Error ? error.message : 'Unknown error';
            execution.endTime = Date.now();
            this.emit('pattern_execution_failed', execution);
            console.error(`❌ Pattern execution failed: ${execution.executionId}`, error);
        }
        // Move to history
        this.patternHistory.push(execution);
        this.activeExecutions.delete(execution.executionId);
        return execution;
    }
    /**
     * Adapt pattern based on feedback
     */
    async adaptPattern(executionId, feedback) {
        const execution = this.activeExecutions.get(executionId);
        if (!execution) {
            throw new Error(`Execution ${executionId} not found`);
        }
        const pattern = this.patterns.get(execution.patternId);
        if (!pattern)
            return;
        console.log(`🔄 Adapting pattern: ${pattern.name}`);
        try {
            await pattern.implementation.adaptPattern(feedback);
            execution.metrics.adaptationCount++;
            this.emit('pattern_adapted', { execution, feedback });
        }
        catch (error) {
            console.error(`Failed to adapt pattern ${pattern.name}:`, error);
        }
    }
    /**
     * Get pattern analytics
     */
    getPatternAnalytics() {
        const totalExecutions = this.patternHistory.length;
        const successfulExecutions = this.patternHistory.filter(e => e.status === 'completed').length;
        return {
            totalPatterns: this.patterns.size,
            totalStrategies: this.strategies.size,
            totalExecutions,
            successRate: totalExecutions > 0 ? successfulExecutions / totalExecutions : 0,
            averageExecutionTime: this.calculateAverageExecutionTime(),
            mostUsedPatterns: this.getMostUsedPatterns(),
            patternEffectiveness: this.calculatePatternEffectiveness(),
            adaptationSuccess: this.calculateAdaptationSuccess()
        };
    }
    /**
     * Private helper methods
     */
    calculatePatternScore(pattern, context) {
        let score = 0;
        let factors = 0;
        // Task type compatibility
        if (pattern.applicability.taskTypes.includes(context.taskType) || pattern.applicability.taskTypes.includes('*')) {
            score += 0.3;
        }
        factors++;
        // Team size compatibility
        const teamSize = context.participants.length;
        if (teamSize >= pattern.applicability.teamSizes.min && teamSize <= pattern.applicability.teamSizes.max) {
            score += 0.2;
        }
        factors++;
        // Complexity match
        const complexityScore = this.getComplexityScore(pattern.applicability.complexity, context.complexity);
        score += complexityScore * 0.2;
        factors++;
        // Time constraints
        const timeScore = this.getTimeConstraintScore(pattern.applicability.timeConstraints, context.timeConstraints);
        score += timeScore * 0.15;
        factors++;
        // Quality requirements
        const qualityScore = this.getQualityScore(pattern.applicability.qualityRequirements, context.qualityRequirements);
        score += qualityScore * 0.15;
        factors++;
        return score;
    }
    getComplexityScore(patternComplexity, contextComplexity) {
        const complexityMap = { low: 1, medium: 2, high: 3 };
        const patternLevel = complexityMap[patternComplexity];
        const contextLevel = complexityMap[contextComplexity];
        return Math.max(0, 1 - Math.abs(patternLevel - contextLevel) / 2);
    }
    getTimeConstraintScore(patternTime, contextTime) {
        const timeMap = { tight: 1, moderate: 2, flexible: 3 };
        const patternLevel = timeMap[patternTime];
        const contextLevel = timeMap[contextTime];
        return patternLevel >= contextLevel ? 1 : 0.5;
    }
    getQualityScore(patternQuality, contextQuality) {
        const qualityMap = { basic: 1, standard: 2, high: 3, critical: 4 };
        const patternLevel = qualityMap[patternQuality];
        const contextLevel = qualityMap[contextQuality];
        return patternLevel >= contextLevel ? 1 : 0.5;
    }
    generateRecommendationReasoning(pattern, context, score) {
        const reasoning = [];
        if (pattern.applicability.taskTypes.includes(context.taskType)) {
            reasoning.push(`Optimized for ${context.taskType} tasks`);
        }
        if (score > 0.8) {
            reasoning.push('High compatibility with current context');
        }
        else if (score > 0.6) {
            reasoning.push('Good compatibility with current context');
        }
        if (pattern.metrics.successRate > 0.8) {
            reasoning.push(`High success rate (${Math.round(pattern.metrics.successRate * 100)}%)`);
        }
        return reasoning;
    }
    identifyExpectedBenefits(pattern, context) {
        const benefits = [];
        if (pattern.metrics.resourceEfficiency > 0.8) {
            benefits.push('High resource efficiency');
        }
        if (pattern.metrics.qualityScore > 0.8) {
            benefits.push('High quality outcomes');
        }
        if (pattern.metrics.scalability > 0.8) {
            benefits.push('Good scalability');
        }
        return benefits;
    }
    identifyPotentialRisks(pattern, context) {
        const risks = [];
        if (pattern.metrics.successRate < 0.7) {
            risks.push('Lower than average success rate');
        }
        if (pattern.applicability.complexity === 'high' && context.complexity === 'low') {
            risks.push('Pattern may be overly complex for the task');
        }
        return risks;
    }
    findAlternativePatterns(pattern, context) {
        const alternatives = [];
        for (const [patternId, altPattern] of this.patterns.entries()) {
            if (patternId !== pattern.patternId &&
                altPattern.type === pattern.type &&
                this.calculatePatternScore(altPattern, context) > 0.5) {
                alternatives.push(patternId);
            }
        }
        return alternatives.slice(0, 3);
    }
    updatePatternMetrics(pattern, execution) {
        pattern.metrics.usageCount++;
        pattern.metrics.lastUsed = Date.now();
        if (execution.status === 'completed') {
            pattern.metrics.successRate = (pattern.metrics.successRate * (pattern.metrics.usageCount - 1) + 1) / pattern.metrics.usageCount;
        }
        else {
            pattern.metrics.successRate = (pattern.metrics.successRate * (pattern.metrics.usageCount - 1)) / pattern.metrics.usageCount;
        }
        if (execution.metrics.executionTime > 0) {
            pattern.metrics.averageExecutionTime = (pattern.metrics.averageExecutionTime + execution.metrics.executionTime) / 2;
        }
    }
    calculateAverageExecutionTime() {
        const completed = this.patternHistory.filter(e => e.status === 'completed' && e.endTime);
        if (completed.length === 0)
            return 0;
        const totalTime = completed.reduce((sum, e) => sum + (e.endTime - e.startTime), 0);
        return totalTime / completed.length;
    }
    getMostUsedPatterns() {
        return Array.from(this.patterns.entries())
            .map(([patternId, pattern]) => ({ patternId, usageCount: pattern.metrics.usageCount }))
            .sort((a, b) => b.usageCount - a.usageCount)
            .slice(0, 5);
    }
    calculatePatternEffectiveness() {
        const effectiveness = new Map();
        for (const [patternId, pattern] of this.patterns.entries()) {
            const score = (pattern.metrics.successRate + pattern.metrics.qualityScore + pattern.metrics.resourceEfficiency) / 3;
            effectiveness.set(patternId, score);
        }
        return effectiveness;
    }
    calculateAdaptationSuccess() {
        const adaptations = this.patternHistory.filter(e => e.metrics.adaptationCount > 0);
        if (adaptations.length === 0)
            return 0;
        const successful = adaptations.filter(e => e.status === 'completed');
        return successful.length / adaptations.length;
    }
    initializeBuiltInPatterns() {
        // Hierarchical Delegation Pattern
        this.registerPattern({
            patternId: 'hierarchical_delegation',
            name: 'Hierarchical Delegation',
            description: 'Top-down task delegation based on authority levels',
            type: 'delegation',
            category: 'hierarchical',
            applicability: {
                taskTypes: ['planning', 'coordination', 'management'],
                agentTypes: ['planning', 'executor', 'manager'],
                teamSizes: { min: 3, max: 20 },
                complexity: 'medium',
                timeConstraints: 'moderate',
                qualityRequirements: 'standard',
                riskTolerance: 'medium'
            },
            configuration: {
                parameters: [
                    { name: 'maxDelegationDepth', type: 'number', defaultValue: 3, range: { min: 1, max: 5 }, required: true, description: 'Maximum delegation depth' },
                    { name: 'requireConfirmation', type: 'boolean', defaultValue: true, required: false, description: 'Require delegation confirmation' }
                ],
                constraints: [],
                prerequisites: ['authority_levels_defined'],
                dependencies: ['task_delegation_system'],
                fallbackPatterns: ['peer_to_peer_delegation']
            },
            metrics: {
                successRate: 0.85,
                averageExecutionTime: 120000,
                resourceEfficiency: 0.8,
                qualityScore: 0.85,
                scalability: 0.9,
                adaptability: 0.7,
                usageCount: 0,
                lastUsed: 0
            },
            implementation: {
                initializePattern: async (config) => ({ type: 'hierarchical_delegation', config }),
                executePattern: async (context) => ({ success: true, result: 'delegation_completed' }),
                monitorPattern: async (execution) => ({ status: 'monitoring' }),
                adaptPattern: async (feedback) => ({ adapted: true }),
                cleanupPattern: async (execution) => { }
            }
        });
        // Consensus Decision Making Pattern
        this.registerPattern({
            patternId: 'consensus_decision',
            name: 'Consensus Decision Making',
            description: 'Democratic decision making through voting and consensus',
            type: 'consensus',
            category: 'democratic',
            applicability: {
                taskTypes: ['decision_making', 'planning', 'resource_allocation'],
                agentTypes: ['*'],
                teamSizes: { min: 3, max: 15 },
                complexity: 'medium',
                timeConstraints: 'flexible',
                qualityRequirements: 'high',
                riskTolerance: 'low'
            },
            configuration: {
                parameters: [
                    { name: 'quorumThreshold', type: 'number', defaultValue: 0.6, range: { min: 0.5, max: 1.0 }, required: true, description: 'Quorum threshold for decisions' },
                    { name: 'votingTimeout', type: 'number', defaultValue: 300000, range: { min: 60000, max: 1800000 }, required: true, description: 'Voting timeout in milliseconds' }
                ],
                constraints: [],
                prerequisites: ['voting_rights_defined'],
                dependencies: ['consensus_protocol_engine'],
                fallbackPatterns: ['authority_based_decision']
            },
            metrics: {
                successRate: 0.78,
                averageExecutionTime: 180000,
                resourceEfficiency: 0.7,
                qualityScore: 0.9,
                scalability: 0.6,
                adaptability: 0.8,
                usageCount: 0,
                lastUsed: 0
            },
            implementation: {
                initializePattern: async (config) => ({ type: 'consensus_decision', config }),
                executePattern: async (context) => ({ success: true, result: 'consensus_reached' }),
                monitorPattern: async (execution) => ({ status: 'monitoring' }),
                adaptPattern: async (feedback) => ({ adapted: true }),
                cleanupPattern: async (execution) => { }
            }
        });
        // Workflow Handoff Pattern
        this.registerPattern({
            patternId: 'workflow_handoff',
            name: 'Workflow Handoff',
            description: 'Formal handoff of work between agents with state validation',
            type: 'handoff',
            category: 'peer_to_peer',
            applicability: {
                taskTypes: ['development', 'processing', 'analysis'],
                agentTypes: ['*'],
                teamSizes: { min: 2, max: 10 },
                complexity: 'high',
                timeConstraints: 'tight',
                qualityRequirements: 'critical',
                riskTolerance: 'low'
            },
            configuration: {
                parameters: [
                    { name: 'enableStateValidation', type: 'boolean', defaultValue: true, required: true, description: 'Enable state validation during handoff' },
                    { name: 'enableRollback', type: 'boolean', defaultValue: true, required: false, description: 'Enable rollback capabilities' }
                ],
                constraints: [],
                prerequisites: ['state_validation_rules'],
                dependencies: ['workflow_handoff_manager'],
                fallbackPatterns: ['simple_handoff']
            },
            metrics: {
                successRate: 0.92,
                averageExecutionTime: 90000,
                resourceEfficiency: 0.85,
                qualityScore: 0.95,
                scalability: 0.8,
                adaptability: 0.75,
                usageCount: 0,
                lastUsed: 0
            },
            implementation: {
                initializePattern: async (config) => ({ type: 'workflow_handoff', config }),
                executePattern: async (context) => ({ success: true, result: 'handoff_completed' }),
                monitorPattern: async (execution) => ({ status: 'monitoring' }),
                adaptPattern: async (feedback) => ({ adapted: true }),
                cleanupPattern: async (execution) => { }
            }
        });
    }
    initializeBuiltInStrategies() {
        // Adaptive Strategy
        this.registerStrategy({
            strategyId: 'adaptive_coordination',
            name: 'Adaptive Coordination Strategy',
            description: 'Dynamically selects coordination patterns based on context',
            patterns: ['hierarchical_delegation', 'consensus_decision', 'workflow_handoff'],
            selectionCriteria: {
                contextFactors: [
                    { name: 'taskComplexity', type: 'categorical', weight: 0.3, values: ['low', 'medium', 'high'] },
                    { name: 'teamSize', type: 'numerical', weight: 0.2, values: [1, 50] },
                    { name: 'timeConstraints', type: 'categorical', weight: 0.25, values: ['tight', 'moderate', 'flexible'] },
                    { name: 'qualityRequirements', type: 'categorical', weight: 0.25, values: ['basic', 'standard', 'high', 'critical'] }
                ],
                weightingScheme: {
                    taskComplexity: 0.3,
                    teamSize: 0.2,
                    timeConstraints: 0.25,
                    qualityRequirements: 0.25,
                    riskLevel: 0,
                    resourceAvailability: 0
                },
                decisionThreshold: 0.7,
                fallbackStrategy: 'default_coordination'
            },
            adaptationRules: [
                {
                    ruleId: 'complexity_escalation',
                    condition: 'taskComplexity === "high" && currentPattern !== "workflow_handoff"',
                    action: 'switch_pattern',
                    parameters: { targetPattern: 'workflow_handoff' },
                    priority: 1
                }
            ],
            performance: {
                overallScore: 0.85,
                patternEffectiveness: new Map(),
                adaptationSuccess: 0.8,
                contextAccuracy: 0.9,
                learningRate: 0.1
            }
        });
    }
}
exports.CoordinationPatternRegistry = CoordinationPatternRegistry;
exports.default = CoordinationPatternRegistry;
//# sourceMappingURL=coordination-pattern-registry.js.map