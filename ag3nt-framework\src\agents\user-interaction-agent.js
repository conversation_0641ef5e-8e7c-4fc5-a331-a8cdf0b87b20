"use strict";
/**
 * AG3NT Framework - User Interaction Agent
 *
 * Specialized agent for interfacing with users, collecting feedback,
 * and providing explanations and clarifications.
 *
 * Features:
 * - Natural language interaction
 * - User feedback collection
 * - Explanation generation
 * - Clarification requests
 * - User preference learning
 * - Multi-modal communication
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = exports.UserInteractionAgent = void 0;
const base_agent_1 = require("../core/base-agent");
const ai_service_1 = require("../../ai-service");
/**
 * User Interaction Agent - Natural language user interface
 */
class UserInteractionAgent extends base_agent_1.BaseAgent {
    constructor(config = {}) {
        super('user-interaction', {
            capabilities: {
                requiredCapabilities: [
                    'natural_language_processing',
                    'user_modeling',
                    'personalization',
                    'explanation_generation',
                    'feedback_collection',
                    'preference_learning',
                    'multimodal_communication'
                ],
                contextFilters: ['user', 'interaction', 'communication', 'feedback', 'preferences'],
                mcpEnhanced: true,
                sequentialThinking: true,
                contextEnrichment: true,
                ragIntegration: true
            },
            ...config
        });
        this.interactionSteps = [
            'analyze_user', 'understand_intent', 'generate_response',
            'personalize_content', 'collect_feedback', 'learn_preferences',
            'provide_explanations', 'suggest_improvements', 'update_profile'
        ];
    }
    /**
     * Execute user interaction workflow
     */
    async executeWorkflow(state) {
        const input = state.input;
        console.log(`👤 Starting user interaction: ${input.task.title}`);
        // Execute interaction steps sequentially
        for (const stepId of this.interactionSteps) {
            console.log(`🔄 Executing step: ${stepId}`);
            const stepResult = await this.executeStepWithContext(stepId, input);
            if (stepResult.needsInput) {
                state.needsInput = true;
                state.question = stepResult.question;
                break;
            }
            // Update progress
            state.metadata.completedSteps++;
            state.metadata.lastUpdated = new Date().toISOString();
            this.emit('step_completed', {
                agentId: this.agentId,
                stepId,
                progress: state.metadata.completedSteps / state.metadata.totalSteps
            });
        }
        // Mark as completed if no input needed
        if (!state.needsInput) {
            state.completed = true;
            console.log(`✅ User interaction completed: ${input.task.title}`);
        }
        return state;
    }
    /**
     * Execute individual interaction step with context enhancement
     */
    async executeStepWithContext(stepId, input) {
        // Get enhanced context from context engine
        const enhancedState = await this.getEnhancedContext(stepId);
        // Execute step with MCP enhancement
        switch (stepId) {
            case 'analyze_user':
                return await this.analyzeUserWithMCP(enhancedState, input);
            case 'understand_intent':
                return await this.understandIntentWithMCP(enhancedState);
            case 'generate_response':
                return await this.generateResponseWithMCP(enhancedState);
            case 'personalize_content':
                return await this.personalizeContentWithMCP(enhancedState);
            case 'collect_feedback':
                return await this.collectFeedbackWithMCP(enhancedState);
            case 'learn_preferences':
                return await this.learnPreferencesWithMCP(enhancedState);
            case 'provide_explanations':
                return await this.provideExplanationsWithMCP(enhancedState);
            case 'suggest_improvements':
                return await this.suggestImprovementsWithMCP(enhancedState);
            case 'update_profile':
                return await this.updateProfileWithMCP(enhancedState);
            default:
                throw new Error(`Unknown interaction step: ${stepId}`);
        }
    }
    /**
     * Get total steps for progress tracking
     */
    getTotalSteps() {
        return this.interactionSteps.length;
    }
    /**
     * Get relevant documentation for user interaction
     */
    async getRelevantDocumentation() {
        return {
            userInteraction: 'User interaction design and natural language processing',
            personalization: 'User personalization and adaptive interfaces',
            feedback: 'Feedback collection and analysis techniques',
            accessibility: 'Accessibility guidelines and inclusive design',
            communication: 'Effective communication and explanation strategies',
            learning: 'User preference learning and behavior modeling'
        };
    }
    // ============================================================================
    // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
    // ============================================================================
    async analyzeUserWithMCP(state, input) {
        const userAnalysis = await ai_service_1.aiService.analyzeUserProfile(input.user, input.context, input.task.scope);
        this.state.results.userAnalysis = userAnalysis;
        return {
            results: userAnalysis,
            needsInput: false,
            completed: false
        };
    }
    async understandIntentWithMCP(state) {
        const userAnalysis = this.state.results.userAnalysis;
        const intentUnderstanding = await ai_service_1.aiService.understandUserIntent(userAnalysis, this.state.input.context);
        this.state.results.intentUnderstanding = intentUnderstanding;
        return {
            results: intentUnderstanding,
            needsInput: false,
            completed: false
        };
    }
    async generateResponseWithMCP(state) {
        const intentUnderstanding = this.state.results.intentUnderstanding;
        const response = await ai_service_1.aiService.generateUserResponse(intentUnderstanding, this.state.input.user.preferences);
        this.state.results.response = response;
        return {
            results: response,
            needsInput: false,
            completed: false
        };
    }
    async personalizeContentWithMCP(state) {
        const response = this.state.results.response;
        const personalization = await ai_service_1.aiService.personalizeUserContent(response, this.state.input.user, this.state.input.requirements.personalization);
        this.state.results.personalization = personalization;
        return {
            results: personalization,
            needsInput: false,
            completed: false
        };
    }
    async collectFeedbackWithMCP(state) {
        const personalization = this.state.results.personalization;
        const feedback = await ai_service_1.aiService.collectUserFeedback(personalization, this.state.input.user.preferences);
        this.state.results.feedback = feedback;
        return {
            results: feedback,
            needsInput: false,
            completed: false
        };
    }
    async learnPreferencesWithMCP(state) {
        const feedback = this.state.results.feedback;
        const learning = await ai_service_1.aiService.learnUserPreferences(feedback, this.state.input.user.history);
        this.state.results.learning = learning;
        return {
            results: learning,
            needsInput: false,
            completed: false
        };
    }
    async provideExplanationsWithMCP(state) {
        const learning = this.state.results.learning;
        const explanations = await ai_service_1.aiService.provideUserExplanations(learning, this.state.input.requirements.quality);
        this.state.results.explanations = explanations;
        return {
            results: explanations,
            needsInput: false,
            completed: false
        };
    }
    async suggestImprovementsWithMCP(state) {
        const explanations = this.state.results.explanations;
        const improvements = await ai_service_1.aiService.suggestUserImprovements(explanations, this.state.input.user);
        this.state.results.improvements = improvements;
        return {
            results: improvements,
            needsInput: false,
            completed: false
        };
    }
    async updateProfileWithMCP(state) {
        const improvements = this.state.results.improvements;
        const profileUpdate = await ai_service_1.aiService.updateUserProfile(improvements, this.state.input.task);
        this.state.results.profileUpdate = profileUpdate;
        return {
            results: profileUpdate,
            needsInput: false,
            completed: true // Final step
        };
    }
}
exports.UserInteractionAgent = UserInteractionAgent;
exports.default = UserInteractionAgent;
//# sourceMappingURL=user-interaction-agent.js.map