/**
 * AG3NT Framework - Failover Manager
 * 
 * Advanced failover management system with automatic detection, recovery,
 * and intelligent backup agent selection for high availability.
 */

import { EventEmitter } from "events"
import { AgentInstance, AgentDiscoveryService } from "./agent-discovery-service"
import { LoadBalancer } from "./load-balancer"

export interface FailoverConfig {
  enableAutoFailover: boolean
  enableGracefulShutdown: boolean
  failureDetectionInterval: number
  maxFailureThreshold: number
  recoveryTimeout: number
  backupAgentRatio: number
  enableDataReplication: boolean
  enableStateTransfer: boolean
}

export interface FailoverEvent {
  eventId: string
  type: 'agent_failure' | 'agent_recovery' | 'failover_triggered' | 'failover_completed'
  timestamp: number
  primaryAgent: string
  backupAgent?: string
  reason: string
  impact: FailoverImpact
  recovery: FailoverRecovery
}

export interface FailoverImpact {
  affectedRequests: number
  estimatedDowntime: number
  dataLoss: boolean
  serviceDisruption: 'none' | 'minimal' | 'moderate' | 'severe'
}

export interface FailoverRecovery {
  strategy: 'immediate' | 'graceful' | 'manual'
  estimatedTime: number
  requiresIntervention: boolean
  rollbackPlan: string[]
}

export interface BackupAgent {
  agentId: string
  primaryAgent: string
  readinessLevel: 'cold' | 'warm' | 'hot'
  lastSyncTime: number
  dataConsistency: number // 0-1
  activationTime: number
}

export interface FailoverPlan {
  planId: string
  primaryAgent: string
  backupAgents: BackupAgent[]
  failoverTriggers: FailoverTrigger[]
  recoverySteps: RecoveryStep[]
  rollbackSteps: RollbackStep[]
  testSchedule: TestSchedule
}

export interface FailoverTrigger {
  type: 'health_check_failure' | 'response_timeout' | 'error_rate_threshold' | 'manual'
  threshold: any
  consecutiveFailures: number
  timeWindow: number
}

export interface RecoveryStep {
  stepId: string
  order: number
  action: 'activate_backup' | 'transfer_state' | 'redirect_traffic' | 'notify_stakeholders'
  parameters: any
  timeout: number
  rollbackOnFailure: boolean
}

export interface RollbackStep {
  stepId: string
  order: number
  action: 'restore_primary' | 'transfer_state_back' | 'redirect_traffic_back'
  parameters: any
  timeout: number
}

export interface TestSchedule {
  frequency: 'daily' | 'weekly' | 'monthly'
  nextTest: number
  lastTest?: number
  testResults: TestResult[]
}

export interface TestResult {
  testId: string
  timestamp: number
  success: boolean
  failoverTime: number
  issues: string[]
  recommendations: string[]
}

export interface FailoverMetrics {
  totalFailovers: number
  successfulFailovers: number
  averageFailoverTime: number
  averageRecoveryTime: number
  dataLossIncidents: number
  testSuccessRate: number
  mtbf: number // Mean Time Between Failures
  mttr: number // Mean Time To Recovery
}

/**
 * Failover Manager
 */
export class FailoverManager extends EventEmitter {
  private config: FailoverConfig
  private discoveryService: AgentDiscoveryService
  private loadBalancer: LoadBalancer
  private failoverPlans: Map<string, FailoverPlan> = new Map()
  private backupAgents: Map<string, BackupAgent[]> = new Map()
  private failoverHistory: FailoverEvent[] = []
  private activeFailovers: Map<string, FailoverEvent> = new Map()
  private metrics: FailoverMetrics
  private monitoringTimer?: NodeJS.Timeout

  constructor(
    discoveryService: AgentDiscoveryService,
    loadBalancer: LoadBalancer,
    config: Partial<FailoverConfig> = {}
  ) {
    super()
    
    this.discoveryService = discoveryService
    this.loadBalancer = loadBalancer
    this.config = {
      enableAutoFailover: true,
      enableGracefulShutdown: true,
      failureDetectionInterval: 5000, // 5 seconds
      maxFailureThreshold: 3,
      recoveryTimeout: 30000, // 30 seconds
      backupAgentRatio: 0.5, // 50% backup agents
      enableDataReplication: true,
      enableStateTransfer: true,
      ...config
    }

    this.metrics = {
      totalFailovers: 0,
      successfulFailovers: 0,
      averageFailoverTime: 0,
      averageRecoveryTime: 0,
      dataLossIncidents: 0,
      testSuccessRate: 0,
      mtbf: 0,
      mttr: 0
    }

    this.setupEventHandlers()
  }

  /**
   * Start failover monitoring
   */
  start(): void {
    console.log('🛡️ Starting Failover Manager')
    
    if (this.config.enableAutoFailover) {
      this.startFailureDetection()
    }

    this.emit('failover_manager_started')
  }

  /**
   * Create failover plan for agent
   */
  async createFailoverPlan(primaryAgentId: string): Promise<FailoverPlan> {
    const primaryAgent = this.discoveryService.getAgent(primaryAgentId)
    if (!primaryAgent) {
      throw new Error(`Primary agent ${primaryAgentId} not found`)
    }

    console.log(`🛡️ Creating failover plan for agent: ${primaryAgentId}`)

    // Find suitable backup agents
    const backupAgents = await this.findBackupAgents(primaryAgent)
    
    const plan: FailoverPlan = {
      planId: `failover-${primaryAgentId}-${Date.now()}`,
      primaryAgent: primaryAgentId,
      backupAgents,
      failoverTriggers: this.createDefaultTriggers(),
      recoverySteps: this.createRecoverySteps(primaryAgentId, backupAgents),
      rollbackSteps: this.createRollbackSteps(primaryAgentId, backupAgents),
      testSchedule: {
        frequency: 'weekly',
        nextTest: Date.now() + 7 * 24 * 60 * 60 * 1000, // Next week
        testResults: []
      }
    }

    this.failoverPlans.set(primaryAgentId, plan)
    this.backupAgents.set(primaryAgentId, backupAgents)

    this.emit('failover_plan_created', plan)
    return plan
  }

  /**
   * Trigger failover for agent
   */
  async triggerFailover(primaryAgentId: string, reason: string, manual: boolean = false): Promise<FailoverEvent> {
    console.log(`🚨 Triggering failover for agent: ${primaryAgentId}`)
    console.log(`📝 Reason: ${reason}`)

    const plan = this.failoverPlans.get(primaryAgentId)
    if (!plan) {
      throw new Error(`No failover plan found for agent ${primaryAgentId}`)
    }

    const startTime = Date.now()
    const failoverEvent: FailoverEvent = {
      eventId: `failover-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: 'failover_triggered',
      timestamp: startTime,
      primaryAgent: primaryAgentId,
      reason,
      impact: {
        affectedRequests: 0,
        estimatedDowntime: 0,
        dataLoss: false,
        serviceDisruption: 'minimal'
      },
      recovery: {
        strategy: manual ? 'manual' : 'immediate',
        estimatedTime: 30000,
        requiresIntervention: manual,
        rollbackPlan: plan.rollbackSteps.map(s => s.action)
      }
    }

    this.activeFailovers.set(primaryAgentId, failoverEvent)

    try {
      // Execute recovery steps
      const backupAgent = await this.executeRecoverySteps(plan, failoverEvent)
      
      failoverEvent.backupAgent = backupAgent.agentId
      failoverEvent.type = 'failover_completed'
      
      const failoverTime = Date.now() - startTime
      this.updateMetrics(failoverEvent, failoverTime, true)

      this.emit('failover_completed', failoverEvent)
      console.log(`✅ Failover completed in ${failoverTime}ms`)

      return failoverEvent

    } catch (error) {
      failoverEvent.type = 'agent_failure'
      this.updateMetrics(failoverEvent, Date.now() - startTime, false)
      
      this.emit('failover_failed', { failoverEvent, error })
      console.error(`❌ Failover failed:`, error)
      
      throw error
    } finally {
      this.activeFailovers.delete(primaryAgentId)
      this.failoverHistory.push(failoverEvent)
    }
  }

  /**
   * Recover primary agent
   */
  async recoverPrimaryAgent(primaryAgentId: string): Promise<void> {
    console.log(`🔄 Recovering primary agent: ${primaryAgentId}`)

    const plan = this.failoverPlans.get(primaryAgentId)
    if (!plan) {
      throw new Error(`No failover plan found for agent ${primaryAgentId}`)
    }

    // Check if primary agent is healthy
    const primaryAgent = this.discoveryService.getAgent(primaryAgentId)
    if (!primaryAgent || primaryAgent.status !== 'healthy') {
      throw new Error(`Primary agent ${primaryAgentId} is not healthy`)
    }

    // Execute rollback steps
    for (const step of plan.rollbackSteps) {
      await this.executeRollbackStep(step, primaryAgentId)
    }

    const recoveryEvent: FailoverEvent = {
      eventId: `recovery-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: 'agent_recovery',
      timestamp: Date.now(),
      primaryAgent: primaryAgentId,
      reason: 'Primary agent recovered',
      impact: {
        affectedRequests: 0,
        estimatedDowntime: 0,
        dataLoss: false,
        serviceDisruption: 'none'
      },
      recovery: {
        strategy: 'graceful',
        estimatedTime: 0,
        requiresIntervention: false,
        rollbackPlan: []
      }
    }

    this.failoverHistory.push(recoveryEvent)
    this.emit('agent_recovered', recoveryEvent)
    console.log(`✅ Primary agent ${primaryAgentId} recovered`)
  }

  /**
   * Test failover plan
   */
  async testFailoverPlan(primaryAgentId: string): Promise<TestResult> {
    console.log(`🧪 Testing failover plan for agent: ${primaryAgentId}`)

    const plan = this.failoverPlans.get(primaryAgentId)
    if (!plan) {
      throw new Error(`No failover plan found for agent ${primaryAgentId}`)
    }

    const startTime = Date.now()
    const testResult: TestResult = {
      testId: `test-${Date.now()}`,
      timestamp: startTime,
      success: false,
      failoverTime: 0,
      issues: [],
      recommendations: []
    }

    try {
      // Simulate failover without actually failing the agent
      const backupAgents = this.backupAgents.get(primaryAgentId) || []
      
      if (backupAgents.length === 0) {
        testResult.issues.push('No backup agents available')
        testResult.recommendations.push('Configure backup agents')
      }

      // Test backup agent readiness
      for (const backup of backupAgents) {
        const backupAgent = this.discoveryService.getAgent(backup.agentId)
        if (!backupAgent || backupAgent.status !== 'healthy') {
          testResult.issues.push(`Backup agent ${backup.agentId} is not healthy`)
          testResult.recommendations.push(`Check backup agent ${backup.agentId} status`)
        }
      }

      // Test data consistency
      if (this.config.enableDataReplication) {
        for (const backup of backupAgents) {
          if (backup.dataConsistency < 0.95) {
            testResult.issues.push(`Data consistency low for backup ${backup.agentId}`)
            testResult.recommendations.push('Improve data replication')
          }
        }
      }

      testResult.failoverTime = Date.now() - startTime
      testResult.success = testResult.issues.length === 0

      // Update test schedule
      plan.testSchedule.lastTest = Date.now()
      plan.testSchedule.nextTest = this.calculateNextTestTime(plan.testSchedule.frequency)
      plan.testSchedule.testResults.push(testResult)

      // Keep only last 10 test results
      if (plan.testSchedule.testResults.length > 10) {
        plan.testSchedule.testResults = plan.testSchedule.testResults.slice(-10)
      }

      this.emit('failover_test_completed', testResult)
      console.log(`🧪 Failover test completed: ${testResult.success ? 'PASSED' : 'FAILED'}`)

      return testResult

    } catch (error) {
      testResult.issues.push(`Test execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      testResult.recommendations.push('Review test execution environment')
      
      this.emit('failover_test_failed', { testResult, error })
      return testResult
    }
  }

  /**
   * Get failover metrics
   */
  getFailoverMetrics(): FailoverMetrics {
    return { ...this.metrics }
  }

  /**
   * Get failover plans
   */
  getFailoverPlans(): Map<string, FailoverPlan> {
    return new Map(this.failoverPlans)
  }

  /**
   * Private helper methods
   */
  private async findBackupAgents(primaryAgent: AgentInstance): Promise<BackupAgent[]> {
    const discovery = await this.discoveryService.discoverAgents({
      agentType: primaryAgent.agentType,
      capabilities: primaryAgent.capabilities.map(c => c.name),
      excludeAgents: [primaryAgent.agentId]
    })

    const backupAgents: BackupAgent[] = []
    const maxBackups = Math.max(1, Math.floor(discovery.agents.length * this.config.backupAgentRatio))

    for (let i = 0; i < Math.min(maxBackups, discovery.agents.length); i++) {
      const agent = discovery.agents[i]
      backupAgents.push({
        agentId: agent.agentId,
        primaryAgent: primaryAgent.agentId,
        readinessLevel: 'warm',
        lastSyncTime: Date.now(),
        dataConsistency: 0.95,
        activationTime: 5000 // 5 seconds
      })
    }

    return backupAgents
  }

  private createDefaultTriggers(): FailoverTrigger[] {
    return [
      {
        type: 'health_check_failure',
        threshold: null,
        consecutiveFailures: this.config.maxFailureThreshold,
        timeWindow: 60000 // 1 minute
      },
      {
        type: 'response_timeout',
        threshold: 30000, // 30 seconds
        consecutiveFailures: 3,
        timeWindow: 180000 // 3 minutes
      },
      {
        type: 'error_rate_threshold',
        threshold: 0.5, // 50% error rate
        consecutiveFailures: 1,
        timeWindow: 300000 // 5 minutes
      }
    ]
  }

  private createRecoverySteps(primaryAgentId: string, backupAgents: BackupAgent[]): RecoveryStep[] {
    return [
      {
        stepId: 'activate_backup',
        order: 1,
        action: 'activate_backup',
        parameters: { backupAgents },
        timeout: 10000,
        rollbackOnFailure: true
      },
      {
        stepId: 'transfer_state',
        order: 2,
        action: 'transfer_state',
        parameters: { primaryAgent: primaryAgentId },
        timeout: 15000,
        rollbackOnFailure: true
      },
      {
        stepId: 'redirect_traffic',
        order: 3,
        action: 'redirect_traffic',
        parameters: { fromAgent: primaryAgentId },
        timeout: 5000,
        rollbackOnFailure: false
      }
    ]
  }

  private createRollbackSteps(primaryAgentId: string, backupAgents: BackupAgent[]): RollbackStep[] {
    return [
      {
        stepId: 'restore_primary',
        order: 1,
        action: 'restore_primary',
        parameters: { primaryAgent: primaryAgentId },
        timeout: 10000
      },
      {
        stepId: 'transfer_state_back',
        order: 2,
        action: 'transfer_state_back',
        parameters: { backupAgents },
        timeout: 15000
      },
      {
        stepId: 'redirect_traffic_back',
        order: 3,
        action: 'redirect_traffic_back',
        parameters: { toAgent: primaryAgentId },
        timeout: 5000
      }
    ]
  }

  private async executeRecoverySteps(plan: FailoverPlan, failoverEvent: FailoverEvent): Promise<BackupAgent> {
    const backupAgents = this.backupAgents.get(plan.primaryAgent) || []
    if (backupAgents.length === 0) {
      throw new Error('No backup agents available')
    }

    // Select best backup agent
    const selectedBackup = backupAgents.reduce((best, current) => 
      current.dataConsistency > best.dataConsistency ? current : best
    )

    // Execute recovery steps
    for (const step of plan.recoverySteps) {
      await this.executeRecoveryStep(step, plan.primaryAgent, selectedBackup)
    }

    return selectedBackup
  }

  private async executeRecoveryStep(step: RecoveryStep, primaryAgentId: string, backupAgent: BackupAgent): Promise<void> {
    console.log(`🔄 Executing recovery step: ${step.action}`)

    switch (step.action) {
      case 'activate_backup':
        // Activate backup agent
        this.discoveryService.updateAgentStatus(backupAgent.agentId, 'healthy')
        break
      
      case 'transfer_state':
        // Transfer state from primary to backup
        if (this.config.enableStateTransfer) {
          // Simulate state transfer
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
        break
      
      case 'redirect_traffic':
        // Update load balancer to redirect traffic
        // This would integrate with the load balancer
        break
      
      case 'notify_stakeholders':
        this.emit('stakeholders_notified', { primaryAgentId, backupAgent: backupAgent.agentId })
        break
    }
  }

  private async executeRollbackStep(step: RollbackStep, primaryAgentId: string): Promise<void> {
    console.log(`🔄 Executing rollback step: ${step.action}`)

    switch (step.action) {
      case 'restore_primary':
        // Restore primary agent
        this.discoveryService.updateAgentStatus(primaryAgentId, 'healthy')
        break
      
      case 'transfer_state_back':
        // Transfer state back to primary
        if (this.config.enableStateTransfer) {
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
        break
      
      case 'redirect_traffic_back':
        // Redirect traffic back to primary
        break
    }
  }

  private startFailureDetection(): void {
    this.monitoringTimer = setInterval(() => {
      this.detectFailures()
    }, this.config.failureDetectionInterval)
  }

  private detectFailures(): void {
    // Monitor agent health and trigger failover if needed
    for (const [primaryAgentId, plan] of this.failoverPlans.entries()) {
      const agent = this.discoveryService.getAgent(primaryAgentId)
      
      if (!agent || agent.status === 'unhealthy') {
        // Check if failover is already active
        if (!this.activeFailovers.has(primaryAgentId)) {
          this.triggerFailover(primaryAgentId, 'Agent health check failed', false)
            .catch(error => console.error('Auto-failover failed:', error))
        }
      }
    }
  }

  private calculateNextTestTime(frequency: TestSchedule['frequency']): number {
    const now = Date.now()
    switch (frequency) {
      case 'daily':
        return now + 24 * 60 * 60 * 1000
      case 'weekly':
        return now + 7 * 24 * 60 * 60 * 1000
      case 'monthly':
        return now + 30 * 24 * 60 * 60 * 1000
      default:
        return now + 7 * 24 * 60 * 60 * 1000
    }
  }

  private updateMetrics(failoverEvent: FailoverEvent, failoverTime: number, success: boolean): void {
    this.metrics.totalFailovers++
    
    if (success) {
      this.metrics.successfulFailovers++
    }
    
    this.metrics.averageFailoverTime = 
      (this.metrics.averageFailoverTime * (this.metrics.totalFailovers - 1) + failoverTime) / 
      this.metrics.totalFailovers

    if (failoverEvent.impact.dataLoss) {
      this.metrics.dataLossIncidents++
    }

    // Calculate MTBF and MTTR
    if (this.failoverHistory.length > 1) {
      const failures = this.failoverHistory.filter(e => e.type === 'agent_failure')
      if (failures.length > 1) {
        const timeBetweenFailures = failures.slice(1).map((failure, index) => 
          failure.timestamp - failures[index].timestamp
        )
        this.metrics.mtbf = timeBetweenFailures.reduce((sum, time) => sum + time, 0) / timeBetweenFailures.length
      }

      const recoveries = this.failoverHistory.filter(e => e.type === 'failover_completed')
      if (recoveries.length > 0) {
        const recoveryTimes = recoveries.map(r => r.recovery.estimatedTime)
        this.metrics.mttr = recoveryTimes.reduce((sum, time) => sum + time, 0) / recoveryTimes.length
      }
    }
  }

  private setupEventHandlers(): void {
    // Listen for agent status changes
    this.discoveryService.on('agent_status_changed', (event) => {
      if (event.newStatus === 'unhealthy' && this.config.enableAutoFailover) {
        const plan = this.failoverPlans.get(event.agentId)
        if (plan && !this.activeFailovers.has(event.agentId)) {
          this.triggerFailover(event.agentId, 'Agent status changed to unhealthy', false)
            .catch(error => console.error('Auto-failover failed:', error))
        }
      }
    })
  }

  /**
   * Shutdown failover manager
   */
  shutdown(): void {
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer)
    }

    this.failoverPlans.clear()
    this.backupAgents.clear()
    this.activeFailovers.clear()
    this.removeAllListeners()
    
    console.log('🛡️ Failover Manager shutdown complete')
  }
}

export default FailoverManager
