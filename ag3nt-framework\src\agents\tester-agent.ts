/**
 * AG3NT Framework - Tester Agent
 * 
 * Specialized agent for comprehensive testing and quality assurance.
 * Handles unit testing, integration testing, e2e testing, and quality metrics.
 * 
 * Features:
 * - Automated test generation and execution
 * - Test strategy planning and optimization
 * - Quality metrics and coverage analysis
 * - Performance and security testing
 * - Test maintenance and refactoring
 * - CI/CD integration
 */

import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent"
import { aiService } from "../../ai-service"

export interface TesterInput {
  task: TestingTask
  codebase: TestableCodebase
  requirements: TestingRequirements
  strategy: TestingStrategy
}

export interface TestingTask {
  taskId: string
  type: 'unit' | 'integration' | 'e2e' | 'performance' | 'security' | 'accessibility' | 'regression'
  title: string
  description: string
  priority: 'critical' | 'high' | 'medium' | 'low'
  scope: TestingScope
  acceptanceCriteria: string[]
  deadline?: string
}

export interface TestingScope {
  components: string[]
  features: string[]
  apis: string[]
  workflows: string[]
  browsers?: string[]
  devices?: string[]
}

export interface TestableCodebase {
  structure: CodebaseStructure
  components: TestableComponent[]
  apis: TestableAPI[]
  dependencies: TestDependency[]
  existingTests: ExistingTest[]
}

export interface CodebaseStructure {
  language: string
  framework: string
  testFramework: string
  testDirectory: string
  sourceDirectory: string
  buildDirectory: string
}

export interface TestableComponent {
  name: string
  path: string
  type: 'component' | 'service' | 'utility' | 'model'
  complexity: 'simple' | 'medium' | 'complex'
  dependencies: string[]
  publicMethods: ComponentMethod[]
  testCoverage: number
}

export interface ComponentMethod {
  name: string
  parameters: MethodParameter[]
  returnType: string
  complexity: number
  sideEffects: boolean
}

export interface MethodParameter {
  name: string
  type: string
  optional: boolean
  validation: string[]
}

export interface TestableAPI {
  endpoint: string
  method: string
  parameters: APITestParameter[]
  responses: APITestResponse[]
  authentication: boolean
  rateLimit: boolean
}

export interface APITestParameter {
  name: string
  type: string
  location: 'path' | 'query' | 'body' | 'header'
  required: boolean
  validation: ValidationRule[]
}

export interface ValidationRule {
  type: string
  value: any
  message: string
}

export interface APITestResponse {
  status: number
  schema: any
  examples: any[]
  headers: Record<string, string>
}

export interface TestDependency {
  name: string
  version: string
  type: 'testing' | 'mocking' | 'assertion' | 'runner'
  purpose: string
}

export interface ExistingTest {
  file: string
  type: 'unit' | 'integration' | 'e2e'
  coverage: number
  lastRun: string
  status: 'passing' | 'failing' | 'skipped'
  duration: number
}

export interface TestingRequirements {
  coverage: CoverageRequirements
  performance: PerformanceTestRequirements
  security: SecurityTestRequirements
  accessibility: AccessibilityTestRequirements
  browsers: BrowserTestRequirements
  devices: DeviceTestRequirements
  compliance: ComplianceTestRequirements
}

export interface CoverageRequirements {
  minimum: number
  target: number
  statements: number
  branches: number
  functions: number
  lines: number
}

export interface PerformanceTestRequirements {
  loadTesting: boolean
  stressTesting: boolean
  enduranceTesting: boolean
  spikeTesting: boolean
  volumeTesting: boolean
  thresholds: PerformanceThresholds
}

export interface PerformanceThresholds {
  responseTime: number
  throughput: number
  errorRate: number
  resourceUsage: ResourceThresholds
}

export interface ResourceThresholds {
  cpu: number
  memory: number
  disk: number
  network: number
}

export interface SecurityTestRequirements {
  vulnerabilityScanning: boolean
  penetrationTesting: boolean
  authenticationTesting: boolean
  authorizationTesting: boolean
  dataValidationTesting: boolean
  encryptionTesting: boolean
}

export interface AccessibilityTestRequirements {
  wcagLevel: 'A' | 'AA' | 'AAA'
  screenReaderTesting: boolean
  keyboardNavigationTesting: boolean
  colorContrastTesting: boolean
  focusManagementTesting: boolean
}

export interface BrowserTestRequirements {
  browsers: BrowserConfig[]
  crossBrowserTesting: boolean
  responsiveTesting: boolean
}

export interface BrowserConfig {
  name: string
  versions: string[]
  platforms: string[]
}

export interface DeviceTestRequirements {
  mobile: boolean
  tablet: boolean
  desktop: boolean
  devices: DeviceConfig[]
}

export interface DeviceConfig {
  name: string
  type: 'mobile' | 'tablet' | 'desktop'
  resolution: string
  userAgent: string
}

export interface ComplianceTestRequirements {
  standards: string[]
  regulations: string[]
  certifications: string[]
  auditing: boolean
}

export interface TestingStrategy {
  approach: 'tdd' | 'bdd' | 'atdd' | 'hybrid'
  pyramid: TestPyramid
  automation: AutomationStrategy
  reporting: ReportingStrategy
  maintenance: MaintenanceStrategy
}

export interface TestPyramid {
  unit: number // percentage
  integration: number // percentage
  e2e: number // percentage
  manual: number // percentage
}

export interface AutomationStrategy {
  level: 'none' | 'partial' | 'full'
  triggers: AutomationTrigger[]
  environments: string[]
  parallelization: boolean
}

export interface AutomationTrigger {
  event: 'commit' | 'pull_request' | 'deploy' | 'schedule'
  conditions: string[]
  actions: string[]
}

export interface ReportingStrategy {
  formats: string[]
  destinations: string[]
  frequency: string
  stakeholders: string[]
}

export interface MaintenanceStrategy {
  frequency: string
  refactoring: boolean
  cleanup: boolean
  optimization: boolean
}

export interface TesterResult {
  taskId: string
  status: 'completed' | 'failed' | 'partial'
  testSuites: TestSuite[]
  coverage: CoverageReport
  performance: PerformanceReport
  security: SecurityTestReport
  accessibility: AccessibilityReport
  quality: QualityReport
  recommendations: TestingRecommendation[]
}

export interface TestSuite {
  name: string
  type: 'unit' | 'integration' | 'e2e' | 'performance' | 'security'
  tests: TestCase[]
  coverage: number
  duration: number
  status: 'passed' | 'failed' | 'skipped'
}

export interface TestCase {
  name: string
  description: string
  status: 'passed' | 'failed' | 'skipped'
  duration: number
  assertions: number
  error?: TestError
}

export interface TestError {
  message: string
  stack: string
  type: string
  expected?: any
  actual?: any
}

export interface CoverageReport {
  overall: number
  statements: number
  branches: number
  functions: number
  lines: number
  files: FileCoverage[]
}

export interface FileCoverage {
  file: string
  coverage: number
  statements: CoverageMetric
  branches: CoverageMetric
  functions: CoverageMetric
  lines: CoverageMetric
}

export interface CoverageMetric {
  covered: number
  total: number
  percentage: number
}

export interface PerformanceReport {
  loadTest: LoadTestResult
  stressTest: StressTestResult
  enduranceTest: EnduranceTestResult
  metrics: PerformanceMetrics
}

export interface LoadTestResult {
  users: number
  duration: number
  responseTime: ResponseTimeMetrics
  throughput: number
  errorRate: number
}

export interface ResponseTimeMetrics {
  min: number
  max: number
  avg: number
  p50: number
  p95: number
  p99: number
}

export interface StressTestResult {
  breakingPoint: number
  recoveryTime: number
  degradation: DegradationMetrics
}

export interface DegradationMetrics {
  responseTime: number
  throughput: number
  errorRate: number
}

export interface EnduranceTestResult {
  duration: number
  memoryLeaks: boolean
  performanceDrift: number
  stability: number
}

export interface PerformanceMetrics {
  cpu: number
  memory: number
  disk: number
  network: number
  database: DatabaseMetrics
}

export interface DatabaseMetrics {
  connections: number
  queries: number
  slowQueries: number
  deadlocks: number
}

export interface SecurityTestReport {
  vulnerabilities: SecurityVulnerability[]
  compliance: SecurityCompliance[]
  score: number
  recommendations: string[]
}

export interface SecurityVulnerability {
  type: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  location: string
  cve?: string
  fix: string
}

export interface SecurityCompliance {
  standard: string
  requirement: string
  status: 'compliant' | 'non-compliant' | 'partial'
  evidence: string
}

export interface AccessibilityReport {
  score: number
  level: 'A' | 'AA' | 'AAA'
  violations: AccessibilityViolation[]
  recommendations: string[]
}

export interface AccessibilityViolation {
  rule: string
  severity: 'error' | 'warning' | 'info'
  element: string
  description: string
  fix: string
}

export interface QualityReport {
  score: number
  maintainability: number
  reliability: number
  security: number
  performance: number
  coverage: number
  debt: TechnicalDebt
}

export interface TechnicalDebt {
  total: number
  critical: number
  major: number
  minor: number
  effort: string
}

export interface TestingRecommendation {
  type: 'coverage' | 'performance' | 'security' | 'maintenance' | 'strategy'
  priority: 'high' | 'medium' | 'low'
  description: string
  action: string
  effort: string
  impact: string
}

/**
 * Tester Agent - Comprehensive testing and quality assurance
 */
export class TesterAgent extends BaseAgent {
  private readonly testingSteps = [
    'analyze_testability', 'plan_testing_strategy', 'generate_tests',
    'execute_unit_tests', 'execute_integration_tests', 'execute_e2e_tests',
    'performance_testing', 'security_testing', 'accessibility_testing', 'generate_reports'
  ]

  constructor(config: Partial<AgentConfig> = {}) {
    super('tester', {
      capabilities: {
        requiredCapabilities: [
          'test_generation',
          'test_execution',
          'quality_assurance',
          'performance_testing',
          'security_testing',
          'accessibility_testing',
          'test_automation',
          'coverage_analysis'
        ],
        contextFilters: ['testing', 'quality', 'code', 'performance', 'security'],
        mcpEnhanced: true,
        sequentialThinking: true,
        contextEnrichment: true,
        ragIntegration: true
      },
      ...config
    })
  }

  /**
   * Execute testing workflow
   */
  protected async executeWorkflow(state: AgentState): Promise<AgentState> {
    const input = state.input as TesterInput
    
    console.log(`🧪 Starting testing workflow: ${input.task.title}`)

    // Execute testing steps sequentially
    for (const stepId of this.testingSteps) {
      console.log(`🔄 Executing step: ${stepId}`)
      
      const stepResult = await this.executeStepWithContext(stepId, input)
      
      if (stepResult.needsInput) {
        state.needsInput = true
        state.question = stepResult.question
        break
      }

      // Update progress
      state.metadata.completedSteps++
      state.metadata.lastUpdated = new Date().toISOString()
      
      this.emit('step_completed', {
        agentId: this.agentId,
        stepId,
        progress: state.metadata.completedSteps / state.metadata.totalSteps
      })
    }

    // Mark as completed if no input needed
    if (!state.needsInput) {
      state.completed = true
      console.log(`✅ Testing completed: ${input.task.title}`)
    }

    return state
  }

  /**
   * Execute individual testing step with context enhancement
   */
  private async executeStepWithContext(stepId: string, input?: any): Promise<any> {
    // Get enhanced context from context engine
    const enhancedState = await this.getEnhancedContext(stepId)

    // Execute step with MCP enhancement
    switch (stepId) {
      case 'analyze_testability':
        return await this.analyzeTestabilityWithMCP(enhancedState, input)
      case 'plan_testing_strategy':
        return await this.planTestingStrategyWithMCP(enhancedState)
      case 'generate_tests':
        return await this.generateTestsWithMCP(enhancedState)
      case 'execute_unit_tests':
        return await this.executeUnitTestsWithMCP(enhancedState)
      case 'execute_integration_tests':
        return await this.executeIntegrationTestsWithMCP(enhancedState)
      case 'execute_e2e_tests':
        return await this.executeE2ETestsWithMCP(enhancedState)
      case 'performance_testing':
        return await this.performanceTestingWithMCP(enhancedState)
      case 'security_testing':
        return await this.securityTestingWithMCP(enhancedState)
      case 'accessibility_testing':
        return await this.accessibilityTestingWithMCP(enhancedState)
      case 'generate_reports':
        return await this.generateReportsWithMCP(enhancedState)
      default:
        throw new Error(`Unknown testing step: ${stepId}`)
    }
  }

  /**
   * Get total steps for progress tracking
   */
  protected getTotalSteps(): number {
    return this.testingSteps.length
  }

  /**
   * Get relevant documentation for testing
   */
  protected async getRelevantDocumentation(): Promise<Record<string, any>> {
    return {
      testing: 'Software testing methodologies and best practices',
      automation: 'Test automation frameworks and strategies',
      performance: 'Performance testing and load testing techniques',
      security: 'Security testing and vulnerability assessment',
      accessibility: 'Accessibility testing and WCAG compliance',
      quality: 'Quality assurance and metrics analysis'
    }
  }

  // ============================================================================
  // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
  // ============================================================================

  private async analyzeTestabilityWithMCP(state: any, input: TesterInput): Promise<any> {
    const analysis = await aiService.analyzeTestability(
      input.codebase,
      input.requirements,
      input.strategy
    )

    this.state!.results.testabilityAnalysis = analysis
    
    return {
      results: analysis,
      needsInput: false,
      completed: false
    }
  }

  private async planTestingStrategyWithMCP(state: any): Promise<any> {
    const testabilityAnalysis = this.state!.results.testabilityAnalysis
    
    const strategy = await aiService.planTestingStrategy(
      testabilityAnalysis,
      this.state!.input.requirements
    )

    this.state!.results.testingStrategy = strategy
    
    return {
      results: strategy,
      needsInput: false,
      completed: false
    }
  }

  private async generateTestsWithMCP(state: any): Promise<any> {
    const strategy = this.state!.results.testingStrategy
    
    const tests = await aiService.generateTests(
      strategy,
      this.state!.input.codebase
    )

    this.state!.results.generatedTests = tests
    
    return {
      results: tests,
      needsInput: false,
      completed: false
    }
  }

  private async executeUnitTestsWithMCP(state: any): Promise<any> {
    const tests = this.state!.results.generatedTests
    
    const unitTestResults = await aiService.executeUnitTests(tests.unit)

    this.state!.results.unitTestResults = unitTestResults
    
    return {
      results: unitTestResults,
      needsInput: false,
      completed: false
    }
  }

  private async executeIntegrationTestsWithMCP(state: any): Promise<any> {
    const tests = this.state!.results.generatedTests
    
    const integrationTestResults = await aiService.executeIntegrationTests(tests.integration)

    this.state!.results.integrationTestResults = integrationTestResults
    
    return {
      results: integrationTestResults,
      needsInput: false,
      completed: false
    }
  }

  private async executeE2ETestsWithMCP(state: any): Promise<any> {
    const tests = this.state!.results.generatedTests
    
    const e2eTestResults = await aiService.executeE2ETests(tests.e2e)

    this.state!.results.e2eTestResults = e2eTestResults
    
    return {
      results: e2eTestResults,
      needsInput: false,
      completed: false
    }
  }

  private async performanceTestingWithMCP(state: any): Promise<any> {
    const requirements = this.state!.input.requirements.performance
    
    const performanceResults = await aiService.performPerformanceTesting(
      requirements,
      this.state!.input.codebase
    )

    this.state!.results.performanceResults = performanceResults
    
    return {
      results: performanceResults,
      needsInput: false,
      completed: false
    }
  }

  private async securityTestingWithMCP(state: any): Promise<any> {
    const requirements = this.state!.input.requirements.security
    
    const securityResults = await aiService.performSecurityTesting(
      requirements,
      this.state!.input.codebase
    )

    this.state!.results.securityResults = securityResults
    
    return {
      results: securityResults,
      needsInput: false,
      completed: false
    }
  }

  private async accessibilityTestingWithMCP(state: any): Promise<any> {
    const requirements = this.state!.input.requirements.accessibility
    
    const accessibilityResults = await aiService.performAccessibilityTesting(
      requirements,
      this.state!.input.codebase
    )

    this.state!.results.accessibilityResults = accessibilityResults
    
    return {
      results: accessibilityResults,
      needsInput: false,
      completed: false
    }
  }

  private async generateReportsWithMCP(state: any): Promise<any> {
    const allResults = {
      unit: this.state!.results.unitTestResults,
      integration: this.state!.results.integrationTestResults,
      e2e: this.state!.results.e2eTestResults,
      performance: this.state!.results.performanceResults,
      security: this.state!.results.securityResults,
      accessibility: this.state!.results.accessibilityResults
    }
    
    const reports = await aiService.generateTestingReports(
      allResults,
      this.state!.input.task
    )

    this.state!.results.reports = reports
    
    return {
      results: reports,
      needsInput: false,
      completed: true // Final step
    }
  }
}

// Export for easy access
export { TesterAgent as default }
