"use strict";
/**
 * AG3NT Framework - Agent Discovery Service
 *
 * Intelligent agent discovery system that automatically finds, registers,
 * and manages agent instances across distributed environments.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentDiscoveryService = void 0;
const events_1 = require("events");
/**
 * Agent Discovery Service
 */
class AgentDiscoveryService extends events_1.EventEmitter {
    constructor(config = {}) {
        super();
        this.agents = new Map();
        this.config = {
            enableAutoDiscovery: true,
            discoveryInterval: 30000, // 30 seconds
            healthCheckInterval: 10000, // 10 seconds
            maxRetries: 3,
            timeoutMs: 5000,
            enableServiceMesh: false,
            enableLoadBalancing: true,
            ...config
        };
        if (this.config.enableServiceMesh) {
            this.serviceMesh = new ServiceMeshIntegration();
        }
    }
    /**
     * Start discovery service
     */
    async start() {
        console.log('🔍 Starting Agent Discovery Service');
        if (this.config.enableAutoDiscovery) {
            this.startAutoDiscovery();
        }
        this.startHealthChecks();
        if (this.serviceMesh) {
            await this.serviceMesh.initialize();
        }
        this.emit('discovery_started');
    }
    /**
     * Register agent instance
     */
    async registerAgent(agent) {
        const fullAgent = {
            agentId: agent.agentId,
            agentType: agent.agentType,
            version: agent.version || '1.0.0',
            capabilities: agent.capabilities || [],
            endpoint: agent.endpoint,
            status: 'healthy',
            metadata: agent.metadata || {
                hostname: 'localhost',
                region: 'local',
                zone: 'default',
                environment: 'development',
                tags: [],
                customProperties: {}
            },
            performance: agent.performance || {
                averageResponseTime: 0,
                successRate: 1.0,
                currentLoad: 0,
                maxLoad: 10,
                throughput: 0,
                errorRate: 0,
                lastUpdated: Date.now()
            },
            lastSeen: Date.now(),
            registeredAt: Date.now()
        };
        this.agents.set(fullAgent.agentId, fullAgent);
        this.emit('agent_registered', fullAgent);
        console.log(`🤖 Registered agent: ${fullAgent.agentId} (${fullAgent.agentType})`);
    }
    /**
     * Discover agents based on query
     */
    async discoverAgents(query = {}) {
        const startTime = Date.now();
        const matchingAgents = [];
        for (const agent of this.agents.values()) {
            if (this.matchesQuery(agent, query)) {
                matchingAgents.push(agent);
            }
        }
        // Generate recommendations
        const recommendations = this.generateRecommendations(matchingAgents, query);
        const result = {
            agents: matchingAgents,
            totalFound: matchingAgents.length,
            queryTime: Date.now() - startTime,
            recommendations
        };
        this.emit('agents_discovered', result);
        return result;
    }
    /**
     * Find best agent for task
     */
    async findBestAgent(query) {
        const discovery = await this.discoverAgents(query);
        if (discovery.recommendations.length === 0) {
            return null;
        }
        // Return the highest scored agent
        const bestRecommendation = discovery.recommendations[0];
        return this.agents.get(bestRecommendation.agentId) || null;
    }
    /**
     * Update agent performance metrics
     */
    updateAgentPerformance(agentId, performance) {
        const agent = this.agents.get(agentId);
        if (!agent)
            return;
        agent.performance = {
            ...agent.performance,
            ...performance,
            lastUpdated: Date.now()
        };
        agent.lastSeen = Date.now();
        this.emit('agent_performance_updated', { agentId, performance: agent.performance });
    }
    /**
     * Update agent status
     */
    updateAgentStatus(agentId, status) {
        const agent = this.agents.get(agentId);
        if (!agent)
            return;
        const oldStatus = agent.status;
        agent.status = status;
        agent.lastSeen = Date.now();
        this.emit('agent_status_changed', { agentId, oldStatus, newStatus: status });
        if (status === 'unhealthy') {
            console.warn(`⚠️ Agent ${agentId} marked as unhealthy`);
        }
    }
    /**
     * Unregister agent
     */
    unregisterAgent(agentId) {
        const agent = this.agents.get(agentId);
        if (agent) {
            this.agents.delete(agentId);
            this.emit('agent_unregistered', agent);
            console.log(`🗑️ Unregistered agent: ${agentId}`);
        }
    }
    /**
     * Get all registered agents
     */
    getAllAgents() {
        return Array.from(this.agents.values());
    }
    /**
     * Get agent by ID
     */
    getAgent(agentId) {
        return this.agents.get(agentId);
    }
    /**
     * Get discovery statistics
     */
    getDiscoveryStats() {
        const agents = Array.from(this.agents.values());
        const healthyAgents = agents.filter(a => a.status === 'healthy');
        const agentsByType = new Map();
        for (const agent of agents) {
            agentsByType.set(agent.agentType, (agentsByType.get(agent.agentType) || 0) + 1);
        }
        return {
            totalAgents: agents.length,
            healthyAgents: healthyAgents.length,
            unhealthyAgents: agents.filter(a => a.status === 'unhealthy').length,
            agentsByType: Object.fromEntries(agentsByType),
            averageLoad: agents.reduce((sum, a) => sum + a.performance.currentLoad, 0) / agents.length,
            averageResponseTime: agents.reduce((sum, a) => sum + a.performance.averageResponseTime, 0) / agents.length
        };
    }
    /**
     * Private helper methods
     */
    startAutoDiscovery() {
        this.discoveryTimer = setInterval(async () => {
            try {
                await this.performAutoDiscovery();
            }
            catch (error) {
                console.error('Auto-discovery error:', error);
            }
        }, this.config.discoveryInterval);
    }
    startHealthChecks() {
        this.healthCheckTimer = setInterval(async () => {
            try {
                await this.performHealthChecks();
            }
            catch (error) {
                console.error('Health check error:', error);
            }
        }, this.config.healthCheckInterval);
    }
    async performAutoDiscovery() {
        // In a real implementation, this would:
        // 1. Scan network for agent services
        // 2. Query service registries (Consul, etcd, etc.)
        // 3. Check cloud provider service discovery
        // 4. Integrate with container orchestrators (Kubernetes, Docker Swarm)
        this.emit('auto_discovery_completed', { discovered: 0 });
    }
    async performHealthChecks() {
        const healthCheckPromises = [];
        for (const agent of this.agents.values()) {
            healthCheckPromises.push(this.checkAgentHealth(agent));
        }
        await Promise.allSettled(healthCheckPromises);
    }
    async checkAgentHealth(agent) {
        try {
            // In a real implementation, this would make HTTP/gRPC calls to agent endpoints
            const isHealthy = await this.pingAgent(agent.endpoint);
            if (isHealthy) {
                if (agent.status === 'unhealthy') {
                    this.updateAgentStatus(agent.agentId, 'healthy');
                    console.log(`✅ Agent ${agent.agentId} recovered`);
                }
                agent.lastSeen = Date.now();
            }
            else {
                this.updateAgentStatus(agent.agentId, 'unhealthy');
            }
        }
        catch (error) {
            this.updateAgentStatus(agent.agentId, 'unhealthy');
        }
    }
    async pingAgent(endpoint) {
        // Simulate health check
        return Math.random() > 0.1; // 90% success rate
    }
    matchesQuery(agent, query) {
        // Agent type filter
        if (query.agentType && agent.agentType !== query.agentType) {
            return false;
        }
        // Capabilities filter
        if (query.capabilities && query.capabilities.length > 0) {
            const agentCapabilities = agent.capabilities.map(c => c.name);
            const hasAllCapabilities = query.capabilities.every(cap => agentCapabilities.includes(cap));
            if (!hasAllCapabilities) {
                return false;
            }
        }
        // Region filter
        if (query.region && agent.metadata.region !== query.region) {
            return false;
        }
        // Environment filter
        if (query.environment && agent.metadata.environment !== query.environment) {
            return false;
        }
        // Tags filter
        if (query.tags && query.tags.length > 0) {
            const hasAllTags = query.tags.every(tag => agent.metadata.tags.includes(tag));
            if (!hasAllTags) {
                return false;
            }
        }
        // Proficiency filter
        if (query.minProficiency) {
            const maxProficiency = Math.max(...agent.capabilities.map(c => c.proficiency));
            if (maxProficiency < query.minProficiency) {
                return false;
            }
        }
        // Load filter
        if (query.maxLoad && agent.performance.currentLoad > query.maxLoad) {
            return false;
        }
        // Exclude agents filter
        if (query.excludeAgents && query.excludeAgents.includes(agent.agentId)) {
            return false;
        }
        // Only include healthy agents
        if (agent.status !== 'healthy') {
            return false;
        }
        return true;
    }
    generateRecommendations(agents, query) {
        const recommendations = [];
        for (const agent of agents) {
            const score = this.calculateAgentScore(agent, query);
            const reasoning = this.generateRecommendationReasoning(agent, query, score);
            recommendations.push({
                agentId: agent.agentId,
                score,
                reasoning,
                estimatedPerformance: {
                    responseTime: agent.performance.averageResponseTime,
                    successProbability: agent.performance.successRate,
                    loadImpact: agent.performance.currentLoad / agent.performance.maxLoad
                }
            });
        }
        // Sort by score (highest first)
        recommendations.sort((a, b) => b.score - a.score);
        return recommendations;
    }
    calculateAgentScore(agent, query) {
        let score = 0;
        let factors = 0;
        // Performance score (40% weight)
        const performanceScore = (agent.performance.successRate * 0.4 +
            (1 - agent.performance.errorRate) * 0.3 +
            (1 - agent.performance.currentLoad / agent.performance.maxLoad) * 0.3);
        score += performanceScore * 0.4;
        factors += 0.4;
        // Capability match score (30% weight)
        if (query.capabilities && query.capabilities.length > 0) {
            const capabilityScore = this.calculateCapabilityScore(agent, query.capabilities);
            score += capabilityScore * 0.3;
            factors += 0.3;
        }
        // Response time score (20% weight)
        const responseTimeScore = Math.max(0, 1 - agent.performance.averageResponseTime / 10000); // Normalize to 10s max
        score += responseTimeScore * 0.2;
        factors += 0.2;
        // Availability score (10% weight)
        const availabilityScore = 1 - (agent.performance.currentLoad / agent.performance.maxLoad);
        score += availabilityScore * 0.1;
        factors += 0.1;
        return factors > 0 ? score / factors : 0;
    }
    calculateCapabilityScore(agent, requiredCapabilities) {
        let totalScore = 0;
        let matchedCapabilities = 0;
        for (const requiredCap of requiredCapabilities) {
            const agentCap = agent.capabilities.find(c => c.name === requiredCap);
            if (agentCap) {
                totalScore += agentCap.proficiency;
                matchedCapabilities++;
            }
        }
        return matchedCapabilities > 0 ? totalScore / matchedCapabilities : 0;
    }
    generateRecommendationReasoning(agent, query, score) {
        const reasoning = [];
        if (score > 0.8) {
            reasoning.push('High overall performance score');
        }
        if (agent.performance.successRate > 0.95) {
            reasoning.push('Excellent success rate');
        }
        if (agent.performance.currentLoad / agent.performance.maxLoad < 0.5) {
            reasoning.push('Low current load');
        }
        if (agent.performance.averageResponseTime < 1000) {
            reasoning.push('Fast response time');
        }
        if (query.capabilities && query.capabilities.length > 0) {
            const matchedCaps = agent.capabilities.filter(c => query.capabilities.includes(c.name) && c.proficiency > 0.8);
            if (matchedCaps.length > 0) {
                reasoning.push(`High proficiency in ${matchedCaps.map(c => c.name).join(', ')}`);
            }
        }
        return reasoning;
    }
    /**
     * Shutdown discovery service
     */
    async shutdown() {
        if (this.discoveryTimer) {
            clearInterval(this.discoveryTimer);
        }
        if (this.healthCheckTimer) {
            clearInterval(this.healthCheckTimer);
        }
        if (this.serviceMesh) {
            await this.serviceMesh.shutdown();
        }
        this.agents.clear();
        this.removeAllListeners();
        console.log('🔍 Agent Discovery Service shutdown complete');
    }
}
exports.AgentDiscoveryService = AgentDiscoveryService;
/**
 * Service Mesh Integration
 */
class ServiceMeshIntegration {
    async initialize() {
        // Initialize service mesh integration
        console.log('🕸️ Service mesh integration initialized');
    }
    async shutdown() {
        // Cleanup service mesh integration
        console.log('🕸️ Service mesh integration shutdown');
    }
}
exports.default = AgentDiscoveryService;
//# sourceMappingURL=agent-discovery-service.js.map