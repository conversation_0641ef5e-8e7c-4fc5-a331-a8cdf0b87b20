/**
 * AG3NT Framework - Advanced Task Delegation System
 *
 * Sophisticated delegation patterns with authority levels, capability matching,
 * and accountability tracking for multi-agent coordination.
 */
import { EventEmitter } from "events";
export interface DelegationConfig {
    enableHierarchicalDelegation: boolean;
    enablePeerDelegation: boolean;
    maxDelegationDepth: number;
    delegationTimeout: number;
    requireConfirmation: boolean;
    enableRollback: boolean;
}
export interface AgentAuthority {
    agentId: string;
    authorityLevel: number;
    capabilities: AgentCapability[];
    delegationRights: DelegationRight[];
    currentLoad: number;
    maxLoad: number;
    trustScore: number;
    specializations: string[];
}
export interface AgentCapability {
    name: string;
    proficiency: number;
    experience: number;
    lastUsed: number;
    successRate: number;
    averageTime: number;
}
export interface DelegationRight {
    canDelegateTo: string[];
    maxAuthorityLevel: number;
    allowedTaskTypes: string[];
    requiresApproval: boolean;
}
export interface TaskDelegation {
    delegationId: string;
    fromAgent: string;
    toAgent: string;
    task: DelegatedTask;
    delegationType: 'hierarchical' | 'peer' | 'emergency' | 'load_balance';
    authority: DelegationAuthority;
    status: 'pending' | 'accepted' | 'rejected' | 'in_progress' | 'completed' | 'failed' | 'rolled_back';
    createdAt: number;
    acceptedAt?: number;
    completedAt?: number;
    result?: DelegationResult;
    rollbackPlan?: RollbackPlan;
}
export interface DelegatedTask {
    taskId: string;
    type: string;
    description: string;
    requirements: TaskRequirement[];
    constraints: TaskConstraint[];
    priority: 'low' | 'medium' | 'high' | 'critical';
    deadline?: number;
    context: any;
    dependencies: string[];
    expectedOutput: any;
}
export interface TaskRequirement {
    capability: string;
    minimumProficiency: number;
    required: boolean;
    weight: number;
}
export interface TaskConstraint {
    type: 'time' | 'resource' | 'quality' | 'security';
    value: any;
    strict: boolean;
}
export interface DelegationAuthority {
    level: number;
    scope: string[];
    limitations: string[];
    canSubDelegate: boolean;
    reportingRequired: boolean;
}
export interface DelegationResult {
    success: boolean;
    output: any;
    metrics: DelegationMetrics;
    feedback: string;
    issues: DelegationIssue[];
    recommendations: string[];
}
export interface DelegationMetrics {
    executionTime: number;
    qualityScore: number;
    resourceUsage: any;
    errorCount: number;
    userSatisfaction: number;
}
export interface DelegationIssue {
    type: 'capability_mismatch' | 'resource_constraint' | 'time_overrun' | 'quality_issue';
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    impact: string;
    resolution?: string;
}
export interface RollbackPlan {
    triggers: RollbackTrigger[];
    steps: RollbackStep[];
    fallbackAgent?: string;
    dataRecovery: boolean;
    notificationRequired: boolean;
}
export interface RollbackTrigger {
    condition: string;
    threshold: any;
    timeWindow: number;
}
export interface RollbackStep {
    order: number;
    action: string;
    parameters: any;
    validation: string;
}
export interface DelegationStrategy {
    name: string;
    type: 'capability_based' | 'load_based' | 'authority_based' | 'hybrid';
    selectionCriteria: SelectionCriteria;
    fallbackStrategy?: string;
    optimizationGoals: string[];
}
export interface SelectionCriteria {
    capabilityWeight: number;
    loadWeight: number;
    trustWeight: number;
    experienceWeight: number;
    availabilityWeight: number;
    costWeight: number;
}
export interface DelegationAnalytics {
    totalDelegations: number;
    successRate: number;
    averageExecutionTime: number;
    topPerformers: AgentPerformance[];
    bottlenecks: DelegationBottleneck[];
    recommendations: OptimizationRecommendation[];
}
export interface AgentPerformance {
    agentId: string;
    delegationsReceived: number;
    successRate: number;
    averageTime: number;
    qualityScore: number;
    trustScore: number;
}
export interface DelegationBottleneck {
    type: 'capability_gap' | 'overload' | 'authority_conflict' | 'communication_delay';
    description: string;
    impact: number;
    suggestedSolution: string;
}
export interface OptimizationRecommendation {
    type: 'training' | 'load_balancing' | 'authority_adjustment' | 'capability_development';
    description: string;
    expectedBenefit: string;
    implementationEffort: 'low' | 'medium' | 'high';
}
/**
 * Advanced Task Delegation System
 */
export declare class TaskDelegationSystem extends EventEmitter {
    private config;
    private agentAuthorities;
    private activeDelegations;
    private delegationHistory;
    private delegationStrategies;
    constructor(config?: Partial<DelegationConfig>);
    /**
     * Register agent with authority and capabilities
     */
    registerAgent(agentId: string, authority: Partial<AgentAuthority>): void;
    /**
     * Delegate task to another agent
     */
    delegateTask(fromAgent: string, task: DelegatedTask, delegationType?: TaskDelegation['delegationType'], targetAgent?: string): Promise<TaskDelegation>;
    /**
     * Accept or reject delegation
     */
    respondToDelegation(delegationId: string, response: 'accept' | 'reject', reason?: string): Promise<void>;
    /**
     * Complete delegation with results
     */
    completeDelegation(delegationId: string, result: DelegationResult): Promise<void>;
    /**
     * Rollback delegation
     */
    rollbackDelegation(delegationId: string, reason: string): Promise<void>;
    /**
     * Get delegation analytics
     */
    getDelegationAnalytics(): DelegationAnalytics;
    /**
     * Private helper methods
     */
    private selectOptimalAgent;
    private calculateAgentScore;
    private calculateCapabilityScore;
    private calculateExperienceScore;
    private calculateDelegationAuthority;
    private createRollbackPlan;
    private requestDelegationConfirmation;
    private updateAgentLoad;
    private updateAgentCapabilities;
    private updateTrustScore;
    private executeRollbackStep;
    private calculateAverageExecutionTime;
    private calculateTopPerformers;
    private identifyBottlenecks;
    private generateOptimizationRecommendations;
    private initializeDefaultStrategies;
}
export default TaskDelegationSystem;
//# sourceMappingURL=task-delegation-system.d.ts.map