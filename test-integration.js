#!/usr/bin/env node

/**
 * Integration Test Script for Dyad Backend
 * Tests all major API endpoints to ensure they're working correctly
 */

const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3002';

async function testEndpoint(name, url, options = {}) {
  try {
    console.log(`Testing ${name}...`);
    const response = await fetch(`${BASE_URL}${url}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    console.log(`✅ ${name} - OK`);
    return data;
  } catch (error) {
    console.log(`❌ ${name} - FAILED: ${error.message}`);
    return null;
  }
}

async function runTests() {
  console.log('🚀 Starting Dyad Backend Integration Tests\n');
  
  // Test health check
  await testEndpoint('Health Check', '/health');
  
  // Test language models
  await testEndpoint('Language Model Providers', '/api/language-models/providers');
  await testEndpoint('All Language Models', '/api/language-models/all');
  await testEndpoint('OpenRouter Models', '/api/language-models/provider/openrouter');
  
  // Test apps
  const apps = await testEndpoint('List Apps', '/api/apps');
  
  if (apps && apps.length > 0) {
    const firstApp = apps[0];
    console.log(`\nTesting with app: ${firstApp.name} (ID: ${firstApp.id})`);
    
    // Test app status
    await testEndpoint(`App ${firstApp.id} Status`, `/api/apps/${firstApp.id}/status`);
    
    // Test file operations
    await testEndpoint(`App ${firstApp.id} Files`, `/api/apps/${firstApp.id}/files`);
    
    // Test chats
    const chats = await testEndpoint(`App ${firstApp.id} Chats`, `/api/chats?appId=${firstApp.id}`);
    
    if (chats && chats.length > 0) {
      const firstChat = chats[0];
      await testEndpoint(`Chat ${firstChat.id} Details`, `/api/chats/${firstChat.id}`);
    }
  } else {
    console.log('\n⚠️  No apps found. Create an app through the UI to test app-specific endpoints.');
  }
  
  // Test settings
  await testEndpoint('User Settings', '/api/settings');
  
  // Test Node.js status
  await testEndpoint('Node.js Status', '/api/node/status');
  
  console.log('\n🎉 Integration tests completed!');
  console.log('\nNext steps:');
  console.log('1. Start the web server: npm run web-server');
  console.log('2. Start the frontend: cd ai-dev-ecosystem && npm run dev');
  console.log('3. Open http://localhost:3000 to test the full integration');
}

runTests().catch(console.error);
