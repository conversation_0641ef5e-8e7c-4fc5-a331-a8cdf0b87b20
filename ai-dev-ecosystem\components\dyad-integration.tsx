"use client"

import React, { useState } from 'react';
import { useDyad } from '@/hooks/use-dyad';
import DyadStatus from './dyad-status';
import DyadAppManager from './dyad-app-manager';
import DyadChat from './dyad-chat';
import DyadTest from './dyad-test';
import IntegrationSummary from './integration-summary';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Folder, 
  MessageSquare, 
  Settings, 
  Code,
  Zap,
  Bot
} from 'lucide-react';
import { App } from '@/lib/dyad-client';

interface DyadIntegrationProps {
  className?: string;
}

export default function DyadIntegration({ className }: DyadIntegrationProps) {
  const { isConnected } = useDyad();
  const [selectedApp, setSelectedApp] = useState<App | null>(null);
  const [activeTab, setActiveTab] = useState('test');

  // Auto-switch to chat when app is selected
  const handleSelectApp = (app: App) => {
    setSelectedApp(app);
    setActiveTab('chat');
  };

  if (!isConnected) {
    return (
      <div className={`space-y-4 ${className}`}>
        <Card className="bg-[#0a0a0a] border-[#1a1a1a]">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Zap className="w-5 h-5 text-blue-500" />
              Dyad Integration
            </CardTitle>
            <CardDescription className="text-[#666]">
              Connect to Dyad backend for AI-powered development
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DyadStatus />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header with Status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <Zap className="w-5 h-5 text-blue-500" />
            <h2 className="text-lg font-semibold text-white">Dyad Integration</h2>
          </div>
          {selectedApp && (
            <Badge variant="secondary" className="bg-blue-900/20 text-blue-400 border-blue-800">
              {selectedApp.name}
            </Badge>
          )}
        </div>
        <DyadStatus />
      </div>

      {/* Main Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4 bg-[#1a1a1a] border-[#333]">
          <TabsTrigger
            value="test"
            className="data-[state=active]:bg-[#2a2a2a] data-[state=active]:text-white text-[#888]"
          >
            <Settings className="w-4 h-4 mr-2" />
            Test
          </TabsTrigger>
          <TabsTrigger
            value="apps"
            className="data-[state=active]:bg-[#2a2a2a] data-[state=active]:text-white text-[#888]"
          >
            <Folder className="w-4 h-4 mr-2" />
            Apps
          </TabsTrigger>
          <TabsTrigger
            value="chat"
            className="data-[state=active]:bg-[#2a2a2a] data-[state=active]:text-white text-[#888]"
            disabled={!selectedApp}
          >
            <MessageSquare className="w-4 h-4 mr-2" />
            Chat
          </TabsTrigger>
          <TabsTrigger
            value="code"
            className="data-[state=active]:bg-[#2a2a2a] data-[state=active]:text-white text-[#888]"
            disabled={!selectedApp}
          >
            <Code className="w-4 h-4 mr-2" />
            Code
          </TabsTrigger>
        </TabsList>

        <TabsContent value="test" className="space-y-4">
          <IntegrationSummary />
          <DyadTest />
        </TabsContent>

        <TabsContent value="apps" className="space-y-4">
          <Card className="bg-[#0a0a0a] border-[#1a1a1a]">
            <CardContent className="p-6">
              <DyadAppManager 
                onSelectApp={handleSelectApp}
                selectedAppId={selectedApp?.id}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="chat" className="space-y-4">
          <Card className="bg-[#0a0a0a] border-[#1a1a1a] h-[600px]">
            <CardContent className="p-0 h-full">
              <DyadChat selectedApp={selectedApp || undefined} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="code" className="space-y-4">
          <Card className="bg-[#0a0a0a] border-[#1a1a1a]">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Code className="w-5 h-5" />
                Code Editor
              </CardTitle>
              <CardDescription className="text-[#666]">
                {selectedApp ? `Edit files in ${selectedApp.name}` : 'Select an app to edit code'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {selectedApp ? (
                <div className="h-[400px] bg-[#111111] rounded-lg border border-[#1a1a1a] flex items-center justify-center">
                  <div className="text-center text-[#666]">
                    <Code className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p className="text-sm">Code editor integration coming soon</p>
                    <p className="text-xs mt-2 opacity-70">
                      File browser and editor for {selectedApp.name}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="h-[400px] bg-[#111111] rounded-lg border border-[#1a1a1a] flex items-center justify-center">
                  <div className="text-center text-[#666]">
                    <Folder className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p className="text-sm">Select an app to start coding</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      {selectedApp && (
        <Card className="bg-[#0a0a0a] border-[#1a1a1a]">
          <CardHeader>
            <CardTitle className="text-sm text-white">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex gap-2 text-xs">
              <Badge variant="outline" className="bg-[#1a1a1a] border-[#333] text-[#888]">
                App: {selectedApp.name}
              </Badge>
              <Badge variant="outline" className="bg-[#1a1a1a] border-[#333] text-[#888]">
                ID: {selectedApp.id}
              </Badge>
              <Badge variant="outline" className="bg-[#1a1a1a] border-[#333] text-[#888]">
                Created: {new Date(selectedApp.createdAt).toLocaleDateString()}
              </Badge>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
