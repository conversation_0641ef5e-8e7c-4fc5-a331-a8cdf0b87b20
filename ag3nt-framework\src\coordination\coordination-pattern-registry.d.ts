/**
 * AG3NT Framework - Coordination Pattern Registry
 *
 * Registry for managing different coordination patterns and strategies
 * for sophisticated multi-agent collaboration.
 */
import { EventEmitter } from "events";
export interface CoordinationPattern {
    patternId: string;
    name: string;
    description: string;
    type: 'delegation' | 'consensus' | 'handoff' | 'hybrid';
    category: 'hierarchical' | 'peer_to_peer' | 'democratic' | 'market_based' | 'emergent';
    applicability: PatternApplicability;
    configuration: PatternConfiguration;
    metrics: PatternMetrics;
    implementation: PatternImplementation;
}
export interface PatternApplicability {
    taskTypes: string[];
    agentTypes: string[];
    teamSizes: {
        min: number;
        max: number;
    };
    complexity: 'low' | 'medium' | 'high';
    timeConstraints: 'tight' | 'moderate' | 'flexible';
    qualityRequirements: 'basic' | 'standard' | 'high' | 'critical';
    riskTolerance: 'low' | 'medium' | 'high';
}
export interface PatternConfiguration {
    parameters: PatternParameter[];
    constraints: PatternConstraint[];
    prerequisites: string[];
    dependencies: string[];
    fallbackPatterns: string[];
}
export interface PatternParameter {
    name: string;
    type: 'number' | 'boolean' | 'string' | 'enum' | 'object';
    defaultValue: any;
    range?: {
        min?: number;
        max?: number;
    };
    options?: string[];
    required: boolean;
    description: string;
}
export interface PatternConstraint {
    type: 'resource' | 'time' | 'quality' | 'security' | 'compliance';
    condition: string;
    enforcement: 'strict' | 'flexible' | 'advisory';
    penalty: number;
}
export interface PatternMetrics {
    successRate: number;
    averageExecutionTime: number;
    resourceEfficiency: number;
    qualityScore: number;
    scalability: number;
    adaptability: number;
    usageCount: number;
    lastUsed: number;
}
export interface PatternImplementation {
    initializePattern: (config: any) => Promise<any>;
    executePattern: (context: any) => Promise<any>;
    monitorPattern: (execution: any) => Promise<any>;
    adaptPattern: (feedback: any) => Promise<any>;
    cleanupPattern: (execution: any) => Promise<void>;
}
export interface CoordinationStrategy {
    strategyId: string;
    name: string;
    description: string;
    patterns: string[];
    selectionCriteria: StrategySelectionCriteria;
    adaptationRules: AdaptationRule[];
    performance: StrategyPerformance;
}
export interface StrategySelectionCriteria {
    contextFactors: ContextFactor[];
    weightingScheme: WeightingScheme;
    decisionThreshold: number;
    fallbackStrategy?: string;
}
export interface ContextFactor {
    name: string;
    type: 'categorical' | 'numerical' | 'boolean';
    weight: number;
    values: any[];
    currentValue?: any;
}
export interface WeightingScheme {
    taskComplexity: number;
    teamSize: number;
    timeConstraints: number;
    qualityRequirements: number;
    riskLevel: number;
    resourceAvailability: number;
}
export interface AdaptationRule {
    ruleId: string;
    condition: string;
    action: 'switch_pattern' | 'adjust_parameters' | 'escalate' | 'fallback';
    parameters: any;
    priority: number;
}
export interface StrategyPerformance {
    overallScore: number;
    patternEffectiveness: Map<string, number>;
    adaptationSuccess: number;
    contextAccuracy: number;
    learningRate: number;
}
export interface PatternRecommendation {
    patternId: string;
    confidence: number;
    reasoning: string[];
    expectedBenefits: string[];
    potentialRisks: string[];
    alternativePatterns: string[];
}
/**
 * Coordination Pattern Registry
 */
export declare class CoordinationPatternRegistry extends EventEmitter {
    private patterns;
    private strategies;
    private activeExecutions;
    private patternHistory;
    constructor();
    /**
     * Register coordination pattern
     */
    registerPattern(pattern: CoordinationPattern): void;
    /**
     * Register coordination strategy
     */
    registerStrategy(strategy: CoordinationStrategy): void;
    /**
     * Recommend pattern for context
     */
    recommendPattern(context: CoordinationContext): PatternRecommendation[];
    /**
     * Execute coordination pattern
     */
    executePattern(patternId: string, context: CoordinationContext, config?: any): Promise<PatternExecution>;
    /**
     * Adapt pattern based on feedback
     */
    adaptPattern(executionId: string, feedback: PatternFeedback): Promise<void>;
    /**
     * Get pattern analytics
     */
    getPatternAnalytics(): PatternAnalytics;
    /**
     * Private helper methods
     */
    private calculatePatternScore;
    private getComplexityScore;
    private getTimeConstraintScore;
    private getQualityScore;
    private generateRecommendationReasoning;
    private identifyExpectedBenefits;
    private identifyPotentialRisks;
    private findAlternativePatterns;
    private updatePatternMetrics;
    private calculateAverageExecutionTime;
    private getMostUsedPatterns;
    private calculatePatternEffectiveness;
    private calculateAdaptationSuccess;
    private initializeBuiltInPatterns;
    private initializeBuiltInStrategies;
}
interface CoordinationContext {
    taskType: string;
    participants: string[];
    complexity: 'low' | 'medium' | 'high';
    timeConstraints: 'tight' | 'moderate' | 'flexible';
    qualityRequirements: 'basic' | 'standard' | 'high' | 'critical';
    riskTolerance: 'low' | 'medium' | 'high';
    resourceConstraints: any;
    dependencies: string[];
}
interface PatternExecution {
    executionId: string;
    patternId: string;
    context: CoordinationContext;
    config: any;
    status: 'initializing' | 'running' | 'completed' | 'failed';
    startTime: number;
    endTime?: number;
    patternInstance?: any;
    result?: any;
    error?: string;
    metrics: {
        executionTime: number;
        resourceUsage: number;
        qualityScore: number;
        successRate: number;
        adaptationCount: number;
    };
    events: any[];
}
interface PatternFeedback {
    executionId: string;
    quality: number;
    efficiency: number;
    satisfaction: number;
    issues: string[];
    suggestions: string[];
}
interface PatternAnalytics {
    totalPatterns: number;
    totalStrategies: number;
    totalExecutions: number;
    successRate: number;
    averageExecutionTime: number;
    mostUsedPatterns: Array<{
        patternId: string;
        usageCount: number;
    }>;
    patternEffectiveness: Map<string, number>;
    adaptationSuccess: number;
}
export default CoordinationPatternRegistry;
//# sourceMappingURL=coordination-pattern-registry.d.ts.map