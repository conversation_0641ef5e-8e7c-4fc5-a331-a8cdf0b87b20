// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`problem_prompt > createConciseProblemFixPrompt > should format a concise prompt for multiple errors 1`] = `
"Fix these 2 TypeScript compile-time errors:

1. src/main.ts:5:12 - Cannot find module 'react-dom/client' or its corresponding type declarations. (TS2307)
2. src/components/Modal.tsx:35:20 - Property 'isOpen' does not exist on type 'IntrinsicAttributes & ModalProps'. (TS2339)

Please fix all errors in a concise way."
`;

exports[`problem_prompt > createConciseProblemFixPrompt > should format a concise prompt for single error 1`] = `
"Fix these 1 TypeScript compile-time error:

1. src/App.tsx:10:5 - Cannot find name 'consol'. Did you mean 'console'? (TS2552)

Please fix all errors in a concise way."
`;

exports[`problem_prompt > createConciseProblemFixPrompt > should return a short message when no problems exist 1`] = `"No TypeScript problems detected."`;

exports[`problem_prompt > createProblemFixPrompt > should format a single error correctly 1`] = `
"Fix these 1 TypeScript compile-time error:

1. src/components/Button.tsx:15:23 - Property 'onClick' does not exist on type 'ButtonProps'. (TS2339)

Please fix all errors in a concise way."
`;

exports[`problem_prompt > createProblemFixPrompt > should format multiple errors across multiple files 1`] = `
"Fix these 4 TypeScript compile-time errors:

1. src/components/Button.tsx:15:23 - Property 'onClick' does not exist on type 'ButtonProps'. (TS2339)
2. src/components/Button.tsx:8:12 - Type 'string | undefined' is not assignable to type 'string'. (TS2322)
3. src/hooks/useApi.ts:42:5 - Argument of type 'unknown' is not assignable to parameter of type 'string'. (TS2345)
4. src/utils/helpers.ts:45:8 - Function lacks ending return statement and return type does not include 'undefined'. (TS2366)

Please fix all errors in a concise way."
`;

exports[`problem_prompt > createProblemFixPrompt > should handle realistic React TypeScript errors 1`] = `
"Fix these 4 TypeScript compile-time errors:

1. src/components/UserProfile.tsx:12:35 - Type '{ children: string; }' is missing the following properties from type 'UserProfileProps': user, onEdit (TS2739)
2. src/components/UserProfile.tsx:25:15 - Object is possibly 'null'. (TS2531)
3. src/hooks/useLocalStorage.ts:18:12 - Type 'string | null' is not assignable to type 'T'. (TS2322)
4. src/types/api.ts:45:3 - Duplicate identifier 'UserRole'. (TS2300)

Please fix all errors in a concise way."
`;

exports[`problem_prompt > createProblemFixPrompt > should return a message when no problems exist 1`] = `"No TypeScript problems detected."`;

exports[`problem_prompt > realistic TypeScript error scenarios > should handle common React + TypeScript errors 1`] = `
"Fix these 4 TypeScript compile-time errors:

1. src/components/ProductCard.tsx:22:18 - Property 'price' is missing in type '{ name: string; description: string; }' but required in type 'Product'. (TS2741)
2. src/components/SearchInput.tsx:15:45 - Type '(value: string) => void' is not assignable to type 'ChangeEventHandler<HTMLInputElement>'. (TS2322)
3. src/api/userService.ts:8:1 - Function lacks ending return statement and return type does not include 'undefined'. (TS2366)
4. src/utils/dataProcessor.ts:34:25 - Object is possibly 'undefined'. (TS2532)

Please fix all errors in a concise way."
`;
