/**
 * AG3NT Framework - Tester Agent
 *
 * Specialized agent for comprehensive testing and quality assurance.
 * Handles unit testing, integration testing, e2e testing, and quality metrics.
 *
 * Features:
 * - Automated test generation and execution
 * - Test strategy planning and optimization
 * - Quality metrics and coverage analysis
 * - Performance and security testing
 * - Test maintenance and refactoring
 * - CI/CD integration
 */
import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent";
export interface TesterInput {
    task: TestingTask;
    codebase: TestableCodebase;
    requirements: TestingRequirements;
    strategy: TestingStrategy;
}
export interface TestingTask {
    taskId: string;
    type: 'unit' | 'integration' | 'e2e' | 'performance' | 'security' | 'accessibility' | 'regression';
    title: string;
    description: string;
    priority: 'critical' | 'high' | 'medium' | 'low';
    scope: TestingScope;
    acceptanceCriteria: string[];
    deadline?: string;
}
export interface TestingScope {
    components: string[];
    features: string[];
    apis: string[];
    workflows: string[];
    browsers?: string[];
    devices?: string[];
}
export interface TestableCodebase {
    structure: CodebaseStructure;
    components: TestableComponent[];
    apis: TestableAPI[];
    dependencies: TestDependency[];
    existingTests: ExistingTest[];
}
export interface CodebaseStructure {
    language: string;
    framework: string;
    testFramework: string;
    testDirectory: string;
    sourceDirectory: string;
    buildDirectory: string;
}
export interface TestableComponent {
    name: string;
    path: string;
    type: 'component' | 'service' | 'utility' | 'model';
    complexity: 'simple' | 'medium' | 'complex';
    dependencies: string[];
    publicMethods: ComponentMethod[];
    testCoverage: number;
}
export interface ComponentMethod {
    name: string;
    parameters: MethodParameter[];
    returnType: string;
    complexity: number;
    sideEffects: boolean;
}
export interface MethodParameter {
    name: string;
    type: string;
    optional: boolean;
    validation: string[];
}
export interface TestableAPI {
    endpoint: string;
    method: string;
    parameters: APITestParameter[];
    responses: APITestResponse[];
    authentication: boolean;
    rateLimit: boolean;
}
export interface APITestParameter {
    name: string;
    type: string;
    location: 'path' | 'query' | 'body' | 'header';
    required: boolean;
    validation: ValidationRule[];
}
export interface ValidationRule {
    type: string;
    value: any;
    message: string;
}
export interface APITestResponse {
    status: number;
    schema: any;
    examples: any[];
    headers: Record<string, string>;
}
export interface TestDependency {
    name: string;
    version: string;
    type: 'testing' | 'mocking' | 'assertion' | 'runner';
    purpose: string;
}
export interface ExistingTest {
    file: string;
    type: 'unit' | 'integration' | 'e2e';
    coverage: number;
    lastRun: string;
    status: 'passing' | 'failing' | 'skipped';
    duration: number;
}
export interface TestingRequirements {
    coverage: CoverageRequirements;
    performance: PerformanceTestRequirements;
    security: SecurityTestRequirements;
    accessibility: AccessibilityTestRequirements;
    browsers: BrowserTestRequirements;
    devices: DeviceTestRequirements;
    compliance: ComplianceTestRequirements;
}
export interface CoverageRequirements {
    minimum: number;
    target: number;
    statements: number;
    branches: number;
    functions: number;
    lines: number;
}
export interface PerformanceTestRequirements {
    loadTesting: boolean;
    stressTesting: boolean;
    enduranceTesting: boolean;
    spikeTesting: boolean;
    volumeTesting: boolean;
    thresholds: PerformanceThresholds;
}
export interface PerformanceThresholds {
    responseTime: number;
    throughput: number;
    errorRate: number;
    resourceUsage: ResourceThresholds;
}
export interface ResourceThresholds {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
}
export interface SecurityTestRequirements {
    vulnerabilityScanning: boolean;
    penetrationTesting: boolean;
    authenticationTesting: boolean;
    authorizationTesting: boolean;
    dataValidationTesting: boolean;
    encryptionTesting: boolean;
}
export interface AccessibilityTestRequirements {
    wcagLevel: 'A' | 'AA' | 'AAA';
    screenReaderTesting: boolean;
    keyboardNavigationTesting: boolean;
    colorContrastTesting: boolean;
    focusManagementTesting: boolean;
}
export interface BrowserTestRequirements {
    browsers: BrowserConfig[];
    crossBrowserTesting: boolean;
    responsiveTesting: boolean;
}
export interface BrowserConfig {
    name: string;
    versions: string[];
    platforms: string[];
}
export interface DeviceTestRequirements {
    mobile: boolean;
    tablet: boolean;
    desktop: boolean;
    devices: DeviceConfig[];
}
export interface DeviceConfig {
    name: string;
    type: 'mobile' | 'tablet' | 'desktop';
    resolution: string;
    userAgent: string;
}
export interface ComplianceTestRequirements {
    standards: string[];
    regulations: string[];
    certifications: string[];
    auditing: boolean;
}
export interface TestingStrategy {
    approach: 'tdd' | 'bdd' | 'atdd' | 'hybrid';
    pyramid: TestPyramid;
    automation: AutomationStrategy;
    reporting: ReportingStrategy;
    maintenance: MaintenanceStrategy;
}
export interface TestPyramid {
    unit: number;
    integration: number;
    e2e: number;
    manual: number;
}
export interface AutomationStrategy {
    level: 'none' | 'partial' | 'full';
    triggers: AutomationTrigger[];
    environments: string[];
    parallelization: boolean;
}
export interface AutomationTrigger {
    event: 'commit' | 'pull_request' | 'deploy' | 'schedule';
    conditions: string[];
    actions: string[];
}
export interface ReportingStrategy {
    formats: string[];
    destinations: string[];
    frequency: string;
    stakeholders: string[];
}
export interface MaintenanceStrategy {
    frequency: string;
    refactoring: boolean;
    cleanup: boolean;
    optimization: boolean;
}
export interface TesterResult {
    taskId: string;
    status: 'completed' | 'failed' | 'partial';
    testSuites: TestSuite[];
    coverage: CoverageReport;
    performance: PerformanceReport;
    security: SecurityTestReport;
    accessibility: AccessibilityReport;
    quality: QualityReport;
    recommendations: TestingRecommendation[];
}
export interface TestSuite {
    name: string;
    type: 'unit' | 'integration' | 'e2e' | 'performance' | 'security';
    tests: TestCase[];
    coverage: number;
    duration: number;
    status: 'passed' | 'failed' | 'skipped';
}
export interface TestCase {
    name: string;
    description: string;
    status: 'passed' | 'failed' | 'skipped';
    duration: number;
    assertions: number;
    error?: TestError;
}
export interface TestError {
    message: string;
    stack: string;
    type: string;
    expected?: any;
    actual?: any;
}
export interface CoverageReport {
    overall: number;
    statements: number;
    branches: number;
    functions: number;
    lines: number;
    files: FileCoverage[];
}
export interface FileCoverage {
    file: string;
    coverage: number;
    statements: CoverageMetric;
    branches: CoverageMetric;
    functions: CoverageMetric;
    lines: CoverageMetric;
}
export interface CoverageMetric {
    covered: number;
    total: number;
    percentage: number;
}
export interface PerformanceReport {
    loadTest: LoadTestResult;
    stressTest: StressTestResult;
    enduranceTest: EnduranceTestResult;
    metrics: PerformanceMetrics;
}
export interface LoadTestResult {
    users: number;
    duration: number;
    responseTime: ResponseTimeMetrics;
    throughput: number;
    errorRate: number;
}
export interface ResponseTimeMetrics {
    min: number;
    max: number;
    avg: number;
    p50: number;
    p95: number;
    p99: number;
}
export interface StressTestResult {
    breakingPoint: number;
    recoveryTime: number;
    degradation: DegradationMetrics;
}
export interface DegradationMetrics {
    responseTime: number;
    throughput: number;
    errorRate: number;
}
export interface EnduranceTestResult {
    duration: number;
    memoryLeaks: boolean;
    performanceDrift: number;
    stability: number;
}
export interface PerformanceMetrics {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
    database: DatabaseMetrics;
}
export interface DatabaseMetrics {
    connections: number;
    queries: number;
    slowQueries: number;
    deadlocks: number;
}
export interface SecurityTestReport {
    vulnerabilities: SecurityVulnerability[];
    compliance: SecurityCompliance[];
    score: number;
    recommendations: string[];
}
export interface SecurityVulnerability {
    type: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    location: string;
    cve?: string;
    fix: string;
}
export interface SecurityCompliance {
    standard: string;
    requirement: string;
    status: 'compliant' | 'non-compliant' | 'partial';
    evidence: string;
}
export interface AccessibilityReport {
    score: number;
    level: 'A' | 'AA' | 'AAA';
    violations: AccessibilityViolation[];
    recommendations: string[];
}
export interface AccessibilityViolation {
    rule: string;
    severity: 'error' | 'warning' | 'info';
    element: string;
    description: string;
    fix: string;
}
export interface QualityReport {
    score: number;
    maintainability: number;
    reliability: number;
    security: number;
    performance: number;
    coverage: number;
    debt: TechnicalDebt;
}
export interface TechnicalDebt {
    total: number;
    critical: number;
    major: number;
    minor: number;
    effort: string;
}
export interface TestingRecommendation {
    type: 'coverage' | 'performance' | 'security' | 'maintenance' | 'strategy';
    priority: 'high' | 'medium' | 'low';
    description: string;
    action: string;
    effort: string;
    impact: string;
}
/**
 * Tester Agent - Comprehensive testing and quality assurance
 */
export declare class TesterAgent extends BaseAgent {
    private readonly testingSteps;
    constructor(config?: Partial<AgentConfig>);
    /**
     * Execute testing workflow
     */
    protected executeWorkflow(state: AgentState): Promise<AgentState>;
    /**
     * Execute individual testing step with context enhancement
     */
    private executeStepWithContext;
    /**
     * Get total steps for progress tracking
     */
    protected getTotalSteps(): number;
    /**
     * Get relevant documentation for testing
     */
    protected getRelevantDocumentation(): Promise<Record<string, any>>;
    private analyzeTestabilityWithMCP;
    private planTestingStrategyWithMCP;
    private generateTestsWithMCP;
    private executeUnitTestsWithMCP;
    private executeIntegrationTestsWithMCP;
    private executeE2ETestsWithMCP;
    private performanceTestingWithMCP;
    private securityTestingWithMCP;
    private accessibilityTestingWithMCP;
    private generateReportsWithMCP;
}
export { TesterAgent as default };
//# sourceMappingURL=tester-agent.d.ts.map