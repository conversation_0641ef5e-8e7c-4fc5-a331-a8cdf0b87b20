import { Router } from 'express';
import { Server } from 'socket.io';
import log from 'electron-log';
import { AG3NTFramework } from '../../ag3nt-framework/src/ag3nt-framework.js';
import { ContextEngine } from '../../Context-Engine/src/core/engine/ContextEngine.js';
import { getDyadAppPath } from '../../paths/paths.js';
import { db } from '../../db/index.js';
import { apps } from '../../db/schema.js';
import { eq } from 'drizzle-orm';

const logger = log.scope('web-server:agents');

// Initialize AG3NT Framework and Context Engine
let ag3ntFramework = null;
let contextEngine = null;

// Initialize the autonomous development ecosystem
async function initializeAutonomousEcosystem() {
  if (ag3ntFramework && contextEngine) return;

  try {
    logger.info('Initializing AG3NT Framework...');
    
    // Initialize AG3NT Framework with full capabilities
    ag3ntFramework = new AG3NTFramework({
      contextEngine: {
        enableMCP: true,
        enableSequentialThinking: true,
        enableRAG: true,
        enableContextEnrichment: true
      },
      agents: {
        maxConcurrentSessions: 20,
        defaultTimeout: 600000, // 10 minutes
        defaultRetries: 3
      },
      monitoring: {
        enableHealthChecks: true,
        healthCheckInterval: 30000,
        enableMetrics: true
      },
      coordination: {
        enableTaskDelegation: true,
        enableConsensus: true,
        enableWorkflowHandoffs: true,
        enablePatternRegistry: true,
        delegationTimeout: 600000,
        consensusTimeout: 600000,
        handoffTimeout: 600000
      },
      discovery: {
        enableAgentDiscovery: true,
        enableLoadBalancing: true,
        enableFailover: true,
        discoveryInterval: 30000,
        healthCheckInterval: 10000,
        loadBalancingAlgorithm: 'adaptive'
      },
      advancedFeatures: {
        adaptiveLearning: { enabled: true },
        temporalDatabase: { enabled: true },
        collaboration: { enabled: true },
        optimization: { enabled: true },
        marketplace: { enabled: true },
        monitoring: { enabled: true }
      }
    });

    await ag3ntFramework.initialize();
    logger.info('✅ AG3NT Framework initialized successfully');

    // Initialize Context Engine
    logger.info('Initializing Context Engine...');
    
    contextEngine = new ContextEngine({
      neo4j: {
        uri: process.env.NEO4J_URI || 'bolt://localhost:7687',
        user: process.env.NEO4J_USER || 'neo4j',
        password: process.env.NEO4J_PASSWORD || 'password'
      },
      processing: {
        batchSize: 100,
        maxConcurrency: 5,
        enableASTAnalysis: true,
        enableSymbolResolution: true,
        enableDependencyTracking: true,
        enableSemanticAnalysis: true
      },
      retrieval: {
        enableHybridSearch: true,
        enableSemanticSearch: true,
        enableGraphTraversal: true,
        maxResults: 50,
        similarityThreshold: 0.7
      }
    });

    await contextEngine.initialize();
    logger.info('✅ Context Engine initialized successfully');

  } catch (error) {
    logger.error('Failed to initialize autonomous ecosystem:', error);
    throw error;
  }
}

export function setupAgentRoutes(io) {
  const router = Router();

  // Initialize on first request
  router.use(async (req, res, next) => {
    try {
      await initializeAutonomousEcosystem();
      next();
    } catch (error) {
      logger.error('Failed to initialize autonomous ecosystem:', error);
      res.status(500).json({ error: 'Failed to initialize autonomous development system' });
    }
  });

  // GET /api/agents/health - Health check for autonomous ecosystem
  router.get('/health', async (req, res) => {
    try {
      const health = {
        timestamp: new Date().toISOString(),
        ag3ntFramework: ag3ntFramework ? ag3ntFramework.getStats() : null,
        contextEngine: contextEngine ? await contextEngine.getHealth() : null,
        status: 'healthy'
      };

      res.json(health);
    } catch (error) {
      logger.error('Health check failed:', error);
      res.status(500).json({ error: 'Health check failed' });
    }
  });

  // GET /api/agents - List all available agents
  router.get('/', async (req, res) => {
    try {
      const agents = ag3ntFramework ? ag3ntFramework.getAgents() : [];
      res.json(agents);
    } catch (error) {
      logger.error('Failed to list agents:', error);
      res.status(500).json({ error: 'Failed to list agents' });
    }
  });

  // POST /api/agents/project-plan - Create project plan using planning agent
  router.post('/project-plan', async (req, res) => {
    try {
      const { appId, requirements, techStack } = req.body;

      if (!appId || !requirements) {
        return res.status(400).json({ error: 'appId and requirements are required' });
      }

      // Get app details
      const app = await db.select().from(apps).where(eq(apps.id, appId)).limit(1);
      if (!app.length) {
        return res.status(404).json({ error: 'App not found' });
      }

      const appPath = getDyadAppPath(app[0].name);

      // Process codebase with Context Engine
      let context = null;
      try {
        context = await contextEngine.processCodebase(appPath, {
          includeDependencies: true,
          includeTests: true,
          includeDocumentation: true
        });
      } catch (error) {
        logger.warn('Failed to process codebase, continuing without context:', error);
      }

      // Create project plan using Planning Agent
      const projectPlan = await ag3ntFramework.execute('planning', {
        type: 'project-planning',
        requirements,
        techStack: techStack || 'auto-detect',
        existingContext: context,
        projectPath: appPath,
        appName: app[0].name
      });

      res.json({
        projectPlan,
        context: context ? {
          filesProcessed: context.filesProcessed,
          nodesCreated: context.nodesCreated,
          relationshipsCreated: context.relationshipsCreated
        } : null
      });

    } catch (error) {
      logger.error('Failed to create project plan:', error);
      res.status(500).json({ error: 'Failed to create project plan' });
    }
  });

  // POST /api/agents/task-plan - Create task breakdown using task planner
  router.post('/task-plan', async (req, res) => {
    try {
      const { appId, featureRequest, priority } = req.body;

      if (!appId || !featureRequest) {
        return res.status(400).json({ error: 'appId and featureRequest are required' });
      }

      // Get app details
      const app = await db.select().from(apps).where(eq(apps.id, appId)).limit(1);
      if (!app.length) {
        return res.status(404).json({ error: 'App not found' });
      }

      const appPath = getDyadAppPath(app[0].name);

      // Get current context
      const context = await contextEngine.getContext(featureRequest, {
        projectPath: appPath,
        includeDependencies: true,
        maxResults: 20
      });

      // Create task plan using Task Planner Agent
      const taskPlan = await ag3ntFramework.execute('task-planning', {
        type: 'task-breakdown',
        featureRequest,
        context: context.results,
        projectPath: appPath,
        appName: app[0].name,
        priority: priority || 'medium'
      });

      res.json({
        taskPlan,
        context: {
          relevantFiles: context.results.map(r => r.filePath),
          totalContext: context.results.length
        }
      });

    } catch (error) {
      logger.error('Failed to create task plan:', error);
      res.status(500).json({ error: 'Failed to create task plan' });
    }
  });

  // POST /api/agents/code-generation - Generate code using coding agents
  router.post('/code-generation', async (req, res) => {
    try {
      const { appId, task, agentType } = req.body;

      if (!appId || !task) {
        return res.status(400).json({ error: 'appId and task are required' });
      }

      // Get app details
      const app = await db.select().from(apps).where(eq(apps.id, appId)).limit(1);
      if (!app.length) {
        return res.status(404).json({ error: 'App not found' });
      }

      const appPath = getDyadAppPath(app[0].name);

      // Get relevant context
      const context = await contextEngine.getContext(task.description || task, {
        projectPath: appPath,
        includeDependencies: true,
        maxResults: 10
      });

      // Determine agent type
      const agentTypeToUse = agentType || 'frontend-coder';
      
      // Generate code using appropriate coding agent
      const codeResult = await ag3ntFramework.execute(agentTypeToUse, {
        type: 'code-generation',
        task,
        context: context.results,
        projectPath: appPath,
        appName: app[0].name
      });

      res.json({
        codeResult,
        context: {
          relevantFiles: context.results.map(r => r.filePath),
          totalContext: context.results.length
        }
      });

    } catch (error) {
      logger.error('Failed to generate code:', error);
      res.status(500).json({ error: 'Failed to generate code' });
    }
  });

  // POST /api/agents/context-analysis - Analyze codebase context
  router.post('/context-analysis', async (req, res) => {
    try {
      const { appId, analysisType } = req.body;

      if (!appId) {
        return res.status(400).json({ error: 'appId is required' });
      }

      // Get app details
      const app = await db.select().from(apps).where(eq(apps.id, appId)).limit(1);
      if (!app.length) {
        return res.status(404).json({ error: 'App not found' });
      }

      const appPath = getDyadAppPath(app[0].name);

      // Process codebase
      const analysis = await contextEngine.processCodebase(appPath, {
        includeDependencies: true,
        includeTests: true,
        includeDocumentation: true,
        analysisType: analysisType || 'full'
      });

      // Get statistics
      const stats = await contextEngine.getStatistics();

      res.json({
        analysis,
        statistics: stats,
        appName: app[0].name
      });

    } catch (error) {
      logger.error('Failed to analyze context:', error);
      res.status(500).json({ error: 'Failed to analyze context' });
    }
  });

  // GET /api/agents/stats - Get framework statistics
  router.get('/stats', async (req, res) => {
    try {
      const stats = {
        timestamp: new Date().toISOString(),
        ag3ntFramework: ag3ntFramework ? ag3ntFramework.getStats() : null,
        contextEngine: contextEngine ? await contextEngine.getStatistics() : null
      };

      res.json(stats);
    } catch (error) {
      logger.error('Failed to get statistics:', error);
      res.status(500).json({ error: 'Failed to get statistics' });
    }
  });

  // WebSocket integration for real-time agent updates
  io.on('connection', (socket) => {
    logger.info('Client connected to agents namespace:', socket.id);

    socket.on('agent:subscribe', (data) => {
      socket.join(`agent-updates-${data.appId || 'global'}`);
    });

    socket.on('agent:execute', async (data) => {
      try {
        const result = await ag3ntFramework.execute(data.agentType, data.input, data.config);
        socket.emit('agent:result', result);
      } catch (error) {
        socket.emit('agent:error', { error: error.message });
      }
    });

    socket.on('disconnect', () => {
      logger.info('Client disconnected from agents namespace:', socket.id);
    });
  });

  return router;
}
