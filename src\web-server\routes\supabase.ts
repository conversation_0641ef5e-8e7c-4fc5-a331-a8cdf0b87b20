import { Router } from 'express';
import log from 'electron-log';

const logger = log.scope('web-server:supabase');

export function setupSupabaseRoutes() {
  const router = Router();

  // GET /api/supabase/projects - List Supabase projects
  router.get('/projects', async (req, res) => {
    try {
      // TODO: Implement Supabase project listing
      res.json([]);
    } catch (error) {
      logger.error('Error listing Supabase projects:', error);
      res.status(500).json({ error: 'Failed to list Supabase projects' });
    }
  });

  return router;
}
