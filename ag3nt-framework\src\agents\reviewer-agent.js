"use strict";
/**
 * AG3NT Framework - Reviewer Agent
 *
 * Specialized agent for code review, quality assessment, and approval workflows.
 * Provides comprehensive code analysis, best practices validation, and improvement recommendations.
 *
 * Features:
 * - Automated code review and analysis
 * - Quality metrics and technical debt assessment
 * - Security and performance review
 * - Best practices validation
 * - Improvement recommendations
 * - Approval workflow management
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = exports.ReviewerAgent = void 0;
const base_agent_1 = require("../core/base-agent");
const ai_service_1 = require("../../ai-service");
/**
 * Reviewer Agent - Comprehensive code review and quality assessment
 */
class ReviewerAgent extends base_agent_1.BaseAgent {
    constructor(config = {}) {
        super('reviewer', {
            capabilities: {
                requiredCapabilities: [
                    'code_review',
                    'quality_assessment',
                    'security_analysis',
                    'performance_analysis',
                    'maintainability_analysis',
                    'compliance_checking',
                    'recommendation_generation'
                ],
                contextFilters: ['review', 'quality', 'security', 'performance', 'code'],
                mcpEnhanced: true,
                sequentialThinking: true,
                contextEnrichment: true,
                ragIntegration: true
            },
            ...config
        });
        this.reviewSteps = [
            'analyze_changes', 'quality_review', 'security_review', 'performance_review',
            'maintainability_review', 'compliance_review', 'generate_recommendations',
            'make_decision', 'create_report'
        ];
    }
    /**
     * Execute review workflow
     */
    async executeWorkflow(state) {
        const input = state.input;
        console.log(`🔍 Starting code review: ${input.task.title}`);
        // Execute review steps sequentially
        for (const stepId of this.reviewSteps) {
            console.log(`🔄 Executing step: ${stepId}`);
            const stepResult = await this.executeStepWithContext(stepId, input);
            if (stepResult.needsInput) {
                state.needsInput = true;
                state.question = stepResult.question;
                break;
            }
            // Update progress
            state.metadata.completedSteps++;
            state.metadata.lastUpdated = new Date().toISOString();
            this.emit('step_completed', {
                agentId: this.agentId,
                stepId,
                progress: state.metadata.completedSteps / state.metadata.totalSteps
            });
        }
        // Mark as completed if no input needed
        if (!state.needsInput) {
            state.completed = true;
            console.log(`✅ Code review completed: ${input.task.title}`);
        }
        return state;
    }
    /**
     * Execute individual review step with context enhancement
     */
    async executeStepWithContext(stepId, input) {
        // Get enhanced context from context engine
        const enhancedState = await this.getEnhancedContext(stepId);
        // Execute step with MCP enhancement
        switch (stepId) {
            case 'analyze_changes':
                return await this.analyzeChangesWithMCP(enhancedState, input);
            case 'quality_review':
                return await this.qualityReviewWithMCP(enhancedState);
            case 'security_review':
                return await this.securityReviewWithMCP(enhancedState);
            case 'performance_review':
                return await this.performanceReviewWithMCP(enhancedState);
            case 'maintainability_review':
                return await this.maintainabilityReviewWithMCP(enhancedState);
            case 'compliance_review':
                return await this.complianceReviewWithMCP(enhancedState);
            case 'generate_recommendations':
                return await this.generateRecommendationsWithMCP(enhancedState);
            case 'make_decision':
                return await this.makeDecisionWithMCP(enhancedState);
            case 'create_report':
                return await this.createReportWithMCP(enhancedState);
            default:
                throw new Error(`Unknown review step: ${stepId}`);
        }
    }
    /**
     * Get total steps for progress tracking
     */
    getTotalSteps() {
        return this.reviewSteps.length;
    }
    /**
     * Get relevant documentation for code review
     */
    async getRelevantDocumentation() {
        return {
            codeReview: 'Code review best practices and methodologies',
            qualityAssurance: 'Software quality metrics and assessment techniques',
            security: 'Security code review and vulnerability assessment',
            performance: 'Performance analysis and optimization techniques',
            maintainability: 'Code maintainability and technical debt management',
            compliance: 'Compliance standards and regulatory requirements'
        };
    }
    // ============================================================================
    // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
    // ============================================================================
    async analyzeChangesWithMCP(state, input) {
        const analysis = await ai_service_1.aiService.analyzeCodeChanges(input.codeChanges, input.context, input.criteria);
        this.state.results.changeAnalysis = analysis;
        return {
            results: analysis,
            needsInput: false,
            completed: false
        };
    }
    async qualityReviewWithMCP(state) {
        const changeAnalysis = this.state.results.changeAnalysis;
        const qualityReview = await ai_service_1.aiService.performQualityReview(changeAnalysis, this.state.input.criteria.quality);
        this.state.results.qualityReview = qualityReview;
        return {
            results: qualityReview,
            needsInput: false,
            completed: false
        };
    }
    async securityReviewWithMCP(state) {
        const changeAnalysis = this.state.results.changeAnalysis;
        const securityReview = await ai_service_1.aiService.performSecurityReview(changeAnalysis, this.state.input.criteria.security);
        this.state.results.securityReview = securityReview;
        return {
            results: securityReview,
            needsInput: false,
            completed: false
        };
    }
    async performanceReviewWithMCP(state) {
        const changeAnalysis = this.state.results.changeAnalysis;
        const performanceReview = await ai_service_1.aiService.performPerformanceReview(changeAnalysis, this.state.input.criteria.performance);
        this.state.results.performanceReview = performanceReview;
        return {
            results: performanceReview,
            needsInput: false,
            completed: false
        };
    }
    async maintainabilityReviewWithMCP(state) {
        const changeAnalysis = this.state.results.changeAnalysis;
        const maintainabilityReview = await ai_service_1.aiService.performMaintainabilityReview(changeAnalysis, this.state.input.criteria.maintainability);
        this.state.results.maintainabilityReview = maintainabilityReview;
        return {
            results: maintainabilityReview,
            needsInput: false,
            completed: false
        };
    }
    async complianceReviewWithMCP(state) {
        const changeAnalysis = this.state.results.changeAnalysis;
        const complianceReview = await ai_service_1.aiService.performComplianceReview(changeAnalysis, this.state.input.criteria.compliance);
        this.state.results.complianceReview = complianceReview;
        return {
            results: complianceReview,
            needsInput: false,
            completed: false
        };
    }
    async generateRecommendationsWithMCP(state) {
        const allReviews = {
            quality: this.state.results.qualityReview,
            security: this.state.results.securityReview,
            performance: this.state.results.performanceReview,
            maintainability: this.state.results.maintainabilityReview,
            compliance: this.state.results.complianceReview
        };
        const recommendations = await ai_service_1.aiService.generateReviewRecommendations(allReviews, this.state.input.context);
        this.state.results.recommendations = recommendations;
        return {
            results: recommendations,
            needsInput: false,
            completed: false
        };
    }
    async makeDecisionWithMCP(state) {
        const allReviews = {
            quality: this.state.results.qualityReview,
            security: this.state.results.securityReview,
            performance: this.state.results.performanceReview,
            maintainability: this.state.results.maintainabilityReview,
            compliance: this.state.results.complianceReview
        };
        const decision = await ai_service_1.aiService.makeReviewDecision(allReviews, this.state.results.recommendations, this.state.input.criteria);
        this.state.results.decision = decision;
        return {
            results: decision,
            needsInput: false,
            completed: false
        };
    }
    async createReportWithMCP(state) {
        const allResults = {
            changeAnalysis: this.state.results.changeAnalysis,
            qualityReview: this.state.results.qualityReview,
            securityReview: this.state.results.securityReview,
            performanceReview: this.state.results.performanceReview,
            maintainabilityReview: this.state.results.maintainabilityReview,
            complianceReview: this.state.results.complianceReview,
            recommendations: this.state.results.recommendations,
            decision: this.state.results.decision
        };
        const report = await ai_service_1.aiService.createReviewReport(allResults, this.state.input.task);
        this.state.results.report = report;
        return {
            results: report,
            needsInput: false,
            completed: true // Final step
        };
    }
}
exports.ReviewerAgent = ReviewerAgent;
exports.default = ReviewerAgent;
//# sourceMappingURL=reviewer-agent.js.map