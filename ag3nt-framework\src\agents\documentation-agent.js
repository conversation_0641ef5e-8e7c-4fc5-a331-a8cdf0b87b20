"use strict";
/**
 * AG3NT Framework - Documentation Agent
 *
 * Specialized agent for maintaining and upgrading technical documentation.
 * Handles changelogs, architectural docs, API documentation, and user guides.
 *
 * Features:
 * - Automated documentation generation
 * - Documentation maintenance and updates
 * - Changelog generation from code changes
 * - API documentation from code analysis
 * - Architecture documentation
 * - User guide creation and updates
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = exports.DocumentationAgent = void 0;
const base_agent_1 = require("../core/base-agent");
const ai_service_1 = require("../../ai-service");
/**
 * Documentation Agent - Technical documentation management
 */
class DocumentationAgent extends base_agent_1.BaseAgent {
    constructor(config = {}) {
        super('documentation', {
            capabilities: {
                requiredCapabilities: [
                    'documentation_generation',
                    'api_documentation',
                    'architecture_documentation',
                    'changelog_generation',
                    'content_writing',
                    'documentation_maintenance',
                    'quality_assessment'
                ],
                contextFilters: ['documentation', 'code', 'api', 'architecture', 'guides'],
                mcpEnhanced: true,
                sequentialThinking: true,
                contextEnrichment: true,
                ragIntegration: true
            },
            ...config
        });
        this.documentationSteps = [
            'analyze_codebase', 'assess_existing_docs', 'plan_documentation',
            'generate_api_docs', 'create_architecture_docs', 'write_user_guides',
            'generate_changelog', 'update_existing_docs', 'validate_quality', 'publish_docs'
        ];
    }
    /**
     * Execute documentation workflow
     */
    async executeWorkflow(state) {
        const input = state.input;
        console.log(`📚 Starting documentation workflow: ${input.task.title}`);
        // Execute documentation steps sequentially
        for (const stepId of this.documentationSteps) {
            console.log(`🔄 Executing step: ${stepId}`);
            const stepResult = await this.executeStepWithContext(stepId, input);
            if (stepResult.needsInput) {
                state.needsInput = true;
                state.question = stepResult.question;
                break;
            }
            // Update progress
            state.metadata.completedSteps++;
            state.metadata.lastUpdated = new Date().toISOString();
            this.emit('step_completed', {
                agentId: this.agentId,
                stepId,
                progress: state.metadata.completedSteps / state.metadata.totalSteps
            });
        }
        // Mark as completed if no input needed
        if (!state.needsInput) {
            state.completed = true;
            console.log(`✅ Documentation workflow completed: ${input.task.title}`);
        }
        return state;
    }
    /**
     * Execute individual documentation step with context enhancement
     */
    async executeStepWithContext(stepId, input) {
        // Get enhanced context from context engine
        const enhancedState = await this.getEnhancedContext(stepId);
        // Execute step with MCP enhancement
        switch (stepId) {
            case 'analyze_codebase':
                return await this.analyzeCodebaseWithMCP(enhancedState, input);
            case 'assess_existing_docs':
                return await this.assessExistingDocsWithMCP(enhancedState);
            case 'plan_documentation':
                return await this.planDocumentationWithMCP(enhancedState);
            case 'generate_api_docs':
                return await this.generateAPIDocsWithMCP(enhancedState);
            case 'create_architecture_docs':
                return await this.createArchitectureDocsWithMCP(enhancedState);
            case 'write_user_guides':
                return await this.writeUserGuidesWithMCP(enhancedState);
            case 'generate_changelog':
                return await this.generateChangelogWithMCP(enhancedState);
            case 'update_existing_docs':
                return await this.updateExistingDocsWithMCP(enhancedState);
            case 'validate_quality':
                return await this.validateQualityWithMCP(enhancedState);
            case 'publish_docs':
                return await this.publishDocsWithMCP(enhancedState);
            default:
                throw new Error(`Unknown documentation step: ${stepId}`);
        }
    }
    /**
     * Get total steps for progress tracking
     */
    getTotalSteps() {
        return this.documentationSteps.length;
    }
    /**
     * Get relevant documentation for documentation agent
     */
    async getRelevantDocumentation() {
        return {
            documentation: 'Technical documentation best practices and standards',
            apiDocumentation: 'API documentation generation and maintenance',
            architectureDocumentation: 'Software architecture documentation patterns',
            contentWriting: 'Technical writing and content creation guidelines',
            qualityAssurance: 'Documentation quality assessment and metrics'
        };
    }
    // ============================================================================
    // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
    // ============================================================================
    async analyzeCodebaseWithMCP(state, input) {
        const analysis = await ai_service_1.aiService.analyzeCodebaseForDocumentation(input.codebase, input.task.scope);
        this.state.results.codebaseAnalysis = analysis;
        return {
            results: analysis,
            needsInput: false,
            completed: false
        };
    }
    async assessExistingDocsWithMCP(state) {
        const existing = this.state.input.existing;
        const assessment = await ai_service_1.aiService.assessExistingDocumentation(existing);
        this.state.results.existingAssessment = assessment;
        return {
            results: assessment,
            needsInput: false,
            completed: false
        };
    }
    async planDocumentationWithMCP(state) {
        const codebaseAnalysis = this.state.results.codebaseAnalysis;
        const existingAssessment = this.state.results.existingAssessment;
        const plan = await ai_service_1.aiService.planDocumentationStrategy(codebaseAnalysis, existingAssessment, this.state.input.requirements);
        this.state.results.documentationPlan = plan;
        return {
            results: plan,
            needsInput: false,
            completed: false
        };
    }
    async generateAPIDocsWithMCP(state) {
        const plan = this.state.results.documentationPlan;
        const apiDocs = await ai_service_1.aiService.generateAPIDocumentation(plan, this.state.input.codebase.apis);
        this.state.results.apiDocs = apiDocs;
        return {
            results: apiDocs,
            needsInput: false,
            completed: false
        };
    }
    async createArchitectureDocsWithMCP(state) {
        const plan = this.state.results.documentationPlan;
        const architectureDocs = await ai_service_1.aiService.createArchitectureDocumentation(plan, this.state.input.codebase.architecture);
        this.state.results.architectureDocs = architectureDocs;
        return {
            results: architectureDocs,
            needsInput: false,
            completed: false
        };
    }
    async writeUserGuidesWithMCP(state) {
        const plan = this.state.results.documentationPlan;
        const userGuides = await ai_service_1.aiService.writeUserGuides(plan, this.state.input.codebase.components);
        this.state.results.userGuides = userGuides;
        return {
            results: userGuides,
            needsInput: false,
            completed: false
        };
    }
    async generateChangelogWithMCP(state) {
        const changes = this.state.input.codebase.changes;
        const changelog = await ai_service_1.aiService.generateChangelog(changes);
        this.state.results.changelog = changelog;
        return {
            results: changelog,
            needsInput: false,
            completed: false
        };
    }
    async updateExistingDocsWithMCP(state) {
        const allDocs = {
            api: this.state.results.apiDocs,
            architecture: this.state.results.architectureDocs,
            guides: this.state.results.userGuides,
            changelog: this.state.results.changelog
        };
        const updates = await ai_service_1.aiService.updateExistingDocumentation(allDocs, this.state.input.existing);
        this.state.results.updates = updates;
        return {
            results: updates,
            needsInput: false,
            completed: false
        };
    }
    async validateQualityWithMCP(state) {
        const allResults = this.state.results;
        const quality = await ai_service_1.aiService.validateDocumentationQuality(allResults, this.state.input.requirements.quality);
        this.state.results.quality = quality;
        return {
            results: quality,
            needsInput: false,
            completed: false
        };
    }
    async publishDocsWithMCP(state) {
        const allResults = this.state.results;
        const publication = await ai_service_1.aiService.publishDocumentation(allResults, this.state.input.requirements.format);
        this.state.results.publication = publication;
        return {
            results: publication,
            needsInput: false,
            completed: true // Final step
        };
    }
}
exports.DocumentationAgent = DocumentationAgent;
exports.default = DocumentationAgent;
//# sourceMappingURL=documentation-agent.js.map