import { Router } from 'express';
import { z } from 'zod';
import log from 'electron-log';
import { 
  getLanguageModelProviders, 
  getLanguageModelsByProviders,
  MODEL_OPTIONS 
} from '../../ipc/shared/language_model_helpers';

const logger = log.scope('web-server:language-models');

// Validation schemas
const CreateCustomModelSchema = z.object({
  name: z.string().min(1),
  displayName: z.string().min(1),
  description: z.string().optional(),
  maxOutputTokens: z.number().positive(),
  contextWindow: z.number().positive(),
  provider: z.string().min(1),
});

const CreateCustomProviderSchema = z.object({
  id: z.string().min(1),
  name: z.string().min(1),
  baseUrl: z.string().url(),
  apiKeyRequired: z.boolean().default(true),
});

export function setupLanguageModelRoutes() {
  const router = Router();

  // GET /api/language-models/providers - Get all language model providers
  router.get('/providers', async (req, res) => {
    try {
      const providers = await getLanguageModelProviders();
      res.json(providers);
    } catch (error) {
      logger.error('Error getting language model providers:', error);
      res.status(500).json({ error: 'Failed to get language model providers' });
    }
  });

  // GET /api/language-models/by-providers - Get models grouped by providers
  router.get('/by-providers', async (req, res) => {
    try {
      const modelsByProviders = await getLanguageModelsByProviders();
      res.json(modelsByProviders);
    } catch (error) {
      logger.error('Error getting models by providers:', error);
      res.status(500).json({ error: 'Failed to get models by providers' });
    }
  });

  // GET /api/language-models/all - Get all available models
  router.get('/all', async (req, res) => {
    try {
      const allModels: any[] = [];
      
      // Flatten all models from MODEL_OPTIONS
      Object.entries(MODEL_OPTIONS).forEach(([provider, models]) => {
        models.forEach(model => {
          allModels.push({
            ...model,
            provider,
          });
        });
      });

      res.json(allModels);
    } catch (error) {
      logger.error('Error getting all models:', error);
      res.status(500).json({ error: 'Failed to get all models' });
    }
  });

  // GET /api/language-models/provider/:provider - Get models for specific provider
  router.get('/provider/:provider', async (req, res) => {
    try {
      const provider = req.params.provider;
      const models = MODEL_OPTIONS[provider] || [];
      
      res.json(models);
    } catch (error) {
      logger.error('Error getting provider models:', error);
      res.status(500).json({ error: 'Failed to get provider models' });
    }
  });

  // POST /api/language-models/custom-model - Create custom model
  router.post('/custom-model', async (req, res) => {
    try {
      const modelData = CreateCustomModelSchema.parse(req.body);
      
      // TODO: Implement custom model creation
      // This would involve saving to settings or database
      
      logger.info(`Custom model creation requested: ${modelData.name}`);
      res.status(501).json({ error: 'Custom model creation not yet implemented' });
    } catch (error) {
      logger.error('Error creating custom model:', error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: 'Invalid model data', details: error.errors });
      } else {
        res.status(500).json({ error: 'Failed to create custom model' });
      }
    }
  });

  // DELETE /api/language-models/custom-model/:id - Delete custom model
  router.delete('/custom-model/:id', async (req, res) => {
    try {
      const modelId = req.params.id;
      
      // TODO: Implement custom model deletion
      
      logger.info(`Custom model deletion requested: ${modelId}`);
      res.status(501).json({ error: 'Custom model deletion not yet implemented' });
    } catch (error) {
      logger.error('Error deleting custom model:', error);
      res.status(500).json({ error: 'Failed to delete custom model' });
    }
  });

  // POST /api/language-models/custom-provider - Create custom provider
  router.post('/custom-provider', async (req, res) => {
    try {
      const providerData = CreateCustomProviderSchema.parse(req.body);
      
      // TODO: Implement custom provider creation
      
      logger.info(`Custom provider creation requested: ${providerData.name}`);
      res.status(501).json({ error: 'Custom provider creation not yet implemented' });
    } catch (error) {
      logger.error('Error creating custom provider:', error);
      if (error instanceof z.ZodError) {
        res.status(400).json({ error: 'Invalid provider data', details: error.errors });
      } else {
        res.status(500).json({ error: 'Failed to create custom provider' });
      }
    }
  });

  // DELETE /api/language-models/custom-provider/:id - Delete custom provider
  router.delete('/custom-provider/:id', async (req, res) => {
    try {
      const providerId = req.params.id;
      
      // TODO: Implement custom provider deletion
      
      logger.info(`Custom provider deletion requested: ${providerId}`);
      res.status(501).json({ error: 'Custom provider deletion not yet implemented' });
    } catch (error) {
      logger.error('Error deleting custom provider:', error);
      res.status(500).json({ error: 'Failed to delete custom provider' });
    }
  });

  // GET /api/language-models/openrouter - Get OpenRouter models specifically
  router.get('/openrouter', async (req, res) => {
    try {
      const openrouterModels = MODEL_OPTIONS.openrouter || [];
      res.json(openrouterModels);
    } catch (error) {
      logger.error('Error getting OpenRouter models:', error);
      res.status(500).json({ error: 'Failed to get OpenRouter models' });
    }
  });

  // GET /api/language-models/openai - Get OpenAI models specifically
  router.get('/openai', async (req, res) => {
    try {
      const openaiModels = MODEL_OPTIONS.openai || [];
      res.json(openaiModels);
    } catch (error) {
      logger.error('Error getting OpenAI models:', error);
      res.status(500).json({ error: 'Failed to get OpenAI models' });
    }
  });

  // GET /api/language-models/anthropic - Get Anthropic models specifically
  router.get('/anthropic', async (req, res) => {
    try {
      const anthropicModels = MODEL_OPTIONS.anthropic || [];
      res.json(anthropicModels);
    } catch (error) {
      logger.error('Error getting Anthropic models:', error);
      res.status(500).json({ error: 'Failed to get Anthropic models' });
    }
  });

  // GET /api/language-models/google - Get Google models specifically
  router.get('/google', async (req, res) => {
    try {
      const googleModels = MODEL_OPTIONS.google || [];
      res.json(googleModels);
    } catch (error) {
      logger.error('Error getting Google models:', error);
      res.status(500).json({ error: 'Failed to get Google models' });
    }
  });

  // POST /api/language-models/test/:provider - Test provider connection
  router.post('/test/:provider', async (req, res) => {
    try {
      const provider = req.params.provider;
      const { apiKey } = req.body;
      
      // TODO: Implement provider connection testing
      // This would make a test API call to verify the API key works
      
      logger.info(`Provider connection test requested: ${provider}`);
      res.json({ 
        success: true, 
        message: `Provider ${provider} connection test would be performed here`,
        provider 
      });
    } catch (error) {
      logger.error('Error testing provider connection:', error);
      res.status(500).json({ error: 'Failed to test provider connection' });
    }
  });

  // GET /api/language-models/stats - Get model usage statistics
  router.get('/stats', async (req, res) => {
    try {
      // TODO: Implement model usage statistics
      // This would track which models are used most frequently
      
      res.json({
        totalModels: Object.values(MODEL_OPTIONS).flat().length,
        providerCount: Object.keys(MODEL_OPTIONS).length,
        mostUsed: 'Statistics not yet implemented',
      });
    } catch (error) {
      logger.error('Error getting model statistics:', error);
      res.status(500).json({ error: 'Failed to get model statistics' });
    }
  });

  return router;
}
