@import "tailwindcss";
@import "tw-animate-css";
@plugin "@tailwindcss/typography";

/* <PERSON><PERSON> Geist Font */

@font-face {
  font-family: "Geist Mono";
  src: url("/node_modules/geist/dist/fonts/geist-mono/GeistMono-Regular.ttf")
    format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Geist Mono";
  src: url("/node_modules/geist/dist/fonts/geist-mono/GeistMono-Medium.ttf")
    format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Geist Mono";
  src: url("/node_modules/geist/dist/fonts/geist-mono/GeistMono-Bold.ttf")
    format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Geist";
  src: url("/node_modules/geist/dist/fonts/geist-sans/Geist-Regular.ttf")
    format("truetype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Geist";
  src: url("/node_modules/geist/dist/fonts/geist-sans/Geist-Medium.ttf")
    format("truetype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Geist";
  src: url("/node_modules/geist/dist/fonts/geist-sans/Geist-Bold.ttf")
    format("truetype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@custom-variant dark (&:is(.dark *));

.app-region-drag {
  app-region: drag;
}

.no-app-region-drag {
  app-region: no-drag;
}

.h-screenish {
  height: calc(100vh - 64px);
}
:root {
  --docs-bg: #f5f5f5;

  --default-font-family: "Geist", sans-serif;
  --default-mono-font-family: "Geist Mono", monospace;
  /* --background: oklch(1 0 0); */
  --background-lightest: #fff;
  --background-lighter: oklch(0.995 0.0188 292.61);
  --background: oklch(0.985 0.0188 292.61);
  --background-darker: oklch(0.955 0.0188 292.61);
  --background-darkest: oklch(0.925 0.0188 292.61);

  --foreground: oklch(0.25 0.0679 293.36);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.59 0.16 287.69);
  /* --primary: oklch(0.205 0 0); */
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.96 0.0188 292.61);
  /* --sidebar: oklch(0.975 0.0188 292.61); */
  /* --sidebar: oklch(0.985 0 0); */
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  /* --sidebar-accent: oklch(0.97 0 0); */
  --sidebar-accent: oklch(0.88 0.07 291.44);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: var(--sidebar);
  /* --sidebar-border: oklch(0.922 0 0); */
  --sidebar-ring: oklch(0.708 0 0);
}

.prose {
  --tw-prose-pre-bg: var(--background);
}
.dark {
  --docs-bg: #121212;

  --background-lightest: oklch(0.285 0 0);
  --background-lighter: oklch(0.185 0 0);
  --background: oklch(0.145 0 0);
  --background-darker: oklch(0.125 0 0);
  --background-darkest: oklch(0.105 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
  --container-6xs: 10rem;
  --container-5xs: 12rem;
  --container-4xs: 14rem;

  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer base {
  :root {
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@media (pointer: fine) {
  @supports not (selector(::-webkit-scrollbar)) {
    * {
      scrollbar-color: #dadce0 transparent;
      scrollbar-gutter: auto;
      scrollbar-width: thin;
    }
  }

  ::-webkit-scrollbar,
  ::-webkit-scrollbar-corner {
    background: transparent;
    height: 12px;
    width: 12px;
  }

  ::-webkit-scrollbar-thumb {
    background: content-box currentColor;
    border: 2px solid transparent;
    border-radius: 8px;
    color: #dadce0;
    min-height: 48px;
    min-width: 48px;
  }

  :hover::-webkit-scrollbar-thumb {
    color: #80868b;
  }

  ::-webkit-scrollbar-thumb:active {
    color: #5f6368;
  }

  ::-webkit-scrollbar-button {
    height: 0;
    width: 0;
  }
}

@keyframes marquee {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(200%);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-marquee {
  animation: marquee 2s linear infinite;
}

/* In-between text-xs and text-sm */
.text-xs-sm {
  font-size: 0.82rem;
}
