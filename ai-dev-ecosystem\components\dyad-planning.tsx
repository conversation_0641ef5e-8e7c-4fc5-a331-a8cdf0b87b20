"use client"

import React, { useState, useEffect } from 'react';
import { apiClient, App } from '@/lib/dyad-client';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  FileText, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Plus,
  Edit,
  Trash2,
  Target,
  Calendar,
  User,
  Loader2,
  RefreshCw,
  Folder,
  Code,
  Package,
  Settings
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface Task {
  id: string;
  title: string;
  description: string;
  status: 'todo' | 'in-progress' | 'completed' | 'blocked';
  priority: 'low' | 'medium' | 'high';
  assignee?: string;
  dueDate?: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

interface ProjectAnalysis {
  totalFiles: number;
  totalLines: number;
  dependencies: string[];
  devDependencies: string[];
  scripts: Record<string, string>;
  mainEntry: string;
  framework: string;
  buildTool: string;
}

interface DyadPlanningProps {
  selectedApp?: App;
}

export default function DyadPlanning({ selectedApp }: DyadPlanningProps) {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [projectAnalysis, setProjectAnalysis] = useState<ProjectAnalysis | null>(null);
  const [showAddTask, setShowAddTask] = useState(false);
  const [newTask, setNewTask] = useState({ title: '', description: '', priority: 'medium' as const });
  const { toast } = useToast();

  // Load real project data
  const loadProjectData = async () => {
    if (!selectedApp) return;

    setIsAnalyzing(true);
    try {
      // Get app files to analyze project structure
      const files = await apiClient.getAppFiles(selectedApp.id);
      
      // Read package.json for dependencies
      let packageJson: any = null;
      try {
        const packageContent = await apiClient.readAppFile(selectedApp.id, 'package.json');
        const content = typeof packageContent === 'string' ? packageContent : packageContent.content;
        packageJson = JSON.parse(content);
      } catch (err) {
        console.warn('Could not read package.json:', err);
      }

      // Analyze project structure
      const analysis: ProjectAnalysis = {
        totalFiles: files.length,
        totalLines: 0, // Would need to read all files to count lines
        dependencies: Object.keys(packageJson?.dependencies || {}),
        devDependencies: Object.keys(packageJson?.devDependencies || {}),
        scripts: packageJson?.scripts || {},
        mainEntry: packageJson?.main || 'index.js',
        framework: detectFramework(packageJson),
        buildTool: detectBuildTool(packageJson)
      };

      setProjectAnalysis(analysis);

      // Generate initial tasks based on project analysis
      const initialTasks = generateInitialTasks(analysis, selectedApp.name);
      setTasks(initialTasks);

    } catch (err) {
      console.error('Failed to analyze project:', err);
      toast({
        title: "Analysis Failed",
        description: "Could not analyze project structure",
        variant: "destructive"
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const detectFramework = (packageJson: any): string => {
    const deps = { ...packageJson?.dependencies, ...packageJson?.devDependencies };
    if (deps.react) return 'React';
    if (deps.vue) return 'Vue.js';
    if (deps['@angular/core']) return 'Angular';
    if (deps.svelte) return 'Svelte';
    if (deps.next) return 'Next.js';
    return 'Vanilla JS';
  };

  const detectBuildTool = (packageJson: any): string => {
    const scripts = packageJson?.scripts || {};
    if (scripts.dev?.includes('vite')) return 'Vite';
    if (scripts.dev?.includes('next')) return 'Next.js';
    if (scripts.build?.includes('webpack')) return 'Webpack';
    if (scripts.build?.includes('rollup')) return 'Rollup';
    return 'Unknown';
  };

  const generateInitialTasks = (analysis: ProjectAnalysis, appName: string): Task[] => {
    const tasks: Task[] = [
      {
        id: 'setup-' + Date.now(),
        title: `Initialize ${appName} project`,
        description: `Set up the basic project structure for ${appName}`,
        status: 'completed',
        priority: 'high',
        assignee: 'AI Assistant',
        dueDate: new Date().toISOString().split('T')[0],
        tags: ['setup', 'foundation'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    // Add framework-specific tasks
    if (analysis.framework === 'React') {
      tasks.push({
        id: 'react-setup-' + Date.now(),
        title: 'Configure React development environment',
        description: 'Set up React with TypeScript and build tools',
        status: 'todo',
        priority: 'high',
        assignee: 'AI Assistant',
        dueDate: new Date(Date.now() + 86400000).toISOString().split('T')[0],
        tags: ['react', 'typescript'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    }

    // Add dependency installation tasks
    if (analysis.dependencies.length > 0) {
      tasks.push({
        id: 'deps-' + Date.now(),
        title: 'Install project dependencies',
        description: `Install ${analysis.dependencies.length} runtime dependencies`,
        status: 'todo',
        priority: 'high',
        assignee: 'AI Assistant',
        dueDate: new Date(Date.now() + 172800000).toISOString().split('T')[0],
        tags: ['dependencies', 'setup'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    }

    return tasks;
  };

  const getStatusIcon = (status: Task['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'in-progress':
        return <Clock className="w-4 h-4 text-blue-500" />;
      case 'blocked':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <div className="w-4 h-4 border-2 border-[#666] rounded-full" />;
    }
  };

  const getStatusColor = (status: Task['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-900/20 text-green-400 border-green-800';
      case 'in-progress':
        return 'bg-blue-900/20 text-blue-400 border-blue-800';
      case 'blocked':
        return 'bg-red-900/20 text-red-400 border-red-800';
      default:
        return 'bg-gray-900/20 text-gray-400 border-gray-800';
    }
  };

  const getPriorityColor = (priority: Task['priority']) => {
    switch (priority) {
      case 'high':
        return 'bg-red-900/20 text-red-400 border-red-800';
      case 'medium':
        return 'bg-yellow-900/20 text-yellow-400 border-yellow-800';
      default:
        return 'bg-gray-900/20 text-gray-400 border-gray-800';
    }
  };

  const getTaskStats = () => {
    const total = tasks.length;
    const completed = tasks.filter(t => t.status === 'completed').length;
    const inProgress = tasks.filter(t => t.status === 'in-progress').length;
    const blocked = tasks.filter(t => t.status === 'blocked').length;
    
    return { total, completed, inProgress, blocked };
  };

  const handleAddTask = async () => {
    if (!newTask.title.trim()) return;

    const task: Task = {
      id: Date.now().toString(),
      title: newTask.title,
      description: newTask.description,
      status: 'todo',
      priority: newTask.priority,
      assignee: 'AI Assistant',
      dueDate: new Date(Date.now() + 86400000 * 3).toISOString().split('T')[0],
      tags: ['new', 'feature'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    setTasks(prev => [...prev, task]);
    setNewTask({ title: '', description: '', priority: 'medium' });
    setShowAddTask(false);
  };

  const handleTaskAction = async (taskId: string, action: 'start' | 'complete' | 'block') => {
    setTasks(prev => prev.map(task => {
      if (task.id === taskId) {
        let newStatus: Task['status'] = task.status;
        switch (action) {
          case 'start':
            newStatus = 'in-progress';
            break;
          case 'complete':
            newStatus = 'completed';
            break;
          case 'block':
            newStatus = 'blocked';
            break;
        }
        return { ...task, status: newStatus, updatedAt: new Date().toISOString() };
      }
      return task;
    }));
  };

  const stats = getTaskStats();

  useEffect(() => {
    if (selectedApp) {
      loadProjectData();
    }
  }, [selectedApp]);

  if (!selectedApp) {
    return (
      <div className="flex-1 bg-[#0a0a0a] flex items-center justify-center">
        <div className="text-center text-[#666] p-8">
          <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-medium mb-2">No app selected</h3>
          <p className="text-sm">Select an app to view its planning</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 bg-[#0a0a0a] flex flex-col">
      {/* Planning Header */}
      <div className="h-12 bg-[#111111] border-b border-[#1a1a1a] flex items-center justify-between px-4">
        <div className="flex items-center gap-3">
          <FileText className="w-4 h-4 text-purple-500" />
          <span className="text-sm text-white font-medium">Project Planning</span>
          <Badge variant="secondary" className="bg-[#2a2a2a] text-[#888] text-[10px] px-2 py-0">
            {selectedApp.name}
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="ghost"
            onClick={loadProjectData}
            disabled={isAnalyzing}
            className="h-7 px-2 text-[#666] hover:text-white"
          >
            {isAnalyzing ? (
              <Loader2 className="w-3 h-3 animate-spin" />
            ) : (
              <RefreshCw className="w-3 h-3" />
            )}
          </Button>
          <Button
            size="sm"
            onClick={() => setShowAddTask(true)}
            className="h-7 px-3 bg-purple-600 hover:bg-purple-700"
          >
            <Plus className="w-3 h-3 mr-1" />
            Add Task
          </Button>
        </div>
      </div>

      {/* Project Analysis */}
      {projectAnalysis && (
        <div className="p-4 border-b border-[#1a1a1a]">
          <div className="grid grid-cols-4 gap-4">
            <div className="bg-[#111111] rounded-lg p-3 border border-[#1a1a1a]">
              <div className="flex items-center gap-2 mb-1">
                <Folder className="w-3 h-3 text-blue-400" />
                <span className="text-xs text-[#666]">Files</span>
              </div>
              <div className="text-lg font-bold text-white">{projectAnalysis.totalFiles}</div>
            </div>
            <div className="bg-[#111111] rounded-lg p-3 border border-[#1a1a1a]">
              <div className="flex items-center gap-2 mb-1">
                <Package className="w-3 h-3 text-green-400" />
                <span className="text-xs text-[#666]">Dependencies</span>
              </div>
              <div className="text-lg font-bold text-white">{projectAnalysis.dependencies.length}</div>
            </div>
            <div className="bg-[#111111] rounded-lg p-3 border border-[#1a1a1a]">
              <div className="flex items-center gap-2 mb-1">
                <Code className="w-3 h-3 text-purple-400" />
                <span className="text-xs text-[#666]">Framework</span>
              </div>
              <div className="text-sm font-bold text-white">{projectAnalysis.framework}</div>
            </div>
            <div className="bg-[#111111] rounded-lg p-3 border border-[#1a1a1a]">
              <div className="flex items-center gap-2 mb-1">
                <Settings className="w-3 h-3 text-orange-400" />
                <span className="text-xs text-[#666]">Build Tool</span>
              </div>
              <div className="text-sm font-bold text-white">{projectAnalysis.buildTool}</div>
            </div>
          </div>
        </div>
      )}

      {/* Stats Overview */}
      <div className="p-4 border-b border-[#1a1a1a]">
        <div className="grid grid-cols-4 gap-4">
          <div className="bg-[#111111] rounded-lg p-3 border border-[#1a1a1a]">
            <div className="text-xs text-[#666] mb-1">Total Tasks</div>
            <div className="text-lg font-bold text-white">{stats.total}</div>
          </div>
          <div className="bg-[#111111] rounded-lg p-3 border border-[#1a1a1a]">
            <div className="text-xs text-[#666] mb-1">Completed</div>
            <div className="text-lg font-bold text-green-400">{stats.completed}</div>
          </div>
          <div className="bg-[#111111] rounded-lg p-3 border border-[#1a1a1a]">
            <div className="text-xs text-[#666] mb-1">In Progress</div>
            <div className="text-lg font-bold text-blue-400">{stats.inProgress}</div>
          </div>
          <div className="bg-[#111111] rounded-lg p-3 border border-[#1a1a1a]">
            <div className="text-xs text-[#666] mb-1">Blocked</div>
            <div className="text-lg font-bold text-red-400">{stats.blocked}</div>
          </div>
        </div>
      </div>

      {/* Task List */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-3">
          {tasks.map((task) => (
            <div
              key={task.id}
              className="bg-[#111111] rounded-lg border border-[#1a1a1a] p-4 hover:border-[#333] transition-colors"
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-3">
                  {getStatusIcon(task.status)}
                  <h3 className="text-sm font-medium text-white">{task.title}</h3>
                  <Badge 
                    variant="secondary" 
                    className={`text-[10px] px-2 py-0 ${getStatusColor(task.status)}`}
                  >
                    {task.status.replace('-', ' ')}
                  </Badge>
                  <Badge 
                    variant="secondary" 
                    className={`text-[10px] px-2 py-0 ${getPriorityColor(task.priority)}`}
                  >
                    {task.priority}
                  </Badge>
                </div>
                
                <div className="flex items-center gap-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleTaskAction(task.id, 'start')}
                    disabled={task.status !== 'todo'}
                    className="h-6 w-6 p-0 text-[#666] hover:text-white hover:bg-[#1a1a1a]"
                  >
                    <Edit className="w-3 h-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleTaskAction(task.id, 'complete')}
                    disabled={task.status === 'completed'}
                    className="h-6 w-6 p-0 text-[#666] hover:text-green-400 hover:bg-green-900/20"
                  >
                    <CheckCircle className="w-3 h-3" />
                  </Button>
                </div>
              </div>
              
              <p className="text-xs text-[#888] mb-3 leading-relaxed">
                {task.description}
              </p>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  {task.assignee && (
                    <div className="flex items-center gap-1 text-xs text-[#666]">
                      <User className="w-3 h-3" />
                      <span>{task.assignee}</span>
                    </div>
                  )}
                  
                  {task.dueDate && (
                    <div className="flex items-center gap-1 text-xs text-[#666]">
                      <Calendar className="w-3 h-3" />
                      <span>{new Date(task.dueDate).toLocaleDateString()}</span>
                    </div>
                  )}
                </div>
                
                <div className="flex items-center gap-1">
                  {task.tags.map((tag) => (
                    <Badge
                      key={tag}
                      variant="secondary"
                      className="bg-[#2a2a2a] text-[#888] text-[10px] px-2 py-0"
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>

      {/* Progress Bar */}
      <div className="p-4 border-t border-[#1a1a1a]">
        <div className="flex items-center justify-between mb-2">
          <span className="text-xs text-[#666]">Overall Progress</span>
          <span className="text-xs text-white">
            {stats.completed}/{stats.total} tasks completed
          </span>
        </div>
        <div className="w-full bg-[#1a1a1a] rounded-full h-2">
          <div 
            className="bg-green-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${stats.total > 0 ? (stats.completed / stats.total) * 100 : 0}%` }}
          />
        </div>
      </div>
    </div>
  );
}
