"use client"

import React, { useState } from 'react';
import { App } from '@/lib/dyad-client';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  FileText, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Plus,
  Edit,
  Trash2,
  Target,
  Calendar,
  User
} from 'lucide-react';

interface Task {
  id: string;
  title: string;
  description: string;
  status: 'todo' | 'in-progress' | 'completed' | 'blocked';
  priority: 'low' | 'medium' | 'high';
  assignee?: string;
  dueDate?: string;
  tags: string[];
}

interface DyadPlanningProps {
  selectedApp?: App;
}

export default function DyadPlanning({ selectedApp }: DyadPlanningProps) {
  const [tasks, setTasks] = useState<Task[]>([
    {
      id: '1',
      title: 'Set up project structure',
      description: 'Create the basic folder structure and configuration files',
      status: 'completed',
      priority: 'high',
      assignee: 'AI Assistant',
      dueDate: '2024-01-15',
      tags: ['setup', 'foundation']
    },
    {
      id: '2',
      title: 'Implement user authentication',
      description: 'Add login/logout functionality with JWT tokens',
      status: 'in-progress',
      priority: 'high',
      assignee: 'AI Assistant',
      dueDate: '2024-01-20',
      tags: ['auth', 'security']
    },
    {
      id: '3',
      title: 'Create dashboard UI',
      description: 'Design and implement the main dashboard interface',
      status: 'todo',
      priority: 'medium',
      dueDate: '2024-01-25',
      tags: ['ui', 'dashboard']
    },
    {
      id: '4',
      title: 'Add data visualization',
      description: 'Implement charts and graphs for data display',
      status: 'todo',
      priority: 'medium',
      dueDate: '2024-01-30',
      tags: ['charts', 'visualization']
    },
    {
      id: '5',
      title: 'Optimize performance',
      description: 'Review and optimize application performance',
      status: 'blocked',
      priority: 'low',
      dueDate: '2024-02-05',
      tags: ['performance', 'optimization']
    }
  ]);

  const getStatusIcon = (status: Task['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'in-progress':
        return <Clock className="w-4 h-4 text-blue-500" />;
      case 'blocked':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <div className="w-4 h-4 border-2 border-[#666] rounded-full" />;
    }
  };

  const getStatusColor = (status: Task['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-900/20 text-green-400 border-green-800';
      case 'in-progress':
        return 'bg-blue-900/20 text-blue-400 border-blue-800';
      case 'blocked':
        return 'bg-red-900/20 text-red-400 border-red-800';
      default:
        return 'bg-gray-900/20 text-gray-400 border-gray-800';
    }
  };

  const getPriorityColor = (priority: Task['priority']) => {
    switch (priority) {
      case 'high':
        return 'bg-red-900/20 text-red-400 border-red-800';
      case 'medium':
        return 'bg-yellow-900/20 text-yellow-400 border-yellow-800';
      default:
        return 'bg-gray-900/20 text-gray-400 border-gray-800';
    }
  };

  const getTaskStats = () => {
    const total = tasks.length;
    const completed = tasks.filter(t => t.status === 'completed').length;
    const inProgress = tasks.filter(t => t.status === 'in-progress').length;
    const blocked = tasks.filter(t => t.status === 'blocked').length;
    
    return { total, completed, inProgress, blocked };
  };

  const stats = getTaskStats();

  if (!selectedApp) {
    return (
      <div className="flex-1 bg-[#0a0a0a] flex items-center justify-center">
        <div className="text-center text-[#666] p-8">
          <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-medium mb-2">No app selected</h3>
          <p className="text-sm">Select an app to view its planning</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 bg-[#0a0a0a] flex flex-col">
      {/* Planning Header */}
      <div className="h-12 bg-[#111111] border-b border-[#1a1a1a] flex items-center justify-between px-4">
        <div className="flex items-center gap-3">
          <FileText className="w-4 h-4 text-purple-500" />
          <span className="text-sm text-white font-medium">Project Planning</span>
          <Badge variant="secondary" className="bg-[#2a2a2a] text-[#888] text-[10px] px-2 py-0">
            {selectedApp.name}
          </Badge>
        </div>
        
        <Button
          size="sm"
          className="h-7 px-3 bg-purple-600 hover:bg-purple-700"
        >
          <Plus className="w-3 h-3 mr-1" />
          Add Task
        </Button>
      </div>

      {/* Stats Overview */}
      <div className="p-4 border-b border-[#1a1a1a]">
        <div className="grid grid-cols-4 gap-4">
          <div className="bg-[#111111] rounded-lg p-3 border border-[#1a1a1a]">
            <div className="text-xs text-[#666] mb-1">Total Tasks</div>
            <div className="text-lg font-bold text-white">{stats.total}</div>
          </div>
          <div className="bg-[#111111] rounded-lg p-3 border border-[#1a1a1a]">
            <div className="text-xs text-[#666] mb-1">Completed</div>
            <div className="text-lg font-bold text-green-400">{stats.completed}</div>
          </div>
          <div className="bg-[#111111] rounded-lg p-3 border border-[#1a1a1a]">
            <div className="text-xs text-[#666] mb-1">In Progress</div>
            <div className="text-lg font-bold text-blue-400">{stats.inProgress}</div>
          </div>
          <div className="bg-[#111111] rounded-lg p-3 border border-[#1a1a1a]">
            <div className="text-xs text-[#666] mb-1">Blocked</div>
            <div className="text-lg font-bold text-red-400">{stats.blocked}</div>
          </div>
        </div>
      </div>

      {/* Task List */}
      <ScrollArea className="flex-1">
        <div className="p-4 space-y-3">
          {tasks.map((task) => (
            <div
              key={task.id}
              className="bg-[#111111] rounded-lg border border-[#1a1a1a] p-4 hover:border-[#333] transition-colors"
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-3">
                  {getStatusIcon(task.status)}
                  <h3 className="text-sm font-medium text-white">{task.title}</h3>
                  <Badge 
                    variant="secondary" 
                    className={`text-[10px] px-2 py-0 ${getStatusColor(task.status)}`}
                  >
                    {task.status.replace('-', ' ')}
                  </Badge>
                  <Badge 
                    variant="secondary" 
                    className={`text-[10px] px-2 py-0 ${getPriorityColor(task.priority)}`}
                  >
                    {task.priority}
                  </Badge>
                </div>
                
                <div className="flex items-center gap-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-6 w-6 p-0 text-[#666] hover:text-white hover:bg-[#1a1a1a]"
                  >
                    <Edit className="w-3 h-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-6 w-6 p-0 text-[#666] hover:text-red-400 hover:bg-[#1a1a1a]"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>
              
              <p className="text-xs text-[#888] mb-3 leading-relaxed">
                {task.description}
              </p>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  {task.assignee && (
                    <div className="flex items-center gap-1 text-xs text-[#666]">
                      <User className="w-3 h-3" />
                      <span>{task.assignee}</span>
                    </div>
                  )}
                  
                  {task.dueDate && (
                    <div className="flex items-center gap-1 text-xs text-[#666]">
                      <Calendar className="w-3 h-3" />
                      <span>{new Date(task.dueDate).toLocaleDateString()}</span>
                    </div>
                  )}
                </div>
                
                <div className="flex items-center gap-1">
                  {task.tags.map((tag) => (
                    <Badge
                      key={tag}
                      variant="secondary"
                      className="bg-[#2a2a2a] text-[#888] text-[10px] px-2 py-0"
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>

      {/* Progress Bar */}
      <div className="p-4 border-t border-[#1a1a1a]">
        <div className="flex items-center justify-between mb-2">
          <span className="text-xs text-[#666]">Overall Progress</span>
          <span className="text-xs text-white">
            {stats.completed}/{stats.total} tasks completed
          </span>
        </div>
        <div className="w-full bg-[#1a1a1a] rounded-full h-2">
          <div 
            className="bg-green-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(stats.completed / stats.total) * 100}%` }}
          />
        </div>
      </div>
    </div>
  );
}
