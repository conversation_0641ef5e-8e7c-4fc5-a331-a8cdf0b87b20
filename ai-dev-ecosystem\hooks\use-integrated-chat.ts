import { useState, useEffect, useCallback } from 'react';
import { useDyadApps, useDyadChats, useDyadStreaming, useDyadModels } from '@/hooks/use-dyad';
import { apiClient, App, Chat, ChatMessage } from '@/lib/dyad-client';

// Types that match the existing UI
interface Message {
  id: string;
  type: "user" | "ai";
  content: string;
  timestamp: Date;
  tasks?: Task[];
}

interface Task {
  id: string;
  title: string;
  status: "pending" | "running" | "completed" | "error";
  type: "component" | "api" | "database" | "test";
  progress?: number;
}

export function useIntegratedChat() {
  // Dyad hooks
  const { apps, createApp } = useDyadApps();
  const { chats, createChat, deleteChat } = useDyadChats();
  const { models } = useDyadModels();
  const { isStreaming, streamContent, startStream, resetStream } = useDyadStreaming();

  // UI state
  const [selectedApp, setSelectedApp] = useState<App | null>(null);
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);

  // DYAD-STYLE: 1 Project = 1 Chat relationship
  // When an app is selected, automatically find and select its primary (and only) chat
  useEffect(() => {
    if (selectedApp) {
      console.log(`App selected: ${selectedApp.name} (ID: ${selectedApp.id})`);

      // Find the primary chat for this app (there should be exactly one)
      const appChats = chats.filter(chat => chat.appId === selectedApp.id);

      if (appChats.length > 0) {
        const primaryChat = appChats[0]; // In Dyad, there's only one chat per app
        console.log(`Auto-selecting primary chat ${primaryChat.id} for app ${selectedApp.name}`);
        setSelectedChat(primaryChat);
      } else {
        console.log(`No chat found for app ${selectedApp.name}, will create one when needed`);
        setSelectedChat(null);
        setMessages([]);
      }
    } else {
      // No app selected, clear chat selection
      setSelectedChat(null);
      setMessages([]);
    }
  }, [selectedApp, chats]);

  // Load messages when chat is selected
  useEffect(() => {
    if (selectedChat) {
      console.log(`Chat selected: ${selectedChat.id}, loading messages...`);
      loadChatMessages(selectedChat);
    } else {
      console.log('No chat selected, clearing messages');
      setMessages([]);
    }
  }, [selectedChat]);

  // Convert Dyad ChatMessage to UI Message
  const convertToUIMessage = (chatMessage: ChatMessage): Message => ({
    id: chatMessage.id.toString(),
    type: chatMessage.role === 'user' ? 'user' : 'ai',
    content: chatMessage.content,
    timestamp: new Date(chatMessage.createdAt),
    // Add mock tasks for AI messages to maintain UI compatibility
    tasks: chatMessage.role === 'assistant' ? [
      {
        id: `task-${chatMessage.id}`,
        title: "Processing your request",
        status: "completed",
        type: "component",
        progress: 100,
      }
    ] : undefined,
  });

  const loadChatMessages = async (chat: Chat) => {
    if (!chat || !chat.id || !Number.isInteger(chat.id) || chat.id <= 0) {
      console.warn('Invalid chat provided:', chat);
      return;
    }

    try {
      setIsLoadingMessages(true);
      console.log(`Loading messages for chat ${chat.id}...`);
      const fullChat = await apiClient.getChat(chat.id);
      console.log(`Loaded chat data:`, fullChat);

      if (fullChat.messages) {
        const uiMessages = fullChat.messages.map(convertToUIMessage);
        console.log(`Converted ${uiMessages.length} messages:`, uiMessages);
        setMessages(uiMessages);
      } else {
        console.log('No messages found in chat');
        setMessages([]);
      }
    } catch (err) {
      console.error('Failed to load messages:', err);
      // Only clear messages if it's not an invalid chat ID error
      if (err instanceof Error && err.message.includes('Invalid chat ID')) {
        console.warn('Chat does not exist in Dyad backend, keeping current state');
      } else {
        // Fallback to empty messages for other errors
        setMessages([]);
      }
    } finally {
      setIsLoadingMessages(false);
    }
  };

  const handleCreateApp = async (name: string) => {
    try {
      console.log(`Creating new app with primary chat: ${name}`);
      const newApp = await createApp(name, 'react');
      console.log(`Successfully created app:`, newApp);

      // DYAD-STYLE: App creation automatically creates a primary chat
      // The backend should have created a chat, but let's ensure we have one
      setSelectedApp(newApp);

      // The useEffect above will automatically select the primary chat
      // when the app is set and chats are loaded

      return newApp;
    } catch (err) {
      console.error('Failed to create app:', err);
      console.error('Error details:', err instanceof Error ? err.message : err);

      // Show user-friendly error
      alert(`Failed to create project "${name}". Please check if the Dyad backend is running.`);
      throw err;
    }
  };

  const generateProjectName = () => {
    const adjectives = ['Swift', 'Bright', 'Bold', 'Fresh', 'Smart', 'Quick', 'Clean', 'Pure'];
    const nouns = ['Project', 'App', 'Build', 'Code', 'Work', 'Lab', 'Studio', 'Space'];
    const adj = adjectives[Math.floor(Math.random() * adjectives.length)];
    const noun = nouns[Math.floor(Math.random() * nouns.length)];
    return `${adj} ${noun}`;
  };

  const handleCreateChat = async () => {
    if (!selectedApp) {
      // DYAD-STYLE: Create a fresh project with its primary chat
      try {
        const projectName = generateProjectName();
        console.log(`Creating fresh project with primary chat: ${projectName}`);
        const newApp = await handleCreateApp(projectName);

        // Wait for the chats to be loaded and the primary chat to be auto-selected
        // The useEffect will handle selecting the primary chat
        return null; // Will be set by useEffect
      } catch (err) {
        console.error('Failed to create app and chat:', err);
        return null;
      }
    }

    // DYAD-STYLE: In true Dyad, each project has exactly ONE chat
    // If we reach here, it means we're trying to create a second chat for an app
    // This should not happen in proper Dyad behavior
    console.warn(`Attempted to create additional chat for app ${selectedApp.name}. In Dyad, each project has exactly one chat.`);

    // Instead of creating a new chat, return the existing primary chat
    const appChats = chats.filter(chat => chat.appId === selectedApp.id);
    if (appChats.length > 0) {
      console.log(`Returning existing primary chat for app ${selectedApp.name}`);
      setSelectedChat(appChats[0]);
      return appChats[0];
    }

    // If somehow no chat exists, create the primary chat
    try {
      console.log(`Creating missing primary chat for app ${selectedApp.name}`);
      const newChat = await createChat(selectedApp.id, selectedApp.name);
      setSelectedChat(newChat);
      return newChat;
    } catch (err) {
      console.error('Failed to create primary chat:', err);
      return null;
    }
  };

  const handleSendMessage = async (content: string, selectedModel: string = 'moonshotai/kimi-k2') => {
    if (!content.trim()) return;

    // Ensure we have an app and chat
    let currentApp = selectedApp;
    let currentChat = selectedChat;

    if (!currentApp) {
      const projectName = generateProjectName();
      console.log(`Creating fresh project for message: ${projectName}`);
      currentApp = await handleCreateApp(projectName);
      if (!currentApp) return;
    }

    if (!currentChat) {
      currentChat = await handleCreateChat();
      if (!currentChat) return;
    }

    // Add user message to UI immediately
    const userMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content,
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, userMessage]);

    try {
      // Add user message to backend
      console.log('Sending message:', { content, role: 'user' });
      await apiClient.addMessage(currentChat.id, { content, role: 'user' });

      // Start streaming AI response
      resetStream();
      
      // Add a temporary AI message for the streaming response
      const tempAiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: "ai",
        content: "",
        timestamp: new Date(),
        tasks: [
          {
            id: Date.now().toString(),
            title: "Processing your request",
            status: "running",
            type: "component",
            progress: 30,
          },
        ],
      };
      setMessages(prev => [...prev, tempAiMessage]);

      // Start the actual stream
      console.log(`Starting stream for chat ${currentChat.id} with model ${selectedModel}`);
      await startStream(
        currentChat.id,
        content,
        { name: selectedModel, provider: 'openrouter' },
        currentApp.id
      );
      console.log('Stream completed, reloading messages...');

      // Reload messages after streaming to get the final persisted content
      await loadChatMessages(currentChat);

    } catch (err) {
      console.error('Failed to send message:', err);
      
      // Add error message
      const errorMessage: Message = {
        id: (Date.now() + 2).toString(),
        type: "ai",
        content: "Sorry, I encountered an error processing your request. Please try again.",
        timestamp: new Date(),
        tasks: [
          {
            id: Date.now().toString(),
            title: "Error occurred",
            status: "error",
            type: "component",
          },
        ],
      };
      setMessages(prev => [...prev, errorMessage]);
    }
  };

  // Update the last AI message with streaming content
  useEffect(() => {
    if (streamContent && messages.length > 0) {
      console.log(`Updating AI message with stream content (${streamContent.length} chars):`, streamContent.substring(0, 100) + '...');
      setMessages(prev => {
        const newMessages = [...prev];
        const lastMessage = newMessages[newMessages.length - 1];

        if (lastMessage && lastMessage.type === 'ai') {
          lastMessage.content = streamContent;
          if (lastMessage.tasks) {
            lastMessage.tasks[0].status = isStreaming ? "running" : "completed";
            lastMessage.tasks[0].progress = isStreaming ? 75 : 100;
          }
        }

        return newMessages;
      });
    }
  }, [streamContent, isStreaming, messages.length]);

  const handleSelectChat = async (chat: Chat) => {
    console.log(`DYAD-STYLE: Selecting chat ${chat.id} tethered to app ${chat.appId}`);

    // DYAD-STYLE: Chat selection automatically selects the tethered project
    // This maintains the 1:1 relationship between projects and chats
    if (chat.appId) {
      try {
        const app = apps.find(a => a.id === chat.appId);
        if (app) {
          console.log(`Auto-selecting tethered project: ${app.name}`);
          setSelectedApp(app); // This will trigger the useEffect to select the chat
          // The useEffect will handle setting the selectedChat
        } else {
          console.warn(`Tethered app ${chat.appId} not found for chat ${chat.id}`);
          // Fallback: select chat directly if app not found
          setSelectedChat(chat);
        }
      } catch (err) {
        console.error('Failed to set tethered app for chat:', err);
        // Fallback: select chat directly
        setSelectedChat(chat);
      }
    } else {
      console.warn(`Chat ${chat.id} has no tethered app`);
      setSelectedChat(chat);
    }
  };



  const handleDeleteChat = async (chat: Chat) => {
    try {
      await deleteChat(chat.id);
      if (selectedChat?.id === chat.id) {
        setSelectedChat(null);
        setMessages([]);
      }
    } catch (err) {
      console.error('Failed to delete chat:', err);
    }
  };

  return {
    // State
    selectedApp,
    selectedChat,
    messages,
    isStreaming,
    isLoadingMessages,
    apps,
    chats,
    models,

    // Actions
    handleSendMessage,
    handleCreateChat,
    handleSelectChat,
    handleDeleteChat,
    setSelectedApp,

    // Utilities
    formatTime: (date: Date) => date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
  };
}
