/**
 * AG3NT Framework and Context Engine API Client
 * Provides integration with the autonomous development ecosystem
 */

import { apiClient } from './dyad-client';

export interface Agent {
  id: string;
  name: string;
  type: string;
  status: 'active' | 'inactive' | 'busy';
  capabilities: string[];
  metadata: Record<string, any>;
}

export interface ProjectPlan {
  id: string;
  title: string;
  description: string;
  phases: ProjectPhase[];
  estimatedDuration: number;
  dependencies: string[];
  risks: string[];
}

export interface ProjectPhase {
  id: string;
  name: string;
  description: string;
  tasks: Task[];
  estimatedDuration: number;
  prerequisites: string[];
}

export interface Task {
  id: string;
  title: string;
  description: string;
  type: 'feature' | 'bugfix' | 'refactor' | 'documentation';
  priority: 'low' | 'medium' | 'high' | 'critical';
  estimatedHours: number;
  dependencies: string[];
  assignedAgent?: string;
}

export interface ContextAnalysis {
  filesProcessed: number;
  nodesCreated: number;
  relationshipsCreated: number;
  codeComplexity: number;
  testCoverage: number;
  documentationCoverage: number;
  dependencies: string[];
}

export interface CodeGenerationResult {
  code: string;
  language: string;
  framework: string;
  tests: string;
  documentation: string;
  dependencies: string[];
  estimatedComplexity: number;
}

export interface AgentStats {
  timestamp: string;
  ag3ntFramework: {
    totalAgents: number;
    activeAgents: number;
    totalSessions: number;
    successfulExecutions: number;
    failedExecutions: number;
    averageExecutionTime: number;
  };
  contextEngine: {
    database: {
      nodes: number;
      relationships: number;
      indexes: number;
    };
    ingestion: {
      totalFiles: number;
      processedFiles: number;
      failedFiles: number;
      averageProcessingTime: number;
    };
    retrieval: {
      totalQueries: number;
      successfulQueries: number;
      failedQueries: number;
      averageQueryTime: number;
    };
  };
}

export class AgentsClient {
  private baseUrl: string;

  constructor(baseUrl: string = 'http://localhost:3002') {
    this.baseUrl = baseUrl;
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      credentials: 'include',
      ...options,
    });

    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}`;
      try {
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const error = await response.json();
          errorMessage = error.error || error.message || errorMessage;
        } else {
          const textError = await response.text();
          errorMessage = textError || errorMessage;
        }
      } catch (e) {
        errorMessage = `HTTP ${response.status}`;
      }
      throw new Error(errorMessage);
    }

    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return response.json();
    } else {
      return response.text() as Promise<T>;
    }
  }

  // Health check for autonomous ecosystem
  async getHealth(): Promise<{
    timestamp: string;
    ag3ntFramework: any;
    contextEngine: any;
    status: string;
  }> {
    return this.request('/api/agents/health');
  }

  // List all available agents
  async getAgents(): Promise<Agent[]> {
    return this.request('/api/agents');
  }

  // Create project plan using planning agent
  async createProjectPlan(appId: number, requirements: string, techStack?: string): Promise<{
    projectPlan: ProjectPlan;
    context: ContextAnalysis | null;
  }> {
    return this.request('/api/agents/project-plan', {
      method: 'POST',
      body: JSON.stringify({ appId, requirements, techStack }),
    });
  }

  // Create task breakdown using task planner
  async createTaskPlan(appId: number, featureRequest: string, priority?: string): Promise<{
    taskPlan: Task[];
    context: {
      relevantFiles: string[];
      totalContext: number;
    };
  }> {
    return this.request('/api/agents/task-plan', {
      method: 'POST',
      body: JSON.stringify({ appId, featureRequest, priority }),
    });
  }

  // Generate code using coding agents
  async generateCode(appId: number, task: {
    description: string;
    type: string;
    requirements: string[];
  }, agentType?: string): Promise<{
    codeResult: CodeGenerationResult;
    context: {
      relevantFiles: string[];
      totalContext: number;
    };
  }> {
    return this.request('/api/agents/code-generation', {
      method: 'POST',
      body: JSON.stringify({ appId, task, agentType }),
    });
  }

  // Analyze codebase context
  async analyzeContext(appId: number, analysisType?: string): Promise<{
    analysis: ContextAnalysis;
    statistics: any;
    appName: string;
  }> {
    return this.request('/api/agents/context-analysis', {
      method: 'POST',
      body: JSON.stringify({ appId, analysisType }),
    });
  }

  // Get framework statistics
  async getStats(): Promise<AgentStats> {
    return this.request('/api/agents/stats');
  }

  // Stream agent execution results
  async streamAgentExecution(agentType: string, input: any, config: any, onChunk: (chunk: any) => void) {
    const response = await fetch(`${this.baseUrl}/api/agents/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({ agentType, input, config }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('No response body');
    }

    const decoder = new TextDecoder();
    
    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              onChunk(data);
              if (data.done) return;
            } catch (e) {
              // Ignore malformed JSON
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }
}

// Export singleton instance
export const agentsClient = new AgentsClient();

// Export types
export type {
  Agent,
  ProjectPlan,
  ProjectPhase,
  Task,
  ContextAnalysis,
  CodeGenerationResult,
  AgentStats,
};
