/**
 * AG3NT Framework - Unified Context Engine (Standalone)
 *
 * Simplified context engine for standalone framework.
 * This is a basic implementation - full context engine features
 * are available in the complete AG3NT platform.
 */
import { EventEmitter } from "events";
export interface AgentContextScope {
    agentType: string;
    operationId: string;
    requiredCapabilities: string[];
    contextFilters?: string[];
}
export interface ProjectContext {
    projectId: string;
    name: string;
    description: string;
    technologies: string[];
    structure: any;
}
export interface CodebaseContext {
    files: FileContext[];
    symbols: SymbolContext[];
    dependencies: any[];
}
export interface FileContext {
    path: string;
    content: string;
    language: string;
    symbols: string[];
}
export interface SymbolContext {
    name: string;
    type: string;
    location: string;
    references: string[];
}
export interface ContextEnrichment {
    type: string;
    data: any;
    confidence: number;
}
export interface EnhancedContext {
    original: any;
    enrichments: ContextEnrichment[];
    metadata: any;
}
export interface UnifiedContextEngineConfig {
    enableMCP?: boolean;
    enableSequentialThinking?: boolean;
    enableRAG?: boolean;
    enableNeo4j?: boolean;
    enableTemporalDatabase?: boolean;
}
/**
 * Simplified Unified Context Engine for standalone framework
 */
export declare class UnifiedContextEngine extends EventEmitter {
    private config;
    private contexts;
    private initialized;
    constructor(config?: UnifiedContextEngineConfig);
    initialize(): Promise<void>;
    registerAgent(scope: AgentContextScope): Promise<void>;
    getContext(agentType: string, operationId: string): Promise<any>;
    updateContext(agentType: string, operationId: string, data: any): Promise<void>;
    enrichContext(context: any): Promise<EnhancedContext>;
    queryContext(query: string): Promise<any[]>;
    getProjectContext(projectId: string): Promise<ProjectContext | null>;
    updateProjectContext(projectId: string, context: Partial<ProjectContext>): Promise<void>;
    getCodebaseContext(projectId: string): Promise<CodebaseContext>;
    updateCodebaseContext(projectId: string, context: Partial<CodebaseContext>): Promise<void>;
    getStats(): any;
    cleanup(): Promise<void>;
    shutdown(): Promise<void>;
}
export declare const unifiedContextEngine: UnifiedContextEngine;
export default UnifiedContextEngine;
//# sourceMappingURL=unified-context-engine.d.ts.map