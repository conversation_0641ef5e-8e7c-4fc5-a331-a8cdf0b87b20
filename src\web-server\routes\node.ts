import { Router } from 'express';
import log from 'electron-log';
import { runShellCommand } from '../../ipc/utils/runShellCommand';
import { platform, arch } from 'os';

const logger = log.scope('web-server:node');

export function setupNodeRoutes() {
  const router = Router();

  // GET /api/node/status - Get Node.js and package manager status
  router.get('/status', async (req, res) => {
    try {
      logger.log(
        "handling node status for platform:",
        platform(),
        "and arch:",
        arch(),
      );

      // Run checks in parallel
      const [nodeVersion, pnpmVersion] = await Promise.all([
        runShellCommand("node --version"),
        // First, check if pnpm is installed.
        // If not, try to install it using corepack.
        // If both fail, then pnpm is not available.
        runShellCommand(
          "pnpm --version || (corepack enable pnpm && pnpm --version) || (npm install -g pnpm@latest-10 && pnpm --version)",
        ),
      ]);

      const nodeSystemInfo = {
        nodeVersion: nodeVersion.stdout.trim(),
        pnpmVersion: pnpmVersion.stdout.trim(),
        platform: platform(),
        arch: arch(),
        nodeAvailable: nodeVersion.success,
        pnpmAvailable: pnpmVersion.success,
      };

      res.json(nodeSystemInfo);
    } catch (error) {
      logger.error('Error getting Node.js status:', error);
      res.status(500).json({ error: 'Failed to get Node.js status' });
    }
  });

  // GET /api/node/version - Get Node.js version only
  router.get('/version', async (req, res) => {
    try {
      const result = await runShellCommand("node --version");
      
      if (result.success) {
        res.json({ 
          version: result.stdout.trim(),
          available: true 
        });
      } else {
        res.json({ 
          version: null,
          available: false,
          error: result.stderr 
        });
      }
    } catch (error) {
      logger.error('Error getting Node.js version:', error);
      res.status(500).json({ error: 'Failed to get Node.js version' });
    }
  });

  // GET /api/node/pnpm-version - Get pnpm version only
  router.get('/pnpm-version', async (req, res) => {
    try {
      const result = await runShellCommand("pnpm --version");
      
      if (result.success) {
        res.json({ 
          version: result.stdout.trim(),
          available: true 
        });
      } else {
        res.json({ 
          version: null,
          available: false,
          error: result.stderr 
        });
      }
    } catch (error) {
      logger.error('Error getting pnpm version:', error);
      res.status(500).json({ error: 'Failed to get pnpm version' });
    }
  });

  // POST /api/node/install-pnpm - Install pnpm
  router.post('/install-pnpm', async (req, res) => {
    try {
      const result = await runShellCommand("corepack enable pnpm && pnpm --version");
      
      if (result.success) {
        res.json({ 
          success: true,
          version: result.stdout.trim(),
          message: 'pnpm installed successfully' 
        });
      } else {
        // Try alternative installation method
        const fallbackResult = await runShellCommand("npm install -g pnpm@latest-10 && pnpm --version");
        
        if (fallbackResult.success) {
          res.json({ 
            success: true,
            version: fallbackResult.stdout.trim(),
            message: 'pnpm installed successfully via npm' 
          });
        } else {
          res.status(500).json({ 
            success: false,
            error: 'Failed to install pnpm',
            details: fallbackResult.stderr 
          });
        }
      }
    } catch (error) {
      logger.error('Error installing pnpm:', error);
      res.status(500).json({ error: 'Failed to install pnpm' });
    }
  });

  // GET /api/node/system-info - Get detailed system information
  router.get('/system-info', async (req, res) => {
    try {
      const systemInfo = {
        platform: platform(),
        arch: arch(),
        nodeVersion: null as string | null,
        npmVersion: null as string | null,
        pnpmVersion: null as string | null,
        yarnVersion: null as string | null,
      };

      // Check all package managers
      const checks = await Promise.allSettled([
        runShellCommand("node --version"),
        runShellCommand("npm --version"),
        runShellCommand("pnpm --version"),
        runShellCommand("yarn --version"),
      ]);

      if (checks[0].status === 'fulfilled' && checks[0].value.success) {
        systemInfo.nodeVersion = checks[0].value.stdout.trim();
      }
      if (checks[1].status === 'fulfilled' && checks[1].value.success) {
        systemInfo.npmVersion = checks[1].value.stdout.trim();
      }
      if (checks[2].status === 'fulfilled' && checks[2].value.success) {
        systemInfo.pnpmVersion = checks[2].value.stdout.trim();
      }
      if (checks[3].status === 'fulfilled' && checks[3].value.success) {
        systemInfo.yarnVersion = checks[3].value.stdout.trim();
      }

      res.json(systemInfo);
    } catch (error) {
      logger.error('Error getting system info:', error);
      res.status(500).json({ error: 'Failed to get system info' });
    }
  });

  return router;
}
