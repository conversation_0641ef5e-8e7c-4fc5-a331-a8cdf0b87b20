{"version": 3, "file": "security-agent.js", "sourceRoot": "", "sources": ["security-agent.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;GAaG;;;AAEH,mDAAuE;AACvE,iDAA4C;AAwyB5C;;GAEG;AACH,MAAa,aAAc,SAAQ,sBAAS;IAO1C,YAAY,SAA+B,EAAE;QAC3C,KAAK,CAAC,UAAU,EAAE;YAChB,YAAY,EAAE;gBACZ,oBAAoB,EAAE;oBACpB,wBAAwB;oBACxB,mBAAmB;oBACnB,qBAAqB;oBACrB,iBAAiB;oBACjB,qBAAqB;oBACrB,mBAAmB;oBACnB,iBAAiB;iBAClB;gBACD,cAAc,EAAE,CAAC,UAAU,EAAE,iBAAiB,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,CAAC;gBAChF,WAAW,EAAE,IAAI;gBACjB,kBAAkB,EAAE,IAAI;gBACxB,iBAAiB,EAAE,IAAI;gBACvB,cAAc,EAAE,IAAI;aACrB;YACD,GAAG,MAAM;SACV,CAAC,CAAA;QAzBa,kBAAa,GAAG;YAC/B,kBAAkB,EAAE,sBAAsB,EAAE,uBAAuB;YACnE,kBAAkB,EAAE,iBAAiB,EAAE,eAAe;YACtD,mBAAmB,EAAE,yBAAyB,EAAE,gBAAgB;SACjE,CAAA;IAsBD,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,eAAe,CAAC,KAAiB;QAC/C,MAAM,KAAK,GAAG,KAAK,CAAC,KAAsB,CAAA;QAE1C,OAAO,CAAC,GAAG,CAAC,kCAAkC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;QAEjE,sCAAsC;QACtC,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACxC,OAAO,CAAC,GAAG,CAAC,sBAAsB,MAAM,EAAE,CAAC,CAAA;YAE3C,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;YAEnE,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC1B,KAAK,CAAC,UAAU,GAAG,IAAI,CAAA;gBACvB,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAA;gBACpC,MAAK;YACP,CAAC;YAED,kBAAkB;YAClB,KAAK,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAA;YAC/B,KAAK,CAAC,QAAQ,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAA;YAErD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBAC1B,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,MAAM;gBACN,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,cAAc,GAAG,KAAK,CAAC,QAAQ,CAAC,UAAU;aACpE,CAAC,CAAA;QACJ,CAAC;QAED,uCAAuC;QACvC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;YACtB,KAAK,CAAC,SAAS,GAAG,IAAI,CAAA;YACtB,OAAO,CAAC,GAAG,CAAC,kCAAkC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAA;QACnE,CAAC;QAED,OAAO,KAAK,CAAA;IACd,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,MAAc,EAAE,KAAW;QAC9D,2CAA2C;QAC3C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAA;QAE3D,oCAAoC;QACpC,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,kBAAkB;gBACrB,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAA;YAChE,KAAK,sBAAsB;gBACzB,OAAO,MAAM,IAAI,CAAC,0BAA0B,CAAC,aAAa,CAAC,CAAA;YAC7D,KAAK,uBAAuB;gBAC1B,OAAO,MAAM,IAAI,CAAC,2BAA2B,CAAC,aAAa,CAAC,CAAA;YAC9D,KAAK,kBAAkB;gBACrB,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC,CAAA;YACzD,KAAK,iBAAiB;gBACpB,OAAO,MAAM,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAA;YACxD,KAAK,eAAe;gBAClB,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAA;YACtD,KAAK,mBAAmB;gBACtB,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAA;YAC1D,KAAK,yBAAyB;gBAC5B,OAAO,MAAM,IAAI,CAAC,4BAA4B,CAAC,aAAa,CAAC,CAAA;YAC/D,KAAK,gBAAgB;gBACnB,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAA;YACvD;gBACE,MAAM,IAAI,KAAK,CAAC,0BAA0B,MAAM,EAAE,CAAC,CAAA;QACvD,CAAC;IACH,CAAC;IAED;;OAEG;IACO,aAAa;QACrB,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAA;IAClC,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,wBAAwB;QACtC,OAAO;YACL,QAAQ,EAAE,0DAA0D;YACpE,eAAe,EAAE,yCAAyC;YAC1D,UAAU,EAAE,8CAA8C;YAC1D,cAAc,EAAE,6CAA6C;YAC7D,kBAAkB,EAAE,6CAA6C;YACjE,UAAU,EAAE,yCAAyC;SACtD,CAAA;IACH,CAAC;IAED,+EAA+E;IAC/E,4CAA4C;IAC5C,+EAA+E;IAEvE,KAAK,CAAC,sBAAsB,CAAC,KAAU,EAAE,KAAoB;QACnE,MAAM,QAAQ,GAAG,MAAM,sBAAS,CAAC,uBAAuB,CACtD,KAAK,CAAC,QAAQ,EACd,KAAK,CAAC,IAAI,CAAC,KAAK,CACjB,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,gBAAgB,GAAG,QAAQ,CAAA;QAE/C,OAAO;YACL,OAAO,EAAE,QAAQ;YACjB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,0BAA0B,CAAC,KAAU;QACjD,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,gBAAgB,CAAA;QAE7D,MAAM,eAAe,GAAG,MAAM,sBAAS,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAA;QAE7E,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,eAAe,GAAG,eAAe,CAAA;QAErD,OAAO;YACL,OAAO,EAAE,eAAe;YACxB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,2BAA2B,CAAC,KAAU;QAClD,MAAM,cAAc,GAAG,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,cAAc,CAAA;QAEvD,MAAM,UAAU,GAAG,MAAM,sBAAS,CAAC,4BAA4B,CAAC,cAAc,CAAC,CAAA;QAE/E,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,wBAAwB,GAAG,UAAU,CAAA;QAEzD,OAAO;YACL,OAAO,EAAE,UAAU;YACnB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAAC,KAAU;QAC7C,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,gBAAgB,CAAA;QAC7D,MAAM,wBAAwB,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,wBAAwB,CAAA;QAE7E,MAAM,UAAU,GAAG,MAAM,sBAAS,CAAC,uBAAuB,CACxD,gBAAgB,EAChB,wBAAwB,EACxB,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,YAAY,CAAC,SAAS,CACzC,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,UAAU,GAAG,UAAU,CAAA;QAE3C,OAAO;YACL,OAAO,EAAE,UAAU;YACnB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,KAAU;QAC5C,MAAM,WAAW,GAAG;YAClB,QAAQ,EAAE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,gBAAgB;YAC9C,cAAc,EAAE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,wBAAwB;YAC5D,eAAe,EAAE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,eAAe;SACrD,CAAA;QAED,MAAM,OAAO,GAAG,MAAM,sBAAS,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAA;QAE/D,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAA;QAErC,OAAO;YACL,OAAO,EAAE,OAAO;YAChB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,KAAU;QAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,OAAO,CAAA;QAE3C,MAAM,OAAO,GAAG,MAAM,sBAAS,CAAC,sBAAsB,CACpD,OAAO,EACP,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CACvC,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,OAAO,GAAG,OAAO,CAAA;QAErC,OAAO;YACL,OAAO,EAAE,OAAO;YAChB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,KAAU;QAC9C,MAAM,UAAU,GAAG;YACjB,eAAe,EAAE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,eAAe;YACpD,UAAU,EAAE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,UAAU;YAC1C,OAAO,EAAE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,OAAO;YACpC,OAAO,EAAE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,OAAO;SACrC,CAAA;QAED,MAAM,QAAQ,GAAG,MAAM,sBAAS,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAA;QAErE,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAA;QAEvC,OAAO;YACL,OAAO,EAAE,QAAQ;YACjB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,4BAA4B,CAAC,KAAU;QACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,QAAQ,CAAA;QAE7C,MAAM,WAAW,GAAG,MAAM,sBAAS,CAAC,6BAA6B,CAAC,QAAQ,CAAC,CAAA;QAE3E,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,WAAW,GAAG,WAAW,CAAA;QAE7C,OAAO;YACL,OAAO,EAAE,WAAW;YACpB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,KAAK;SACjB,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,KAAU;QAC3C,MAAM,WAAW,GAAG,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,WAAW,CAAA;QAEnD,MAAM,UAAU,GAAG,MAAM,sBAAS,CAAC,qBAAqB,CACtD,WAAW,EACX,IAAI,CAAC,KAAM,CAAC,KAAK,CAAC,IAAI,CACvB,CAAA;QAED,IAAI,CAAC,KAAM,CAAC,OAAO,CAAC,UAAU,GAAG,UAAU,CAAA;QAE3C,OAAO;YACL,OAAO,EAAE,UAAU;YACnB,UAAU,EAAE,KAAK;YACjB,SAAS,EAAE,IAAI,CAAC,aAAa;SAC9B,CAAA;IACH,CAAC;CACF;AAhRD,sCAgRC;AAGyB,gCAAO"}