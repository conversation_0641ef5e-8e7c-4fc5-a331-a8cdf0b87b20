/**
 * AG3NT Framework - User Interaction Agent
 *
 * Specialized agent for interfacing with users, collecting feedback,
 * and providing explanations and clarifications.
 *
 * Features:
 * - Natural language interaction
 * - User feedback collection
 * - Explanation generation
 * - Clarification requests
 * - User preference learning
 * - Multi-modal communication
 */
import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent";
export interface UserInteractionInput {
    task: InteractionTask;
    user: UserProfile;
    context: InteractionContext;
    requirements: InteractionRequirements;
}
export interface InteractionTask {
    taskId: string;
    type: 'conversation' | 'feedback' | 'explanation' | 'clarification' | 'guidance' | 'support';
    title: string;
    description: string;
    priority: 'critical' | 'high' | 'medium' | 'low';
    scope: InteractionScope;
    deadline?: string;
}
export interface InteractionScope {
    topics: string[];
    agents: string[];
    systems: string[];
    modalities: string[];
    includeHistory: boolean;
    includeContext: boolean;
    includePreferences: boolean;
}
export interface UserProfile {
    userId: string;
    name: string;
    role: string;
    experience: ExperienceLevel;
    preferences: UserPreferences;
    history: InteractionHistory;
    context: UserContext;
}
export interface ExperienceLevel {
    overall: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    technical: 'low' | 'medium' | 'high';
    domain: 'novice' | 'familiar' | 'experienced' | 'expert';
    tools: ToolExperience[];
}
export interface ToolExperience {
    tool: string;
    level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    usage: number;
    lastUsed: string;
}
export interface UserPreferences {
    communication: CommunicationPreferences;
    interface: InterfacePreferences;
    content: ContentPreferences;
    assistance: AssistancePreferences;
}
export interface CommunicationPreferences {
    style: 'formal' | 'casual' | 'technical' | 'friendly';
    verbosity: 'concise' | 'detailed' | 'comprehensive';
    language: string;
    tone: 'professional' | 'conversational' | 'instructional';
    examples: boolean;
}
export interface InterfacePreferences {
    modality: 'text' | 'voice' | 'visual' | 'multimodal';
    layout: 'simple' | 'detailed' | 'dashboard';
    notifications: NotificationPreferences;
    accessibility: AccessibilityPreferences;
}
export interface NotificationPreferences {
    enabled: boolean;
    frequency: 'immediate' | 'batched' | 'scheduled';
    channels: string[];
    types: string[];
}
export interface AccessibilityPreferences {
    screenReader: boolean;
    highContrast: boolean;
    largeText: boolean;
    keyboardNavigation: boolean;
    voiceControl: boolean;
}
export interface ContentPreferences {
    depth: 'overview' | 'detailed' | 'comprehensive';
    format: 'text' | 'visual' | 'interactive' | 'mixed';
    examples: boolean;
    tutorials: boolean;
    references: boolean;
}
export interface AssistancePreferences {
    proactivity: 'reactive' | 'suggestive' | 'proactive';
    automation: 'manual' | 'semi_auto' | 'automatic';
    confirmation: 'always' | 'important' | 'never';
    explanation: 'minimal' | 'standard' | 'detailed';
}
export interface InteractionHistory {
    sessions: InteractionSession[];
    patterns: InteractionPattern[];
    feedback: FeedbackHistory[];
    preferences: PreferenceHistory[];
}
export interface InteractionSession {
    sessionId: string;
    startTime: string;
    endTime: string;
    duration: number;
    interactions: Interaction[];
    outcome: SessionOutcome;
    satisfaction: number;
}
export interface Interaction {
    id: string;
    timestamp: string;
    type: 'question' | 'answer' | 'request' | 'response' | 'feedback' | 'clarification';
    content: InteractionContent;
    context: any;
    metadata: InteractionMetadata;
}
export interface InteractionContent {
    text?: string;
    voice?: VoiceContent;
    visual?: VisualContent;
    structured?: StructuredContent;
}
export interface VoiceContent {
    audio: string;
    transcript: string;
    language: string;
    confidence: number;
}
export interface VisualContent {
    images: ImageContent[];
    diagrams: DiagramContent[];
    charts: ChartContent[];
    videos: VideoContent[];
}
export interface ImageContent {
    url: string;
    caption: string;
    alt: string;
    metadata: any;
}
export interface DiagramContent {
    type: string;
    data: any;
    description: string;
    interactive: boolean;
}
export interface ChartContent {
    type: string;
    data: any;
    title: string;
    description: string;
}
export interface VideoContent {
    url: string;
    title: string;
    description: string;
    duration: number;
}
export interface StructuredContent {
    type: string;
    data: any;
    schema: any;
    presentation: any;
}
export interface InteractionMetadata {
    agent: string;
    confidence: number;
    processing: ProcessingMetadata;
    quality: QualityMetadata;
}
export interface ProcessingMetadata {
    duration: number;
    tokens: number;
    model: string;
    temperature: number;
}
export interface QualityMetadata {
    relevance: number;
    accuracy: number;
    completeness: number;
    clarity: number;
}
export interface SessionOutcome {
    status: 'completed' | 'abandoned' | 'escalated' | 'deferred';
    goals: GoalOutcome[];
    issues: IssueOutcome[];
    satisfaction: number;
    feedback: string;
}
export interface GoalOutcome {
    goal: string;
    achieved: boolean;
    progress: number;
    obstacles: string[];
}
export interface IssueOutcome {
    issue: string;
    resolved: boolean;
    resolution: string;
    escalated: boolean;
}
export interface InteractionPattern {
    type: string;
    frequency: number;
    context: string[];
    triggers: string[];
    outcomes: string[];
}
export interface FeedbackHistory {
    timestamp: string;
    type: 'rating' | 'comment' | 'suggestion' | 'complaint';
    content: string;
    rating?: number;
    category: string;
    resolved: boolean;
}
export interface PreferenceHistory {
    timestamp: string;
    preference: string;
    oldValue: any;
    newValue: any;
    reason: string;
    source: 'explicit' | 'inferred' | 'learned';
}
export interface UserContext {
    current: CurrentContext;
    environment: EnvironmentContext;
    task: TaskContext;
    social: SocialContext;
}
export interface CurrentContext {
    location: string;
    time: string;
    device: string;
    application: string;
    activity: string;
}
export interface EnvironmentContext {
    workspace: string;
    project: string;
    team: string[];
    tools: string[];
    constraints: string[];
}
export interface TaskContext {
    currentTask: string;
    goals: string[];
    progress: number;
    blockers: string[];
    deadline?: string;
}
export interface SocialContext {
    collaborators: string[];
    stakeholders: string[];
    communication: string[];
    permissions: string[];
}
export interface InteractionContext {
    session: SessionContext;
    conversation: ConversationContext;
    system: SystemContext;
    domain: DomainContext;
}
export interface SessionContext {
    sessionId: string;
    startTime: string;
    duration: number;
    interactions: number;
    mode: 'guided' | 'exploratory' | 'task_focused';
}
export interface ConversationContext {
    topic: string;
    intent: string;
    entities: Entity[];
    sentiment: SentimentAnalysis;
    history: ConversationTurn[];
}
export interface Entity {
    type: string;
    value: string;
    confidence: number;
    context: string;
}
export interface SentimentAnalysis {
    polarity: 'positive' | 'negative' | 'neutral';
    confidence: number;
    emotions: Emotion[];
}
export interface Emotion {
    type: string;
    intensity: number;
    confidence: number;
}
export interface ConversationTurn {
    speaker: 'user' | 'agent';
    content: string;
    intent: string;
    entities: Entity[];
    timestamp: string;
}
export interface SystemContext {
    agents: AgentContext[];
    services: ServiceContext[];
    resources: ResourceContext;
    status: SystemStatus;
}
export interface AgentContext {
    agentId: string;
    status: string;
    capabilities: string[];
    load: number;
    availability: boolean;
}
export interface ServiceContext {
    name: string;
    status: string;
    performance: number;
    errors: number;
}
export interface ResourceContext {
    cpu: number;
    memory: number;
    storage: number;
    network: number;
}
export interface SystemStatus {
    health: 'healthy' | 'degraded' | 'unhealthy';
    performance: number;
    availability: number;
    issues: string[];
}
export interface DomainContext {
    domain: string;
    concepts: DomainConcept[];
    relationships: DomainRelationship[];
    rules: DomainRule[];
}
export interface DomainConcept {
    name: string;
    definition: string;
    examples: string[];
    related: string[];
}
export interface DomainRelationship {
    from: string;
    to: string;
    type: string;
    strength: number;
}
export interface DomainRule {
    name: string;
    condition: string;
    action: string;
    priority: number;
}
export interface InteractionRequirements {
    communication: CommunicationRequirements;
    personalization: PersonalizationRequirements;
    accessibility: AccessibilityRequirements;
    quality: QualityRequirements;
}
export interface CommunicationRequirements {
    modalities: string[];
    languages: string[];
    realTime: boolean;
    multiTurn: boolean;
    contextAware: boolean;
}
export interface PersonalizationRequirements {
    adaptive: boolean;
    learning: boolean;
    preferences: boolean;
    history: boolean;
    prediction: boolean;
}
export interface AccessibilityRequirements {
    wcag: string;
    screenReader: boolean;
    keyboard: boolean;
    voice: boolean;
    visual: boolean;
}
export interface QualityRequirements {
    accuracy: number;
    relevance: number;
    completeness: number;
    timeliness: number;
    satisfaction: number;
}
export interface UserInteractionResult {
    taskId: string;
    status: 'completed' | 'failed' | 'partial';
    interactions: ProcessedInteraction[];
    insights: UserInsight[];
    recommendations: UserRecommendation[];
    improvements: ImprovementSuggestion[];
    metrics: InteractionMetrics;
}
export interface ProcessedInteraction {
    id: string;
    type: string;
    content: any;
    response: InteractionResponse;
    quality: InteractionQuality;
    learning: LearningOutcome[];
}
export interface InteractionResponse {
    content: any;
    confidence: number;
    alternatives: Alternative[];
    explanation: string;
    followUp: FollowUpSuggestion[];
}
export interface Alternative {
    content: any;
    confidence: number;
    rationale: string;
}
export interface FollowUpSuggestion {
    type: string;
    content: string;
    priority: number;
}
export interface InteractionQuality {
    relevance: number;
    accuracy: number;
    completeness: number;
    clarity: number;
    satisfaction: number;
}
export interface LearningOutcome {
    type: string;
    insight: string;
    confidence: number;
    application: string;
}
export interface UserInsight {
    type: 'preference' | 'behavior' | 'need' | 'goal' | 'challenge';
    description: string;
    evidence: string[];
    confidence: number;
    implications: string[];
}
export interface UserRecommendation {
    type: 'feature' | 'workflow' | 'training' | 'tool' | 'process';
    description: string;
    rationale: string;
    benefit: string;
    effort: 'low' | 'medium' | 'high';
    priority: 'high' | 'medium' | 'low';
}
export interface ImprovementSuggestion {
    area: string;
    current: string;
    suggested: string;
    impact: string;
    implementation: string;
}
export interface InteractionMetrics {
    satisfaction: number;
    efficiency: number;
    effectiveness: number;
    engagement: number;
    learning: number;
    retention: number;
}
/**
 * User Interaction Agent - Natural language user interface
 */
export declare class UserInteractionAgent extends BaseAgent {
    private readonly interactionSteps;
    constructor(config?: Partial<AgentConfig>);
    /**
     * Execute user interaction workflow
     */
    protected executeWorkflow(state: AgentState): Promise<AgentState>;
    /**
     * Execute individual interaction step with context enhancement
     */
    private executeStepWithContext;
    /**
     * Get total steps for progress tracking
     */
    protected getTotalSteps(): number;
    /**
     * Get relevant documentation for user interaction
     */
    protected getRelevantDocumentation(): Promise<Record<string, any>>;
    private analyzeUserWithMCP;
    private understandIntentWithMCP;
    private generateResponseWithMCP;
    private personalizeContentWithMCP;
    private collectFeedbackWithMCP;
    private learnPreferencesWithMCP;
    private provideExplanationsWithMCP;
    private suggestImprovementsWithMCP;
    private updateProfileWithMCP;
}
export { UserInteractionAgent as default };
//# sourceMappingURL=user-interaction-agent.d.ts.map