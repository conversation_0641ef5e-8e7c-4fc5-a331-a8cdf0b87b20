@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    DYAD INTEGRATED STARTUP SCRIPT
echo ========================================
echo.
echo This script will start:
echo 1. Dyad Web Server (Backend API)
echo 2. AI Dev Ecosystem (Frontend UI)
echo.

REM Check if we're in the right directory
if not exist "package.json" (
    echo Error: package.json not found. Please run this script from the dyad root directory.
    pause
    exit /b 1
)

if not exist "ai-dev-ecosystem\package.json" (
    echo Error: ai-dev-ecosystem directory not found. Please ensure the frontend is in the ai-dev-ecosystem folder.
    pause
    exit /b 1
)

echo Checking dependencies...
echo.

REM Check if node_modules exists in root
if not exist "node_modules" (
    echo Installing backend dependencies...
    npm install
    if errorlevel 1 (
        echo Failed to install backend dependencies
        pause
        exit /b 1
    )
)

REM Check if node_modules exists in frontend
if not exist "ai-dev-ecosystem\node_modules" (
    echo Installing frontend dependencies...
    cd ai-dev-ecosystem
    npm install --legacy-peer-deps
    if errorlevel 1 (
        echo Failed to install frontend dependencies
        pause
        exit /b 1
    )
    cd ..
)

echo All dependencies installed!
echo.

echo Starting Dyad Web Server (Backend)...
echo Backend will run on http://localhost:3002
echo.

REM Start backend in new window
start "Dyad Web Server" cmd /k "echo Starting Dyad Web Server... && npm run web-server || (echo Web server failed to start && pause)"

echo Waiting 5 seconds for backend to initialize...
timeout /t 5 /nobreak >nul

echo.
echo Starting AI Dev Ecosystem (Frontend)...
echo Frontend will run on http://localhost:3000
echo.

REM Start frontend in new window
start "AI Dev Ecosystem" cmd /k "echo Starting AI Dev Ecosystem Frontend... && cd ai-dev-ecosystem && npm run dev || (echo Frontend failed to start && pause)"

echo.
echo ========================================
echo    DYAD INTEGRATION STARTED!
echo ========================================
echo.
echo Services starting:
echo - Backend API: http://localhost:3002
echo - Frontend UI: http://localhost:3000
echo.
echo Wait a few moments for both services to start, then:
echo 1. Open http://localhost:3000 in your browser
echo 2. Test the integrated Dyad functionality
echo.
echo Features to test:
echo - Create and manage apps
echo - Real-time app status updates
echo - File browser and code editor
echo - AI chat with streaming responses
echo - OpenRouter model selection
echo.
echo Press any key to exit this window...
pause >nul
