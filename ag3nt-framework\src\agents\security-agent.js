"use strict";
/**
 * AG3NT Framework - Security Agent
 *
 * Specialized agent for security scanning, vulnerability assessment,
 * and secure coding compliance.
 *
 * Features:
 * - Vulnerability scanning and assessment
 * - Security code analysis
 * - Compliance checking
 * - Threat modeling
 * - Security policy enforcement
 * - Penetration testing automation
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = exports.SecurityAgent = void 0;
const base_agent_1 = require("../core/base-agent");
const ai_service_1 = require("../../ai-service");
/**
 * Security Agent - Comprehensive security analysis and compliance
 */
class SecurityAgent extends base_agent_1.BaseAgent {
    constructor(config = {}) {
        super('security', {
            capabilities: {
                requiredCapabilities: [
                    'vulnerability_scanning',
                    'security_analysis',
                    'compliance_checking',
                    'threat_modeling',
                    'penetration_testing',
                    'security_auditing',
                    'risk_assessment'
                ],
                contextFilters: ['security', 'vulnerabilities', 'compliance', 'threats', 'code'],
                mcpEnhanced: true,
                sequentialThinking: true,
                contextEnrichment: true,
                ragIntegration: true
            },
            ...config
        });
        this.securitySteps = [
            'analyze_codebase', 'scan_vulnerabilities', 'assess_infrastructure',
            'check_compliance', 'analyze_threats', 'test_security',
            'generate_findings', 'create_remediation_plan', 'validate_fixes'
        ];
    }
    /**
     * Execute security workflow
     */
    async executeWorkflow(state) {
        const input = state.input;
        console.log(`🔒 Starting security analysis: ${input.task.title}`);
        // Execute security steps sequentially
        for (const stepId of this.securitySteps) {
            console.log(`🔄 Executing step: ${stepId}`);
            const stepResult = await this.executeStepWithContext(stepId, input);
            if (stepResult.needsInput) {
                state.needsInput = true;
                state.question = stepResult.question;
                break;
            }
            // Update progress
            state.metadata.completedSteps++;
            state.metadata.lastUpdated = new Date().toISOString();
            this.emit('step_completed', {
                agentId: this.agentId,
                stepId,
                progress: state.metadata.completedSteps / state.metadata.totalSteps
            });
        }
        // Mark as completed if no input needed
        if (!state.needsInput) {
            state.completed = true;
            console.log(`✅ Security analysis completed: ${input.task.title}`);
        }
        return state;
    }
    /**
     * Execute individual security step with context enhancement
     */
    async executeStepWithContext(stepId, input) {
        // Get enhanced context from context engine
        const enhancedState = await this.getEnhancedContext(stepId);
        // Execute step with MCP enhancement
        switch (stepId) {
            case 'analyze_codebase':
                return await this.analyzeCodebaseWithMCP(enhancedState, input);
            case 'scan_vulnerabilities':
                return await this.scanVulnerabilitiesWithMCP(enhancedState);
            case 'assess_infrastructure':
                return await this.assessInfrastructureWithMCP(enhancedState);
            case 'check_compliance':
                return await this.checkComplianceWithMCP(enhancedState);
            case 'analyze_threats':
                return await this.analyzeThreatsWithMCP(enhancedState);
            case 'test_security':
                return await this.testSecurityWithMCP(enhancedState);
            case 'generate_findings':
                return await this.generateFindingsWithMCP(enhancedState);
            case 'create_remediation_plan':
                return await this.createRemediationPlanWithMCP(enhancedState);
            case 'validate_fixes':
                return await this.validateFixesWithMCP(enhancedState);
            default:
                throw new Error(`Unknown security step: ${stepId}`);
        }
    }
    /**
     * Get total steps for progress tracking
     */
    getTotalSteps() {
        return this.securitySteps.length;
    }
    /**
     * Get relevant documentation for security
     */
    async getRelevantDocumentation() {
        return {
            security: 'Application security best practices and OWASP guidelines',
            vulnerabilities: 'Vulnerability assessment and management',
            compliance: 'Security compliance frameworks and standards',
            threatModeling: 'Threat modeling methodologies and practices',
            penetrationTesting: 'Penetration testing and security validation',
            secureCode: 'Secure coding practices and code review'
        };
    }
    // ============================================================================
    // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
    // ============================================================================
    async analyzeCodebaseWithMCP(state, input) {
        const analysis = await ai_service_1.aiService.analyzeCodebaseSecurity(input.codebase, input.task.scope);
        this.state.results.codebaseAnalysis = analysis;
        return {
            results: analysis,
            needsInput: false,
            completed: false
        };
    }
    async scanVulnerabilitiesWithMCP(state) {
        const codebaseAnalysis = this.state.results.codebaseAnalysis;
        const vulnerabilities = await ai_service_1.aiService.scanVulnerabilities(codebaseAnalysis);
        this.state.results.vulnerabilities = vulnerabilities;
        return {
            results: vulnerabilities,
            needsInput: false,
            completed: false
        };
    }
    async assessInfrastructureWithMCP(state) {
        const infrastructure = this.state.input.infrastructure;
        const assessment = await ai_service_1.aiService.assessInfrastructureSecurity(infrastructure);
        this.state.results.infrastructureAssessment = assessment;
        return {
            results: assessment,
            needsInput: false,
            completed: false
        };
    }
    async checkComplianceWithMCP(state) {
        const codebaseAnalysis = this.state.results.codebaseAnalysis;
        const infrastructureAssessment = this.state.results.infrastructureAssessment;
        const compliance = await ai_service_1.aiService.checkSecurityCompliance(codebaseAnalysis, infrastructureAssessment, this.state.input.requirements.standards);
        this.state.results.compliance = compliance;
        return {
            results: compliance,
            needsInput: false,
            completed: false
        };
    }
    async analyzeThreatsWithMCP(state) {
        const allAnalysis = {
            codebase: this.state.results.codebaseAnalysis,
            infrastructure: this.state.results.infrastructureAssessment,
            vulnerabilities: this.state.results.vulnerabilities
        };
        const threats = await ai_service_1.aiService.analyzeThreatModel(allAnalysis);
        this.state.results.threats = threats;
        return {
            results: threats,
            needsInput: false,
            completed: false
        };
    }
    async testSecurityWithMCP(state) {
        const threats = this.state.results.threats;
        const testing = await ai_service_1.aiService.performSecurityTesting(threats, this.state.input.requirements.testing);
        this.state.results.testing = testing;
        return {
            results: testing,
            needsInput: false,
            completed: false
        };
    }
    async generateFindingsWithMCP(state) {
        const allResults = {
            vulnerabilities: this.state.results.vulnerabilities,
            compliance: this.state.results.compliance,
            threats: this.state.results.threats,
            testing: this.state.results.testing
        };
        const findings = await ai_service_1.aiService.generateSecurityFindings(allResults);
        this.state.results.findings = findings;
        return {
            results: findings,
            needsInput: false,
            completed: false
        };
    }
    async createRemediationPlanWithMCP(state) {
        const findings = this.state.results.findings;
        const remediation = await ai_service_1.aiService.createSecurityRemediationPlan(findings);
        this.state.results.remediation = remediation;
        return {
            results: remediation,
            needsInput: false,
            completed: false
        };
    }
    async validateFixesWithMCP(state) {
        const remediation = this.state.results.remediation;
        const validation = await ai_service_1.aiService.validateSecurityFixes(remediation, this.state.input.task);
        this.state.results.validation = validation;
        return {
            results: validation,
            needsInput: false,
            completed: true // Final step
        };
    }
}
exports.SecurityAgent = SecurityAgent;
exports.default = SecurityAgent;
//# sourceMappingURL=security-agent.js.map