@echo off
echo ========================================
echo    Starting Dyad Frontend + Backend
echo ========================================
echo.

REM Check current directory
echo Current directory: %CD%
echo.

REM Check if Node.js is installed
echo Checking Node.js...
node --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    echo.
    echo Press any key to exit...
    pause
    exit /b 1
)

REM Check if npm is installed
echo Checking npm...
npm --version
if %errorlevel% neq 0 (
    echo ERROR: npm is not installed or not in PATH
    echo.
    echo Press any key to exit...
    pause
    exit /b 1
)

echo Node.js and npm are available
echo.

REM Set window title
title Dyad Launcher

REM Check if package.json exists in current directory
if not exist "package.json" (
    echo ERROR: package.json not found in current directory
    echo Make sure you're running this script from the dyad-main folder
    echo Current directory: %CD%
    echo.
    echo Press any key to exit...
    pause
    exit /b 1
)

REM Check if ai-dev-ecosystem folder exists
if not exist "ai-dev-ecosystem" (
    echo ERROR: ai-dev-ecosystem folder not found
    echo Make sure the ai-dev-ecosystem folder exists in: %CD%
    echo.
    echo Press any key to exit...
    pause
    exit /b 1
)

REM Check if ai-dev-ecosystem has package.json
if not exist "ai-dev-ecosystem\package.json" (
    echo ERROR: package.json not found in ai-dev-ecosystem folder
    echo.
    echo Press any key to exit...
    pause
    exit /b 1
)

echo All required files found
echo.

echo Starting Dyad Main App...
echo Main app will run in a new window
echo.

REM Start main Dyad app in new window
start "Dyad Main App" cmd /k "echo Starting Dyad Main App... && echo Current dir: %CD% && npm start || (echo Main app failed to start && pause)"

echo Waiting 3 seconds for main app to initialize...
timeout /t 3 /nobreak >nul

echo.
echo Starting Dyad Web Server (API)...
echo Web server will run in a new window
echo.

REM Start Dyad web server in new window
start "Dyad Web Server" cmd /k "echo Starting Dyad Web Server... && echo Current dir: %CD% && npm run web-server || (echo Web server failed to start && pause)"

echo Waiting 3 seconds for web server to initialize...
timeout /t 3 /nobreak >nul

echo.
echo Starting Frontend (AI Dev Ecosystem)...
echo Frontend will run in a new window
echo.

REM Start frontend in new window with better error handling
start "Dyad Frontend" cmd /k "echo Starting Dyad Frontend... && cd ai-dev-ecosystem && echo Current dir: %CD% && npm run dev || (echo Frontend failed to start && pause)"

echo.
echo ========================================
echo    All services are starting up!
echo ========================================
echo.
echo Dyad Main App: Starting in separate window
echo Dyad Web Server: Starting in separate window (http://localhost:3002)
echo Frontend (Web UI): Starting in separate window (http://localhost:3001)
echo.
echo Wait a few moments for all services to fully start.
echo The frontend will be available at: http://localhost:3001
echo The Dyad API will be available at: http://localhost:3002
echo.
echo To stop the services:
echo - Close the Dyad Main App window
echo - Close the Dyad Web Server window
echo - Close the Frontend window
echo.
echo This window will stay open for monitoring.
echo Press any key to exit this launcher...
pause
