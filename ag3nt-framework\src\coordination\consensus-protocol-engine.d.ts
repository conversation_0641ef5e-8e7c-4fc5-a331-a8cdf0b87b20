/**
 * AG3NT Framework - Consensus Protocol Engine
 *
 * Democratic decision-making mechanisms with voting, quorum, and conflict resolution
 * for sophisticated multi-agent coordination.
 */
import { EventEmitter } from "events";
export interface ConsensusConfig {
    defaultProtocol: 'majority' | 'supermajority' | 'unanimous' | 'weighted' | 'byzantine';
    quorumThreshold: number;
    votingTimeout: number;
    enableDelegatedVoting: boolean;
    enableVetoRights: boolean;
    conflictResolutionStrategy: 'escalation' | 'mediation' | 'fallback' | 'random';
}
export interface ConsensusProposal {
    proposalId: string;
    proposerId: string;
    title: string;
    description: string;
    type: 'decision' | 'resource_allocation' | 'task_assignment' | 'policy_change' | 'emergency';
    options: ProposalOption[];
    votingRules: VotingRules;
    context: ProposalContext;
    status: 'draft' | 'active' | 'completed' | 'failed' | 'cancelled';
    createdAt: number;
    votingDeadline: number;
    result?: ConsensusResult;
}
export interface ProposalOption {
    optionId: string;
    title: string;
    description: string;
    impact: OptionImpact;
    cost: number;
    risk: 'low' | 'medium' | 'high';
    feasibility: number;
    supportingData: any;
}
export interface OptionImpact {
    scope: string[];
    magnitude: 'minor' | 'moderate' | 'major' | 'critical';
    reversibility: boolean;
    timeframe: string;
    dependencies: string[];
}
export interface VotingRules {
    protocol: 'majority' | 'supermajority' | 'unanimous' | 'weighted' | 'byzantine';
    quorum: number;
    allowAbstention: boolean;
    allowDelegation: boolean;
    weightingCriteria?: WeightingCriteria;
    vetoRights: VetoRight[];
    tieBreakingMethod: 'random' | 'proposer_decides' | 'escalate' | 'status_quo';
}
export interface WeightingCriteria {
    authorityWeight: number;
    expertiseWeight: number;
    stakeWeight: number;
    trustWeight: number;
    experienceWeight: number;
}
export interface VetoRight {
    agentId: string;
    scope: string[];
    justificationRequired: boolean;
    appealable: boolean;
}
export interface ProposalContext {
    urgency: 'low' | 'medium' | 'high' | 'critical';
    stakeholders: string[];
    affectedSystems: string[];
    prerequisites: string[];
    constraints: any;
    relatedProposals: string[];
}
export interface AgentVote {
    voteId: string;
    agentId: string;
    proposalId: string;
    optionId: string;
    voteType: 'support' | 'oppose' | 'abstain' | 'veto';
    weight: number;
    confidence: number;
    reasoning: string;
    conditions?: VoteCondition[];
    timestamp: number;
    delegatedFrom?: string;
}
export interface VoteCondition {
    type: 'conditional' | 'contingent' | 'time_limited';
    condition: string;
    expiresAt?: number;
}
export interface ConsensusResult {
    decision: string;
    winningOption: string;
    votingSummary: VotingSummary;
    consensusLevel: number;
    dissent: DissentAnalysis;
    implementation: ImplementationPlan;
    appeals: Appeal[];
}
export interface VotingSummary {
    totalEligibleVoters: number;
    totalVotes: number;
    participationRate: number;
    optionResults: OptionResult[];
    vetoesReceived: number;
    delegatedVotes: number;
}
export interface OptionResult {
    optionId: string;
    votes: number;
    weightedVotes: number;
    percentage: number;
    support: AgentVote[];
}
export interface DissentAnalysis {
    dissentLevel: number;
    majorConcerns: string[];
    dissentingAgents: string[];
    compromisePossible: boolean;
    recommendedActions: string[];
}
export interface ImplementationPlan {
    phases: ImplementationPhase[];
    timeline: string;
    responsibilities: AgentResponsibility[];
    successMetrics: SuccessMetric[];
    rollbackPlan: RollbackPlan;
}
export interface ImplementationPhase {
    phaseId: string;
    name: string;
    description: string;
    startConditions: string[];
    deliverables: string[];
    duration: number;
    dependencies: string[];
}
export interface AgentResponsibility {
    agentId: string;
    role: string;
    tasks: string[];
    authority: string[];
    accountability: string[];
}
export interface SuccessMetric {
    name: string;
    target: number;
    measurement: string;
    timeline: string;
}
export interface RollbackPlan {
    triggers: string[];
    steps: string[];
    timeframe: number;
    approvalRequired: boolean;
}
export interface Appeal {
    appealId: string;
    appealerId: string;
    grounds: string;
    evidence: any;
    status: 'pending' | 'accepted' | 'rejected';
    resolution?: string;
}
export interface ConsensusMetrics {
    totalProposals: number;
    successRate: number;
    averageConsensusTime: number;
    participationRate: number;
    conflictRate: number;
    appealRate: number;
    topContributors: AgentContribution[];
}
export interface AgentContribution {
    agentId: string;
    proposalsSubmitted: number;
    votesParticipated: number;
    consensusContribution: number;
    conflictResolutions: number;
}
/**
 * Consensus Protocol Engine
 */
export declare class ConsensusProtocolEngine extends EventEmitter {
    private config;
    private activeProposals;
    private proposalHistory;
    private agentVotingRights;
    private consensusMetrics;
    constructor(config?: Partial<ConsensusConfig>);
    /**
     * Register agent with voting rights
     */
    registerVoter(agentId: string, rights: Partial<VotingRights>): void;
    /**
     * Submit proposal for consensus
     */
    submitProposal(proposal: Partial<ConsensusProposal>): Promise<ConsensusProposal>;
    /**
     * Cast vote on proposal
     */
    castVote(vote: Partial<AgentVote>): Promise<AgentVote>;
    /**
     * Delegate voting rights
     */
    delegateVote(fromAgent: string, toAgent: string, proposalId: string, conditions?: any): Promise<void>;
    /**
     * Exercise veto right
     */
    exerciseVeto(agentId: string, proposalId: string, justification: string): Promise<void>;
    /**
     * Get consensus metrics
     */
    getConsensusMetrics(): ConsensusMetrics;
    /**
     * Private helper methods
     */
    private validateProposal;
    private getDefaultVotingRules;
    private getDefaultContext;
    private notifyEligibleVoters;
    private startVotingTimeout;
    private calculateVoteWeight;
    private checkConsensusReached;
    private calculateVotingSummary;
    private finalizeConsensus;
    private analyzeDissent;
    private createImplementationPlan;
    private moveToHistory;
    private updateMetrics;
    private initializeMetrics;
}
interface VotingRights {
    agentId: string;
    weight: number;
    expertise: string[];
    authority: number;
    canPropose: boolean;
    canVeto: boolean;
    canDelegate: boolean;
    trustScore: number;
}
export default ConsensusProtocolEngine;
//# sourceMappingURL=consensus-protocol-engine.d.ts.map