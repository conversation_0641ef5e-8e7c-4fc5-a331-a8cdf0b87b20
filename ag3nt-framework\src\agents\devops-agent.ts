/**
 * AG3NT Framework - DevOps Agent
 * 
 * Specialized agent for CI/CD pipeline tasks, deployment automation,
 * and environment configuration management.
 * 
 * Features:
 * - CI/CD pipeline creation and management
 * - Deployment automation
 * - Environment configuration
 * - Infrastructure as Code
 * - Monitoring and alerting setup
 * - Container orchestration
 */

import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent"
import { aiService } from "../../ai-service"

export interface DevOpsInput {
  task: DevOpsTask
  project: ProjectInfo
  infrastructure: InfrastructureInfo
  requirements: DevOpsRequirements
}

export interface DevOpsTask {
  taskId: string
  type: 'pipeline' | 'deployment' | 'infrastructure' | 'monitoring' | 'security' | 'optimization'
  title: string
  description: string
  priority: 'critical' | 'high' | 'medium' | 'low'
  scope: DevO<PERSON>Scope
  deadline?: string
}

export interface DevOpsScope {
  environments: string[]
  services: string[]
  regions: string[]
  platforms: string[]
  includeMonitoring: boolean
  includeSecurity: boolean
  includeBackup: boolean
}

export interface ProjectInfo {
  name: string
  type: 'web_app' | 'api' | 'microservices' | 'mobile_app' | 'desktop_app'
  language: string
  framework: string
  buildTool: string
  testFramework: string
  dependencies: ProjectDependency[]
  structure: ProjectStructure
}

export interface ProjectDependency {
  name: string
  version: string
  type: 'runtime' | 'build' | 'test'
  manager: 'npm' | 'yarn' | 'pip' | 'maven' | 'gradle' | 'cargo'
}

export interface ProjectStructure {
  sourceDir: string
  buildDir: string
  testDir: string
  configDir: string
  dockerFile?: string
  buildScript?: string
}

export interface InfrastructureInfo {
  current: CurrentInfrastructure
  target: TargetInfrastructure
  constraints: InfrastructureConstraints
}

export interface CurrentInfrastructure {
  provider: 'aws' | 'azure' | 'gcp' | 'digitalocean' | 'heroku' | 'vercel' | 'netlify'
  services: InfrastructureService[]
  environments: Environment[]
  monitoring: MonitoringSetup
  security: SecuritySetup
}

export interface TargetInfrastructure {
  provider: 'aws' | 'azure' | 'gcp' | 'digitalocean' | 'heroku' | 'vercel' | 'netlify'
  architecture: 'monolith' | 'microservices' | 'serverless' | 'hybrid'
  services: TargetService[]
  scaling: ScalingStrategy
  backup: BackupStrategy
}

export interface InfrastructureService {
  name: string
  type: 'compute' | 'database' | 'storage' | 'network' | 'monitoring' | 'security'
  provider: string
  configuration: any
  status: 'active' | 'inactive' | 'deprecated'
}

export interface TargetService {
  name: string
  type: 'compute' | 'database' | 'storage' | 'network' | 'monitoring' | 'security'
  specifications: ServiceSpecification
  dependencies: string[]
}

export interface ServiceSpecification {
  cpu: string
  memory: string
  storage: string
  network: string
  replicas: number
  autoScaling: boolean
}

export interface Environment {
  name: string
  type: 'development' | 'staging' | 'production' | 'testing'
  url: string
  configuration: EnvironmentConfig
  deployment: DeploymentConfig
}

export interface EnvironmentConfig {
  variables: Record<string, string>
  secrets: Record<string, string>
  resources: ResourceConfig
  networking: NetworkConfig
}

export interface ResourceConfig {
  cpu: string
  memory: string
  storage: string
  instances: number
}

export interface NetworkConfig {
  vpc: string
  subnets: string[]
  securityGroups: string[]
  loadBalancer: boolean
}

export interface DeploymentConfig {
  strategy: 'rolling' | 'blue_green' | 'canary' | 'recreate'
  automation: boolean
  rollback: boolean
  healthChecks: HealthCheck[]
}

export interface HealthCheck {
  type: 'http' | 'tcp' | 'command'
  endpoint?: string
  port?: number
  command?: string
  interval: number
  timeout: number
  retries: number
}

export interface MonitoringSetup {
  tools: MonitoringTool[]
  metrics: MetricConfig[]
  alerts: AlertConfig[]
  dashboards: DashboardConfig[]
}

export interface MonitoringTool {
  name: string
  type: 'metrics' | 'logs' | 'traces' | 'uptime' | 'performance'
  configuration: any
  enabled: boolean
}

export interface MetricConfig {
  name: string
  type: 'counter' | 'gauge' | 'histogram' | 'summary'
  description: string
  labels: string[]
  threshold: number
}

export interface AlertConfig {
  name: string
  condition: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  channels: string[]
  escalation: EscalationRule[]
}

export interface EscalationRule {
  delay: number
  channels: string[]
  condition: string
}

export interface DashboardConfig {
  name: string
  description: string
  panels: DashboardPanel[]
  refresh: number
}

export interface DashboardPanel {
  title: string
  type: 'graph' | 'table' | 'stat' | 'gauge' | 'heatmap'
  query: string
  visualization: any
}

export interface SecuritySetup {
  authentication: AuthenticationSetup
  authorization: AuthorizationSetup
  encryption: EncryptionSetup
  compliance: ComplianceSetup
  scanning: SecurityScanning
}

export interface AuthenticationSetup {
  provider: string
  methods: string[]
  configuration: any
  mfa: boolean
}

export interface AuthorizationSetup {
  model: 'rbac' | 'abac' | 'acl'
  policies: PolicyConfig[]
  roles: RoleConfig[]
}

export interface PolicyConfig {
  name: string
  rules: string[]
  resources: string[]
  actions: string[]
}

export interface RoleConfig {
  name: string
  permissions: string[]
  policies: string[]
}

export interface EncryptionSetup {
  atRest: boolean
  inTransit: boolean
  keyManagement: string
  algorithms: string[]
}

export interface ComplianceSetup {
  standards: string[]
  auditing: boolean
  reporting: boolean
  retention: number
}

export interface SecurityScanning {
  vulnerability: boolean
  dependency: boolean
  container: boolean
  infrastructure: boolean
  frequency: string
}

export interface ScalingStrategy {
  type: 'horizontal' | 'vertical' | 'auto'
  triggers: ScalingTrigger[]
  limits: ScalingLimits
}

export interface ScalingTrigger {
  metric: string
  threshold: number
  action: 'scale_up' | 'scale_down'
  cooldown: number
}

export interface ScalingLimits {
  minInstances: number
  maxInstances: number
  maxCpu: string
  maxMemory: string
}

export interface BackupStrategy {
  frequency: 'hourly' | 'daily' | 'weekly' | 'monthly'
  retention: number
  location: 'local' | 'cloud' | 'both'
  encryption: boolean
  testing: boolean
}

export interface InfrastructureConstraints {
  budget: BudgetConstraints
  compliance: ComplianceConstraints
  performance: PerformanceConstraints
  availability: AvailabilityConstraints
}

export interface BudgetConstraints {
  monthly: number
  annual: number
  currency: string
  alerts: number[]
}

export interface ComplianceConstraints {
  standards: string[]
  regions: string[]
  dataResidency: boolean
  auditing: boolean
}

export interface PerformanceConstraints {
  latency: number
  throughput: number
  availability: number
  durability: number
}

export interface AvailabilityConstraints {
  uptime: number
  rto: number // Recovery Time Objective
  rpo: number // Recovery Point Objective
  regions: number
}

export interface DevOpsRequirements {
  pipeline: PipelineRequirements
  deployment: DeploymentRequirements
  monitoring: MonitoringRequirements
  security: SecurityRequirements
  compliance: ComplianceRequirements
}

export interface PipelineRequirements {
  stages: PipelineStage[]
  triggers: PipelineTrigger[]
  notifications: NotificationConfig[]
  artifacts: ArtifactConfig
  testing: TestingConfig
}

export interface PipelineStage {
  name: string
  type: 'build' | 'test' | 'security' | 'deploy' | 'verify'
  dependencies: string[]
  parallel: boolean
  timeout: number
}

export interface PipelineTrigger {
  type: 'push' | 'pull_request' | 'schedule' | 'manual' | 'webhook'
  branches: string[]
  schedule?: string
  conditions: string[]
}

export interface NotificationConfig {
  channel: 'email' | 'slack' | 'teams' | 'webhook'
  events: string[]
  recipients: string[]
}

export interface ArtifactConfig {
  storage: string
  retention: number
  versioning: boolean
  signing: boolean
}

export interface TestingConfig {
  unit: boolean
  integration: boolean
  e2e: boolean
  performance: boolean
  security: boolean
  coverage: number
}

export interface DeploymentRequirements {
  strategy: 'rolling' | 'blue_green' | 'canary' | 'recreate'
  automation: boolean
  approval: boolean
  rollback: boolean
  environments: string[]
}

export interface MonitoringRequirements {
  metrics: boolean
  logs: boolean
  traces: boolean
  alerts: boolean
  dashboards: boolean
  retention: number
}

export interface SecurityRequirements {
  scanning: boolean
  secrets: boolean
  compliance: boolean
  encryption: boolean
  access: boolean
}

export interface DevOpsResult {
  taskId: string
  status: 'completed' | 'failed' | 'partial'
  deliverables: DevOpsDeliverable[]
  configurations: ConfigurationFile[]
  pipelines: PipelineDefinition[]
  infrastructure: InfrastructureDefinition[]
  monitoring: MonitoringConfiguration[]
  security: SecurityConfiguration[]
  documentation: DevOpsDocumentation[]
  metrics: DevOpsMetrics
}

export interface DevOpsDeliverable {
  type: 'pipeline' | 'infrastructure' | 'configuration' | 'script' | 'documentation'
  name: string
  path: string
  content: string
  format: string
  metadata: any
}

export interface ConfigurationFile {
  name: string
  type: 'yaml' | 'json' | 'toml' | 'env' | 'dockerfile' | 'terraform'
  path: string
  content: string
  environment: string
  purpose: string
}

export interface PipelineDefinition {
  name: string
  platform: 'github_actions' | 'gitlab_ci' | 'jenkins' | 'azure_devops' | 'circleci'
  stages: PipelineStageDefinition[]
  triggers: PipelineTrigger[]
  variables: Record<string, string>
  secrets: string[]
}

export interface PipelineStageDefinition {
  name: string
  jobs: PipelineJob[]
  dependencies: string[]
  conditions: string[]
}

export interface PipelineJob {
  name: string
  image: string
  commands: string[]
  artifacts: string[]
  environment: Record<string, string>
}

export interface InfrastructureDefinition {
  name: string
  type: 'terraform' | 'cloudformation' | 'arm' | 'pulumi' | 'cdk'
  resources: InfrastructureResource[]
  variables: Record<string, any>
  outputs: Record<string, any>
}

export interface InfrastructureResource {
  name: string
  type: string
  properties: Record<string, any>
  dependencies: string[]
}

export interface MonitoringConfiguration {
  tool: string
  configuration: any
  dashboards: any[]
  alerts: any[]
  metrics: any[]
}

export interface SecurityConfiguration {
  tool: string
  policies: any[]
  rules: any[]
  scans: any[]
  compliance: any[]
}

export interface DevOpsDocumentation {
  type: 'runbook' | 'deployment_guide' | 'troubleshooting' | 'architecture'
  title: string
  content: string
  audience: string
}

export interface DevOpsMetrics {
  deploymentFrequency: number
  leadTime: number
  changeFailureRate: number
  recoveryTime: number
  automation: number
  reliability: number
}

/**
 * DevOps Agent - CI/CD and infrastructure automation
 */
export class DevOpsAgent extends BaseAgent {
  private readonly devopsSteps = [
    'analyze_project', 'assess_infrastructure', 'design_pipeline',
    'create_infrastructure', 'setup_monitoring', 'configure_security',
    'implement_deployment', 'setup_automation', 'validate_setup', 'create_documentation'
  ]

  constructor(config: Partial<AgentConfig> = {}) {
    super('devops', {
      capabilities: {
        requiredCapabilities: [
          'pipeline_creation',
          'infrastructure_automation',
          'deployment_automation',
          'monitoring_setup',
          'security_configuration',
          'container_orchestration',
          'cloud_management'
        ],
        contextFilters: ['devops', 'infrastructure', 'deployment', 'monitoring', 'security'],
        mcpEnhanced: true,
        sequentialThinking: true,
        contextEnrichment: true,
        ragIntegration: true
      },
      ...config
    })
  }

  /**
   * Execute DevOps workflow
   */
  protected async executeWorkflow(state: AgentState): Promise<AgentState> {
    const input = state.input as DevOpsInput
    
    console.log(`🚀 Starting DevOps workflow: ${input.task.title}`)

    // Execute DevOps steps sequentially
    for (const stepId of this.devopsSteps) {
      console.log(`🔄 Executing step: ${stepId}`)
      
      const stepResult = await this.executeStepWithContext(stepId, input)
      
      if (stepResult.needsInput) {
        state.needsInput = true
        state.question = stepResult.question
        break
      }

      // Update progress
      state.metadata.completedSteps++
      state.metadata.lastUpdated = new Date().toISOString()
      
      this.emit('step_completed', {
        agentId: this.agentId,
        stepId,
        progress: state.metadata.completedSteps / state.metadata.totalSteps
      })
    }

    // Mark as completed if no input needed
    if (!state.needsInput) {
      state.completed = true
      console.log(`✅ DevOps workflow completed: ${input.task.title}`)
    }

    return state
  }

  /**
   * Execute individual DevOps step with context enhancement
   */
  private async executeStepWithContext(stepId: string, input?: any): Promise<any> {
    // Get enhanced context from context engine
    const enhancedState = await this.getEnhancedContext(stepId)

    // Execute step with MCP enhancement
    switch (stepId) {
      case 'analyze_project':
        return await this.analyzeProjectWithMCP(enhancedState, input)
      case 'assess_infrastructure':
        return await this.assessInfrastructureWithMCP(enhancedState)
      case 'design_pipeline':
        return await this.designPipelineWithMCP(enhancedState)
      case 'create_infrastructure':
        return await this.createInfrastructureWithMCP(enhancedState)
      case 'setup_monitoring':
        return await this.setupMonitoringWithMCP(enhancedState)
      case 'configure_security':
        return await this.configureSecurityWithMCP(enhancedState)
      case 'implement_deployment':
        return await this.implementDeploymentWithMCP(enhancedState)
      case 'setup_automation':
        return await this.setupAutomationWithMCP(enhancedState)
      case 'validate_setup':
        return await this.validateSetupWithMCP(enhancedState)
      case 'create_documentation':
        return await this.createDocumentationWithMCP(enhancedState)
      default:
        throw new Error(`Unknown DevOps step: ${stepId}`)
    }
  }

  /**
   * Get total steps for progress tracking
   */
  protected getTotalSteps(): number {
    return this.devopsSteps.length
  }

  /**
   * Get relevant documentation for DevOps
   */
  protected async getRelevantDocumentation(): Promise<Record<string, any>> {
    return {
      devops: 'DevOps practices and CI/CD pipeline best practices',
      infrastructure: 'Infrastructure as Code and cloud automation',
      deployment: 'Deployment strategies and automation patterns',
      monitoring: 'Application and infrastructure monitoring',
      security: 'DevSecOps and security automation practices',
      containerization: 'Container orchestration and management'
    }
  }

  // ============================================================================
  // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
  // ============================================================================

  private async analyzeProjectWithMCP(state: any, input: DevOpsInput): Promise<any> {
    const analysis = await aiService.analyzeProjectForDevOps(
      input.project,
      input.task.scope
    )

    this.state!.results.projectAnalysis = analysis
    
    return {
      results: analysis,
      needsInput: false,
      completed: false
    }
  }

  private async assessInfrastructureWithMCP(state: any): Promise<any> {
    const infrastructure = this.state!.input.infrastructure
    
    const assessment = await aiService.assessInfrastructure(infrastructure)

    this.state!.results.infrastructureAssessment = assessment
    
    return {
      results: assessment,
      needsInput: false,
      completed: false
    }
  }

  private async designPipelineWithMCP(state: any): Promise<any> {
    const projectAnalysis = this.state!.results.projectAnalysis
    
    const pipeline = await aiService.designCIPipeline(
      projectAnalysis,
      this.state!.input.requirements.pipeline
    )

    this.state!.results.pipeline = pipeline
    
    return {
      results: pipeline,
      needsInput: false,
      completed: false
    }
  }

  private async createInfrastructureWithMCP(state: any): Promise<any> {
    const assessment = this.state!.results.infrastructureAssessment
    
    const infrastructure = await aiService.createInfrastructureAsCode(
      assessment,
      this.state!.input.infrastructure.target
    )

    this.state!.results.infrastructure = infrastructure
    
    return {
      results: infrastructure,
      needsInput: false,
      completed: false
    }
  }

  private async setupMonitoringWithMCP(state: any): Promise<any> {
    const infrastructure = this.state!.results.infrastructure
    
    const monitoring = await aiService.setupMonitoring(
      infrastructure,
      this.state!.input.requirements.monitoring
    )

    this.state!.results.monitoring = monitoring
    
    return {
      results: monitoring,
      needsInput: false,
      completed: false
    }
  }

  private async configureSecurityWithMCP(state: any): Promise<any> {
    const infrastructure = this.state!.results.infrastructure
    
    const security = await aiService.configureDevOpsSecurity(
      infrastructure,
      this.state!.input.requirements.security
    )

    this.state!.results.security = security
    
    return {
      results: security,
      needsInput: false,
      completed: false
    }
  }

  private async implementDeploymentWithMCP(state: any): Promise<any> {
    const pipeline = this.state!.results.pipeline
    const infrastructure = this.state!.results.infrastructure
    
    const deployment = await aiService.implementDeploymentStrategy(
      pipeline,
      infrastructure,
      this.state!.input.requirements.deployment
    )

    this.state!.results.deployment = deployment
    
    return {
      results: deployment,
      needsInput: false,
      completed: false
    }
  }

  private async setupAutomationWithMCP(state: any): Promise<any> {
    const allResults = {
      pipeline: this.state!.results.pipeline,
      infrastructure: this.state!.results.infrastructure,
      monitoring: this.state!.results.monitoring,
      security: this.state!.results.security,
      deployment: this.state!.results.deployment
    }
    
    const automation = await aiService.setupDevOpsAutomation(allResults)

    this.state!.results.automation = automation
    
    return {
      results: automation,
      needsInput: false,
      completed: false
    }
  }

  private async validateSetupWithMCP(state: any): Promise<any> {
    const allResults = this.state!.results
    
    const validation = await aiService.validateDevOpsSetup(
      allResults,
      this.state!.input.requirements
    )

    this.state!.results.validation = validation
    
    return {
      results: validation,
      needsInput: false,
      completed: false
    }
  }

  private async createDocumentationWithMCP(state: any): Promise<any> {
    const allResults = this.state!.results
    
    const documentation = await aiService.createDevOpsDocumentation(
      allResults,
      this.state!.input.task
    )

    this.state!.results.documentation = documentation
    
    return {
      results: documentation,
      needsInput: false,
      completed: true // Final step
    }
  }
}

// Export for easy access
export { DevOpsAgent as default }
