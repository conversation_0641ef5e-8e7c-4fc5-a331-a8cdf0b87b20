"use client"

import React from 'react';
import { App } from '@/lib/dyad-client';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3, 
  TrendingUp, 
  Activity,
  GitBranch,
  Database,
  Globe
} from 'lucide-react';

interface DyadGraphProps {
  selectedApp?: App;
}

export default function DyadGraph({ selectedApp }: DyadGraphProps) {
  // Mock data for demonstration
  const metrics = [
    { name: 'Components', value: 12, change: '+3', color: 'text-blue-400' },
    { name: 'API Endpoints', value: 8, change: '+2', color: 'text-green-400' },
    { name: 'Database Tables', value: 5, change: '0', color: 'text-purple-400' },
    { name: 'Dependencies', value: 24, change: '+1', color: 'text-orange-400' },
  ];

  const dependencies = [
    { name: 'react', version: '18.2.0', type: 'runtime' },
    { name: 'typescript', version: '4.9.5', type: 'dev' },
    { name: 'vite', version: '4.4.5', type: 'dev' },
    { name: 'tailwindcss', version: '3.3.0', type: 'dev' },
    { name: 'express', version: '4.18.2', type: 'runtime' },
    { name: 'prisma', version: '5.1.1', type: 'runtime' },
  ];

  const architecture = [
    { component: 'Frontend', tech: 'React + TypeScript', status: 'active' },
    { component: 'Backend', tech: 'Node.js + Express', status: 'active' },
    { component: 'Database', tech: 'PostgreSQL', status: 'active' },
    { component: 'Auth', tech: 'JWT + bcrypt', status: 'pending' },
    { component: 'Deployment', tech: 'Docker + AWS', status: 'pending' },
  ];

  if (!selectedApp) {
    return (
      <div className="flex-1 bg-[#0a0a0a] flex items-center justify-center">
        <div className="text-center text-[#666] p-8">
          <BarChart3 className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-medium mb-2">No app selected</h3>
          <p className="text-sm">Select an app to view its architecture graph</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 bg-[#0a0a0a] flex flex-col">
      {/* Graph Header */}
      <div className="h-12 bg-[#111111] border-b border-[#1a1a1a] flex items-center justify-between px-4">
        <div className="flex items-center gap-3">
          <BarChart3 className="w-4 h-4 text-orange-500" />
          <span className="text-sm text-white font-medium">Architecture Graph</span>
          <Badge variant="secondary" className="bg-[#2a2a2a] text-[#888] text-[10px] px-2 py-0">
            {selectedApp.name}
          </Badge>
        </div>
      </div>

      <div className="flex-1 p-6 space-y-6">
        {/* Metrics Overview */}
        <div className="grid grid-cols-4 gap-4">
          {metrics.map((metric) => (
            <div key={metric.name} className="bg-[#111111] rounded-lg p-4 border border-[#1a1a1a]">
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs text-[#666]">{metric.name}</span>
                <TrendingUp className="w-3 h-3 text-green-400" />
              </div>
              <div className="flex items-end gap-2">
                <span className={`text-2xl font-bold ${metric.color}`}>{metric.value}</span>
                <span className="text-xs text-green-400 mb-1">{metric.change}</span>
              </div>
            </div>
          ))}
        </div>

        {/* Architecture Diagram */}
        <div className="bg-[#111111] rounded-lg p-6 border border-[#1a1a1a]">
          <h3 className="text-sm font-medium text-white mb-4 flex items-center gap-2">
            <GitBranch className="w-4 h-4 text-blue-500" />
            System Architecture
          </h3>
          
          <div className="grid grid-cols-3 gap-6">
            {architecture.map((item, index) => (
              <div key={item.component} className="text-center">
                <div className={`w-16 h-16 mx-auto mb-3 rounded-lg border-2 flex items-center justify-center ${
                  item.status === 'active' 
                    ? 'border-green-500 bg-green-500/10' 
                    : 'border-yellow-500 bg-yellow-500/10'
                }`}>
                  {item.component === 'Frontend' && <Globe className="w-6 h-6 text-blue-400" />}
                  {item.component === 'Backend' && <Activity className="w-6 h-6 text-green-400" />}
                  {item.component === 'Database' && <Database className="w-6 h-6 text-purple-400" />}
                  {item.component === 'Auth' && <GitBranch className="w-6 h-6 text-orange-400" />}
                  {item.component === 'Deployment' && <BarChart3 className="w-6 h-6 text-red-400" />}
                </div>
                <h4 className="text-xs font-medium text-white mb-1">{item.component}</h4>
                <p className="text-[10px] text-[#666] mb-2">{item.tech}</p>
                <Badge 
                  variant="secondary" 
                  className={`text-[10px] px-2 py-0 ${
                    item.status === 'active'
                      ? 'bg-green-900/20 text-green-400 border-green-800'
                      : 'bg-yellow-900/20 text-yellow-400 border-yellow-800'
                  }`}
                >
                  {item.status}
                </Badge>
              </div>
            ))}
          </div>

          {/* Connection Lines */}
          <div className="relative mt-6">
            <svg className="w-full h-16" viewBox="0 0 400 60">
              {/* Frontend to Backend */}
              <line x1="67" y1="30" x2="133" y2="30" stroke="#333" strokeWidth="2" strokeDasharray="4,4" />
              <text x="100" y="25" fill="#666" fontSize="10" textAnchor="middle">API</text>
              
              {/* Backend to Database */}
              <line x1="200" y1="30" x2="266" y2="30" stroke="#333" strokeWidth="2" strokeDasharray="4,4" />
              <text x="233" y="25" fill="#666" fontSize="10" textAnchor="middle">SQL</text>
              
              {/* Backend to Auth */}
              <line x1="167" y1="45" x2="167" y2="55" stroke="#333" strokeWidth="2" strokeDasharray="4,4" />
              <line x1="167" y1="55" x2="333" y2="55" stroke="#333" strokeWidth="2" strokeDasharray="4,4" />
              <line x1="333" y1="55" x2="333" y2="45" stroke="#333" strokeWidth="2" strokeDasharray="4,4" />
            </svg>
          </div>
        </div>

        {/* Dependencies */}
        <div className="bg-[#111111] rounded-lg p-6 border border-[#1a1a1a]">
          <h3 className="text-sm font-medium text-white mb-4 flex items-center gap-2">
            <Database className="w-4 h-4 text-purple-500" />
            Dependencies
          </h3>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="text-xs text-[#666] mb-3">Runtime Dependencies</h4>
              <div className="space-y-2">
                {dependencies.filter(dep => dep.type === 'runtime').map((dep) => (
                  <div key={dep.name} className="flex items-center justify-between p-2 bg-[#0a0a0a] rounded border border-[#1a1a1a]">
                    <span className="text-xs text-white">{dep.name}</span>
                    <Badge variant="secondary" className="bg-[#2a2a2a] text-[#888] text-[10px] px-2 py-0">
                      {dep.version}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
            
            <div>
              <h4 className="text-xs text-[#666] mb-3">Development Dependencies</h4>
              <div className="space-y-2">
                {dependencies.filter(dep => dep.type === 'dev').map((dep) => (
                  <div key={dep.name} className="flex items-center justify-between p-2 bg-[#0a0a0a] rounded border border-[#1a1a1a]">
                    <span className="text-xs text-white">{dep.name}</span>
                    <Badge variant="secondary" className="bg-[#2a2a2a] text-[#888] text-[10px] px-2 py-0">
                      {dep.version}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="bg-[#111111] rounded-lg p-6 border border-[#1a1a1a]">
          <h3 className="text-sm font-medium text-white mb-4 flex items-center gap-2">
            <Activity className="w-4 h-4 text-green-500" />
            Performance Metrics
          </h3>
          
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400 mb-1">98%</div>
              <div className="text-xs text-[#666]">Uptime</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400 mb-1">1.2s</div>
              <div className="text-xs text-[#666]">Load Time</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400 mb-1">45MB</div>
              <div className="text-xs text-[#666]">Bundle Size</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
