"use client"

import React, { useState, useEffect } from 'react';
import { apiClient, App } from '@/lib/dyad-client';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3, 
  TrendingUp, 
  Activity,
  GitBranch,
  Database,
  Globe,
  Package,
  Code,
  Folder,
  FileText,
  Settings,
  Loader2,
  RefreshCw
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ProjectMetrics {
  totalFiles: number;
  totalLines: number;
  fileTypes: Record<string, number>;
  dependencies: string[];
  devDependencies: string[];
  scripts: Record<string, string>;
  mainEntry: string;
  framework: string;
  buildTool: string;
}

interface FileAnalysis {
  path: string;
  type: string;
  size: number;
  lines: number;
}

interface DyadGraphProps {
  selectedApp?: App;
}

export default function DyadGraph({ selectedApp }: DyadGraphProps) {
  const [metrics, setMetrics] = useState<ProjectMetrics | null>(null);
  const [fileAnalysis, setFileAnalysis] = useState<FileAnalysis[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const loadProjectMetrics = async () => {
    if (!selectedApp) return;

    setIsLoading(true);
    try {
      // Get all app files
      const files = await apiClient.getAppFiles(selectedApp.id);
      
      // Analyze package.json
      let packageJson: any = null;
      try {
        const packageContent = await apiClient.readAppFile(selectedApp.id, 'package.json');
        const content = typeof packageContent === 'string' ? packageContent : packageContent.content;
        packageJson = JSON.parse(content);
      } catch (err) {
        console.warn('Could not read package.json:', err);
      }

      // Analyze file structure
      const fileTypes: Record<string, number> = {};
      const fileDetails: FileAnalysis[] = [];

      for (const filePath of files) {
        const extension = filePath.split('.').pop()?.toLowerCase() || 'unknown';
        fileTypes[extension] = (fileTypes[extension] || 0) + 1;

        try {
          const fileContent = await apiClient.readAppFile(selectedApp.id, filePath);
          const content = typeof fileContent === 'string' ? fileContent : fileContent.content;
          const lines = content.split('\n').length;
          
          fileDetails.push({
            path: filePath,
            type: extension,
            size: content.length,
            lines
          });
        } catch (err) {
          // Skip files that can't be read
        }
      }

      const metrics: ProjectMetrics = {
        totalFiles: files.length,
        totalLines: fileDetails.reduce((sum, file) => sum + file.lines, 0),
        fileTypes,
        dependencies: Object.keys(packageJson?.dependencies || {}),
        devDependencies: Object.keys(packageJson?.devDependencies || {}),
        scripts: packageJson?.scripts || {},
        mainEntry: packageJson?.main || 'index.js',
        framework: detectFramework(packageJson),
        buildTool: detectBuildTool(packageJson)
      };

      setMetrics(metrics);
      setFileAnalysis(fileDetails);

    } catch (err) {
      console.error('Failed to analyze project:', err);
      toast({
        title: "Analysis Failed",
        description: "Could not analyze project structure",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const detectFramework = (packageJson: any): string => {
    const deps = { ...packageJson?.dependencies, ...packageJson?.devDependencies };
    if (deps.react) return 'React';
    if (deps.vue) return 'Vue.js';
    if (deps['@angular/core']) return 'Angular';
    if (deps.svelte) return 'Svelte';
    if (deps.next) return 'Next.js';
    if (deps.nuxt) return 'Nuxt.js';
    return 'Vanilla JS';
  };

  const detectBuildTool = (packageJson: any): string => {
    const scripts = packageJson?.scripts || {};
    const devDeps = packageJson?.devDependencies || {};
    
    if (devDeps.vite) return 'Vite';
    if (devDeps.webpack) return 'Webpack';
    if (devDeps.rollup) return 'Rollup';
    if (devDeps.parcel) return 'Parcel';
    if (scripts.dev?.includes('next')) return 'Next.js';
    return 'Unknown';
  };

  const getFileTypeIcon = (type: string) => {
    switch (type) {
      case 'js':
      case 'ts':
      case 'jsx':
      case 'tsx':
        return <Code className="w-4 h-4 text-yellow-400" />;
      case 'json':
        return <FileText className="w-4 h-4 text-blue-400" />;
      case 'css':
      case 'scss':
      case 'sass':
        return <Settings className="w-4 h-4 text-pink-400" />;
      case 'html':
        return <Globe className="w-4 h-4 text-orange-400" />;
      case 'md':
        return <FileText className="w-4 h-4 text-gray-400" />;
      default:
        return <FileText className="w-4 h-4 text-gray-400" />;
    }
  };

  const getTopFileTypes = () => {
    if (!metrics) return [];
    return Object.entries(metrics.fileTypes)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5);
  };

  const getArchitectureComponents = () => {
    if (!metrics) return [];

    const components = [
      {
        name: 'Frontend',
        tech: metrics.framework,
        status: 'active' as const,
        icon: <Globe className="w-6 h-6 text-blue-400" />,
        details: `${metrics.totalFiles} files`
      },
      {
        name: 'Dependencies',
        tech: `${metrics.dependencies.length} runtime`,
        status: 'active' as const,
        icon: <Package className="w-6 h-6 text-green-400" />,
        details: `${metrics.devDependencies.length} dev`
      },
      {
        name: 'Build System',
        tech: metrics.buildTool,
        status: 'active' as const,
        icon: <Settings className="w-6 h-6 text-purple-400" />,
        details: Object.keys(metrics.scripts).length + ' scripts'
      },
      {
        name: 'Entry Point',
        tech: metrics.mainEntry,
        status: 'active' as const,
        icon: <Code className="w-6 h-6 text-orange-400" />,
        details: metrics.totalLines + ' total lines'
      }
    ];

    return components;
  };

  useEffect(() => {
    if (selectedApp) {
      loadProjectMetrics();
    }
  }, [selectedApp]);

  if (!selectedApp) {
    return (
      <div className="flex-1 bg-[#0a0a0a] flex items-center justify-center">
        <div className="text-center text-[#666] p-8">
          <BarChart3 className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-medium mb-2">No app selected</h3>
          <p className="text-sm">Select an app to view its architecture graph</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 bg-[#0a0a0a] flex flex-col">
      {/* Graph Header */}
      <div className="h-12 bg-[#111111] border-b border-[#1a1a1a] flex items-center justify-between px-4">
        <div className="flex items-center gap-3">
          <BarChart3 className="w-4 h-4 text-orange-500" />
          <span className="text-sm text-white font-medium">Architecture Graph</span>
          <Badge variant="secondary" className="bg-[#2a2a2a] text-[#888] text-[10px] px-2 py-0">
            {selectedApp.name}
          </Badge>
        </div>
        
        <Button
          size="sm"
          variant="ghost"
          onClick={loadProjectMetrics}
          disabled={isLoading}
          className="h-7 px-2 text-[#666] hover:text-white"
        >
          {isLoading ? (
            <Loader2 className="w-3 h-3 animate-spin" />
          ) : (
            <RefreshCw className="w-3 h-3" />
          )}
        </Button>
      </div>

      <div className="flex-1 p-6 space-y-6 overflow-y-auto">
        {/* Project Overview */}
        {metrics && (
          <div className="grid grid-cols-4 gap-4">
            <div className="bg-[#111111] rounded-lg p-4 border border-[#1a1a1a]">
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs text-[#666]">Total Files</span>
                <Folder className="w-3 h-3 text-blue-400" />
              </div>
              <div className="text-2xl font-bold text-white">{metrics.totalFiles}</div>
            </div>
            <div className="bg-[#111111] rounded-lg p-4 border border-[#1a1a1a]">
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs text-[#666]">Total Lines</span>
                <Code className="w-3 h-3 text-green-400" />
              </div>
              <div className="text-2xl font-bold text-white">{metrics.totalLines.toLocaleString()}</div>
            </div>
            <div className="bg-[#111111] rounded-lg p-4 border border-[#1a1a1a]">
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs text-[#666]">Dependencies</span>
                <Package className="w-3 h-3 text-purple-400" />
              </div>
              <div className="text-2xl font-bold text-white">{metrics.dependencies.length}</div>
            </div>
            <div className="bg-[#111111] rounded-lg p-4 border border-[#1a1a1a]">
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs text-[#666]">Dev Deps</span>
                <Settings className="w-3 h-3 text-orange-400" />
              </div>
              <div className="text-2xl font-bold text-white">{metrics.devDependencies.length}</div>
            </div>
          </div>
        )}

        {/* Architecture Components */}
        {metrics && (
          <div className="bg-[#111111] rounded-lg p-6 border border-[#1a1a1a]">
            <h3 className="text-sm font-medium text-white mb-4 flex items-center gap-2">
              <GitBranch className="w-4 h-4 text-blue-500" />
              System Architecture
            </h3>
            
            <div className="grid grid-cols-2 gap-4">
              {getArchitectureComponents().map((component) => (
                <div key={component.name} className="bg-[#0a0a0a] rounded-lg p-4 border border-[#1a1a1a]">
                  <div className="flex items-center gap-3 mb-2">
                    {component.icon}
                    <div>
                      <h4 className="text-sm font-medium text-white">{component.name}</h4>
                      <p className="text-xs text-[#666]">{component.tech}</p>
                    </div>
                  </div>
                  <p className="text-xs text-[#888]">{component.details}</p>
                  <Badge 
                    variant="secondary" 
                    className="mt-2 bg-green-900/20 text-green-400 border-green-800 text-[10px] px-2 py-0"
                  >
                    {component.status}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* File Types Distribution */}
        {metrics && (
          <div className="bg-[#111111] rounded-lg p-6 border border-[#1a1a1a]">
            <h3 className="text-sm font-medium text-white mb-4 flex items-center gap-2">
              <Folder className="w-4 h-4 text-green-500" />
              File Types Distribution
            </h3>
            
            <div className="space-y-3">
              {getTopFileTypes().map(([type, count]) => (
                <div key={type} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getFileTypeIcon(type)}
                    <span className="text-sm text-white">.{type}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-32 bg-[#1a1a1a] rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full"
                        style={{ width: `${(count / metrics.totalFiles) * 100}%` }}
                      />
                    </div>
                    <span className="text-sm text-[#666] w-8 text-right">{count}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Dependencies */}
        {metrics && (
          <div className="grid grid-cols-2 gap-6">
            <div className="bg-[#111111] rounded-lg p-6 border border-[#1a1a1a]">
              <h3 className="text-sm font-medium text-white mb-4 flex items-center gap-2">
                <Package className="w-4 h-4 text-purple-500" />
                Runtime Dependencies
              </h3>
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {metrics.dependencies.map((dep) => (
                  <div key={dep} className="flex items-center justify-between p-2 bg-[#0a0a0a] rounded border border-[#1a1a1a]">
                    <span className="text-xs text-white">{dep}</span>
                    <Badge variant="secondary" className="bg-[#2a2a2a] text-[#888] text-[10px] px-2 py-0">
                      runtime
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="bg-[#111111] rounded-lg p-6 border border-[#1a1a1a]">
              <h3 className="text-sm font-medium text-white mb-4 flex items-center gap-2">
                <Settings className="w-4 h-4 text-orange-500" />
                Development Dependencies
              </h3>
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {metrics.devDependencies.map((dep) => (
                  <div key={dep} className="flex items-center justify-between p-2 bg-[#0a0a0a] rounded border border-[#1a1a1a]">
                    <span className="text-xs text-white">{dep}</span>
                    <Badge variant="secondary" className="bg-[#2a2a2a] text-[#888] text-[10px] px-2 py-0">
                      dev
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Build Scripts */}
        {metrics && Object.keys(metrics.scripts).length > 0 && (
          <div className="bg-[#111111] rounded-lg p-6 border border-[#1a1a1a]">
            <h3 className="text-sm font-medium text-white mb-4 flex items-center gap-2">
              <Activity className="w-4 h-4 text-red-500" />
              Build Scripts
            </h3>
            <div className="grid grid-cols-2 gap-3">
              {Object.entries(metrics.scripts).map(([name, command]) => (
                <div key={name} className="bg-[#0a0a0a] rounded border border-[#1a1a1a] p-3">
                  <div className="text-xs font-medium text-white mb-1">{name}</div>
                  <div className="text-xs text-[#666] font-mono">{command}</div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
