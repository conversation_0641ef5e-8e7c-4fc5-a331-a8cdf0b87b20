{"version": 3, "file": "workflow-handoff-manager.d.ts", "sourceRoot": "", "sources": ["workflow-handoff-manager.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAA;AAErC,MAAM,WAAW,aAAa;IAC5B,qBAAqB,EAAE,OAAO,CAAA;IAC9B,cAAc,EAAE,OAAO,CAAA;IACvB,cAAc,EAAE,MAAM,CAAA;IACtB,mBAAmB,EAAE,OAAO,CAAA;IAC5B,iBAAiB,EAAE,OAAO,CAAA;IAC1B,UAAU,EAAE,MAAM,CAAA;CACnB;AAED,MAAM,WAAW,eAAe;IAC9B,SAAS,EAAE,MAAM,CAAA;IACjB,SAAS,EAAE,MAAM,CAAA;IACjB,OAAO,EAAE,MAAM,CAAA;IACf,UAAU,EAAE,MAAM,CAAA;IAClB,MAAM,EAAE,MAAM,CAAA;IACd,WAAW,EAAE,YAAY,GAAG,UAAU,GAAG,aAAa,GAAG,WAAW,GAAG,YAAY,CAAA;IACnF,KAAK,EAAE,aAAa,CAAA;IACpB,OAAO,EAAE,cAAc,CAAA;IACvB,UAAU,EAAE,eAAe,CAAA;IAC3B,MAAM,EAAE,WAAW,GAAG,SAAS,GAAG,UAAU,GAAG,UAAU,GAAG,WAAW,GAAG,QAAQ,GAAG,aAAa,CAAA;IAClG,SAAS,EAAE,MAAM,CAAA;IACjB,UAAU,CAAC,EAAE,MAAM,CAAA;IACnB,WAAW,CAAC,EAAE,MAAM,CAAA;IACpB,WAAW,EAAE,iBAAiB,EAAE,CAAA;IAChC,YAAY,CAAC,EAAE,YAAY,CAAA;CAC5B;AAED,MAAM,WAAW,aAAa;IAC5B,OAAO,EAAE,MAAM,CAAA;IACf,OAAO,EAAE,MAAM,CAAA;IACf,IAAI,EAAE,GAAG,CAAA;IACT,QAAQ,EAAE,aAAa,CAAA;IACvB,YAAY,EAAE,eAAe,EAAE,CAAA;IAC/B,SAAS,EAAE,aAAa,EAAE,CAAA;IAC1B,QAAQ,EAAE,MAAM,CAAA;CACjB;AAED,MAAM,WAAW,aAAa;IAC5B,YAAY,EAAE,MAAM,CAAA;IACpB,UAAU,EAAE,MAAM,CAAA;IAClB,IAAI,EAAE,MAAM,CAAA;IACZ,QAAQ,EAAE,MAAM,CAAA;IAChB,MAAM,EAAE,MAAM,CAAA;IACd,MAAM,EAAE,MAAM,CAAA;CACf;AAED,MAAM,WAAW,eAAe;IAC9B,YAAY,EAAE,MAAM,CAAA;IACpB,IAAI,EAAE,MAAM,GAAG,SAAS,GAAG,UAAU,GAAG,OAAO,CAAA;IAC/C,MAAM,EAAE,WAAW,GAAG,aAAa,GAAG,SAAS,CAAA;IAC/C,QAAQ,EAAE,OAAO,CAAA;CAClB;AAED,MAAM,WAAW,aAAa;IAC5B,UAAU,EAAE,MAAM,CAAA;IAClB,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,EAAE,MAAM,GAAG,MAAM,GAAG,eAAe,GAAG,QAAQ,CAAA;IAClD,OAAO,EAAE,GAAG,CAAA;IACZ,QAAQ,EAAE,gBAAgB,CAAA;CAC3B;AAED,MAAM,WAAW,gBAAgB;IAC/B,IAAI,EAAE,MAAM,CAAA;IACZ,OAAO,EAAE,MAAM,CAAA;IACf,QAAQ,EAAE,MAAM,CAAA;IAChB,QAAQ,EAAE,MAAM,CAAA;IAChB,QAAQ,EAAE,MAAM,CAAA;CACjB;AAED,MAAM,WAAW,cAAc;IAC7B,aAAa,EAAE,MAAM,CAAA;IACrB,cAAc,EAAE,MAAM,EAAE,CAAA;IACxB,UAAU,EAAE,MAAM,EAAE,CAAA;IACpB,YAAY,EAAE,kBAAkB,EAAE,CAAA;IAClC,WAAW,EAAE,iBAAiB,EAAE,CAAA;IAChC,YAAY,EAAE,kBAAkB,EAAE,CAAA;IAClC,aAAa,EAAE,MAAM,CAAA;CACtB;AAED,MAAM,WAAW,kBAAkB;IACjC,IAAI,EAAE,YAAY,GAAG,UAAU,GAAG,YAAY,GAAG,WAAW,CAAA;IAC5D,WAAW,EAAE,MAAM,CAAA;IACnB,SAAS,EAAE,OAAO,CAAA;IAClB,kBAAkB,EAAE,MAAM,CAAA;CAC3B;AAED,MAAM,WAAW,iBAAiB;IAChC,IAAI,EAAE,MAAM,GAAG,UAAU,GAAG,SAAS,GAAG,UAAU,GAAG,YAAY,CAAA;IACjE,WAAW,EAAE,MAAM,CAAA;IACnB,KAAK,EAAE,GAAG,CAAA;IACV,WAAW,EAAE,QAAQ,GAAG,UAAU,CAAA;CACnC;AAED,MAAM,WAAW,kBAAkB;IACjC,MAAM,EAAE,MAAM,CAAA;IACd,MAAM,EAAE,GAAG,CAAA;IACX,SAAS,EAAE,MAAM,CAAA;IACjB,WAAW,EAAE,MAAM,CAAA;IACnB,QAAQ,EAAE,MAAM,CAAA;CACjB;AAED,MAAM,WAAW,eAAe;IAC9B,YAAY,EAAE,MAAM,CAAA;IACpB,KAAK,EAAE,cAAc,EAAE,CAAA;IACvB,MAAM,EAAE,SAAS,GAAG,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAA;IACnD,OAAO,EAAE,gBAAgB,EAAE,CAAA;IAC3B,KAAK,EAAE,MAAM,CAAA;CACd;AAED,MAAM,WAAW,cAAc;IAC7B,MAAM,EAAE,MAAM,CAAA;IACd,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,EAAE,gBAAgB,GAAG,cAAc,GAAG,QAAQ,GAAG,gBAAgB,GAAG,UAAU,CAAA;IAClF,SAAS,EAAE,MAAM,CAAA;IACjB,QAAQ,EAAE,OAAO,GAAG,SAAS,GAAG,MAAM,CAAA;IACtC,OAAO,EAAE,OAAO,CAAA;CACjB;AAED,MAAM,WAAW,gBAAgB;IAC/B,MAAM,EAAE,MAAM,CAAA;IACd,MAAM,EAAE,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAA;IACvC,OAAO,EAAE,MAAM,CAAA;IACf,OAAO,EAAE,GAAG,CAAA;IACZ,SAAS,EAAE,OAAO,CAAA;CACnB;AAED,MAAM,WAAW,iBAAiB;IAChC,YAAY,EAAE,MAAM,CAAA;IACpB,IAAI,EAAE,MAAM,CAAA;IACZ,SAAS,EAAE,MAAM,CAAA;IACjB,KAAK,EAAE,aAAa,CAAA;IACpB,UAAU,EAAE,eAAe,CAAA;IAC3B,OAAO,EAAE,MAAM,CAAA;IACf,WAAW,EAAE,MAAM,CAAA;IACnB,aAAa,EAAE,OAAO,CAAA;CACvB;AAED,MAAM,WAAW,YAAY;IAC3B,MAAM,EAAE,MAAM,CAAA;IACd,QAAQ,EAAE,eAAe,EAAE,CAAA;IAC3B,QAAQ,EAAE,YAAY,GAAG,gBAAgB,GAAG,eAAe,GAAG,QAAQ,CAAA;IACtE,KAAK,EAAE,YAAY,EAAE,CAAA;IACrB,YAAY,EAAE,gBAAgB,CAAA;IAC9B,aAAa,EAAE,gBAAgB,EAAE,CAAA;CAClC;AAED,MAAM,WAAW,eAAe;IAC9B,IAAI,EAAE,oBAAoB,GAAG,SAAS,GAAG,eAAe,GAAG,QAAQ,GAAG,mBAAmB,CAAA;IACzF,SAAS,EAAE,MAAM,CAAA;IACjB,SAAS,EAAE,GAAG,CAAA;IACd,SAAS,EAAE,OAAO,CAAA;CACnB;AAED,MAAM,WAAW,YAAY;IAC3B,MAAM,EAAE,MAAM,CAAA;IACd,KAAK,EAAE,MAAM,CAAA;IACb,MAAM,EAAE,eAAe,GAAG,eAAe,GAAG,mBAAmB,GAAG,mBAAmB,CAAA;IACrF,UAAU,EAAE,GAAG,CAAA;IACf,UAAU,EAAE,MAAM,CAAA;IAClB,OAAO,EAAE,MAAM,CAAA;CAChB;AAED,MAAM,WAAW,gBAAgB;IAC/B,cAAc,EAAE,YAAY,GAAG,aAAa,GAAG,MAAM,CAAA;IACrD,eAAe,EAAE,MAAM,CAAA;IACvB,kBAAkB,EAAE,OAAO,CAAA;IAC3B,iBAAiB,EAAE,OAAO,CAAA;CAC3B;AAED,MAAM,WAAW,gBAAgB;IAC/B,UAAU,EAAE,MAAM,EAAE,CAAA;IACpB,QAAQ,EAAE,MAAM,EAAE,CAAA;IAClB,QAAQ,EAAE,MAAM,CAAA;IAChB,OAAO,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,UAAU,CAAA;CAChD;AAED,MAAM,WAAW,cAAc;IAC7B,aAAa,EAAE,MAAM,CAAA;IACrB,WAAW,EAAE,MAAM,CAAA;IACnB,kBAAkB,EAAE,MAAM,CAAA;IAC1B,YAAY,EAAE,MAAM,CAAA;IACpB,qBAAqB,EAAE,MAAM,CAAA;IAC7B,gBAAgB,EAAE,uBAAuB,EAAE,CAAA;CAC5C;AAED,MAAM,WAAW,uBAAuB;IACtC,OAAO,EAAE,MAAM,CAAA;IACf,gBAAgB,EAAE,MAAM,CAAA;IACxB,aAAa,EAAE,MAAM,CAAA;IACrB,WAAW,EAAE,MAAM,CAAA;IACnB,qBAAqB,EAAE,MAAM,CAAA;IAC7B,eAAe,EAAE,MAAM,CAAA;CACxB;AAED;;GAEG;AACH,qBAAa,sBAAuB,SAAQ,YAAY;IACtD,OAAO,CAAC,MAAM,CAAe;IAC7B,OAAO,CAAC,cAAc,CAA0C;IAChE,OAAO,CAAC,cAAc,CAAwB;IAC9C,OAAO,CAAC,eAAe,CAA2C;IAClE,OAAO,CAAC,WAAW,CAA8C;gBAErD,MAAM,GAAE,OAAO,CAAC,aAAa,CAAM;IAe/C;;OAEG;IACG,eAAe,CACnB,SAAS,EAAE,MAAM,EACjB,OAAO,EAAE,MAAM,EACf,UAAU,EAAE,MAAM,EAClB,MAAM,EAAE,MAAM,EACd,KAAK,EAAE,aAAa,EACpB,OAAO,GAAE,OAAO,CAAC,cAAc,CAAM,EACrC,WAAW,GAAE,eAAe,CAAC,aAAa,CAAgB,GACzD,OAAO,CAAC,eAAe,CAAC;IA4D3B;;OAEG;IACG,gBAAgB,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,GAAG,QAAQ,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IA8BxG;;OAEG;IACG,eAAe,CAAC,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,MAAM,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IAsChG;;OAEG;IACG,eAAe,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,gBAAgB,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAqClG;;OAEG;IACG,gBAAgB,CACpB,UAAU,EAAE,MAAM,EAClB,OAAO,EAAE,MAAM,EACf,KAAK,EAAE,aAAa,EACpB,WAAW,EAAE,MAAM,EACnB,aAAa,GAAE,OAAe,GAC7B,OAAO,CAAC,iBAAiB,CAAC;IAsB7B;;OAEG;IACH,iBAAiB,IAAI,cAAc;IAenC;;OAEG;YACW,aAAa;YAwBb,qBAAqB;IAuCnC,OAAO,CAAC,uBAAuB;IAU/B,OAAO,CAAC,kBAAkB;YA6BZ,wBAAwB;IActC,OAAO,CAAC,iBAAiB;YASX,mBAAmB;YAgBnB,wBAAwB;IAItC,OAAO,CAAC,UAAU;IAIlB,OAAO,CAAC,sBAAsB;IAK9B,OAAO,CAAC,aAAa;IAKrB,OAAO,CAAC,2BAA2B;IAQnC,OAAO,CAAC,8BAA8B;IAQtC,OAAO,CAAC,yBAAyB;IA0CjC,OAAO,CAAC,gCAAgC;CA8BzC;AAED,eAAe,sBAAsB,CAAA"}