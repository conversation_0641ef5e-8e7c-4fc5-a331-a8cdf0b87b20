Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Starting Dyad Frontend + Backend" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check current directory
Write-Host "Current directory: $PWD" -ForegroundColor Yellow
Write-Host ""

# Check if Node.js is installed
Write-Host "Checking Node.js..." -ForegroundColor Green
try {
    $nodeVersion = node --version
    Write-Host "Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Node.js is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if npm is installed  
Write-Host "Checking npm..." -ForegroundColor Green
try {
    $npmVersion = npm --version
    Write-Host "npm version: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: npm is not installed or not in PATH" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Check required files
if (-not (Test-Path "package.json")) {
    Write-Host "ERROR: package.json not found in current directory" -ForegroundColor Red
    Write-Host "Make sure you're running this script from the dyad-main folder" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

if (-not (Test-Path "ai-dev-ecosystem")) {
    Write-Host "ERROR: ai-dev-ecosystem folder not found" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

if (-not (Test-Path "ai-dev-ecosystem\package.json")) {
    Write-Host "ERROR: package.json not found in ai-dev-ecosystem folder" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "All required files found" -ForegroundColor Green
Write-Host ""

Write-Host "Starting Dyad Backend (Main App)..." -ForegroundColor Yellow
Write-Host "Backend will run in a new window" -ForegroundColor Yellow
Write-Host ""

# Start main Dyad app in new window
Start-Process powershell -ArgumentList "-NoExit", "-Command", "Write-Host 'Starting Dyad Main App...' -ForegroundColor Cyan; npm start"

Write-Host "Waiting 3 seconds for main app to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

Write-Host ""
Write-Host "Starting Dyad Web Server (API)..." -ForegroundColor Yellow
Write-Host "Web server will run in a new window" -ForegroundColor Yellow
Write-Host ""

# Start Dyad web server in new window
Start-Process powershell -ArgumentList "-NoExit", "-Command", "Write-Host 'Starting Dyad Web Server...' -ForegroundColor Cyan; npm run web-server"

Write-Host "Waiting 3 seconds for web server to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

Write-Host ""
Write-Host "Starting Frontend (AI Dev Ecosystem)..." -ForegroundColor Yellow  
Write-Host "Frontend will run in a new window" -ForegroundColor Yellow
Write-Host ""

# Start frontend in new window
Start-Process powershell -ArgumentList "-NoExit", "-Command", "Write-Host 'Starting Dyad Frontend...' -ForegroundColor Cyan; Set-Location ai-dev-ecosystem; npm run dev"

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   All services are starting up!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Dyad Main App: Starting in separate window" -ForegroundColor Green
Write-Host "Dyad Web Server: Starting in separate window (http://localhost:3002)" -ForegroundColor Green
Write-Host "Frontend (Web UI): Starting in separate window (http://localhost:3001)" -ForegroundColor Green
Write-Host ""
Write-Host "Wait a few moments for all services to fully start." -ForegroundColor Yellow
Write-Host "The frontend will be available at: http://localhost:3001" -ForegroundColor Cyan
Write-Host "The Dyad API will be available at: http://localhost:3002" -ForegroundColor Cyan
Write-Host ""
Write-Host "To stop the services:" -ForegroundColor Yellow
Write-Host "- Close the Dyad Main App PowerShell window" -ForegroundColor Yellow
Write-Host "- Close the Dyad Web Server PowerShell window" -ForegroundColor Yellow
Write-Host "- Close the Frontend PowerShell window" -ForegroundColor Yellow
Write-Host ""
Write-Host "This window will stay open for monitoring." -ForegroundColor Yellow
Read-Host "Press Enter to exit this launcher"
