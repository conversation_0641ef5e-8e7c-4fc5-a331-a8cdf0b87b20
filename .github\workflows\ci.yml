name: CI

on:
  push:
    branches:
      - main
  pull_request:
    types: [opened, synchronize, reopened]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

defaults:
  run:
    shell: bash

jobs:
  test:
    # Why Mac and Windows?
    # I can't run electron playwright on ubuntu-latest and
    # Linux support for Dyad is experimental so not as important
    # as Mac + Windows
    strategy:
      fail-fast: false
      matrix:
        os:
          [
            { name: "windows-arm", image: "windows-11-arm" },
            { name: "windows", image: "windows-latest" },
            { name: "macos", image: "macos-latest" },
          ]
    runs-on: ${{ matrix.os.image }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Initialize environment
        uses: actions/setup-node@v4
        with:
          node-version-file: package.json
          cache: npm
          cache-dependency-path: package-lock.json
      - name: Install node modules
        run: npm ci --no-audit --no-fund --progress=false
      - name: Presubmit check (e.g. lint, format)
        # do not run this on Windows (it fails and not necessary)
        if: contains(matrix.os.name, 'macos')
        run: npm run presubmit
      - name: Type-checking
        # do not run this on windows (it's redunant)
        if: contains(matrix.os.name, 'macos')
        run: npm run ts
      - name: Unit tests
        # do not run this on windows (it's redunant)
        if: contains(matrix.os.name, 'macos')
        run: npm run test
      - name: Setup pnpm
        uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.1.0
        with:
          version: latest
      - name: Clone nextjs-template
        run: git clone --depth 1 https://github.com/dyad-sh/nextjs-template.git nextjs-template
      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT
      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ matrix.os.name }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ matrix.os.name }}-pnpm-store-
      # Not strictly needed but makes the e2e tests faster (and less flaky)
      - name: Install scaffold dependencies
        run: cd scaffold && pnpm install
      - name: Install nextjs-template dependencies
        run: cd nextjs-template && pnpm install
      - name: Install Chromium browser for Playwright
        run: npx playwright install chromium --with-deps
      - name: Build
        run: npm run pre:e2e
      - name: Prep test server
        run: cd testing/fake-llm-server && npm install && npm run build && cd -
      - name: E2E tests
        # You can add debug logging to make it easier to see what's failing
        # by adding "DEBUG=pw:browser" in front.
        run: DEBUG=pw:browser npm run e2e
      - uses: actions/upload-artifact@65c4c4a1ddee5b72f698fdd19549f0f0fb45cf08 # v4.6.0
        if: failure()
        with:
          name: playwright-report-${{ matrix.os.name }}
          path: playwright-report/
          retention-days: 3
      - uses: actions/upload-artifact@65c4c4a1ddee5b72f698fdd19549f0f0fb45cf08 # v4.6.0
        if: failure()
        with:
          name: test-results-${{ matrix.os.name }}
          path: test-results/
          retention-days: 3
