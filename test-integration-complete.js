#!/usr/bin/env node

/**
 * Integration Test Script
 * Verifies complete Dyad backend integration with ai-dev-ecosystem frontend
 */

const fetch = require('node-fetch');
const { spawn } = require('child_process');
const path = require('path');

const BACKEND_URL = 'http://localhost:3002';
const FRONTEND_URL = 'http://localhost:3001';

async function testBackendHealth() {
  console.log('🔍 Testing backend health...');
  try {
    const response = await fetch(`${BACKEND_URL}/health`);
    const data = await response.json();
    console.log('✅ Backend health:', data);
    return true;
  } catch (error) {
    console.error('❌ Backend health check failed:', error.message);
    return false;
  }
}

async function testAgentsHealth() {
  console.log('🔍 Testing agents health...');
  try {
    const response = await fetch(`${BACKEND_URL}/api/agents/health`);
    const data = await response.json();
    console.log('✅ Agents health:', data.status);
    return true;
  } catch (error) {
    console.log('⚠️  Agents not initialized (expected if AG3NT framework not available)');
    return true; // Not critical for basic functionality
  }
}

async function testAppsAPI() {
  console.log('🔍 Testing apps API...');
  try {
    const response = await fetch(`${BACKEND_URL}/api/apps`);
    const apps = await response.json();
    console.log('✅ Apps API working:', apps.length, 'apps');
    return true;
  } catch (error) {
    console.error('❌ Apps API failed:', error.message);
    return false;
  }
}

async function testChatAPI() {
  console.log('🔍 Testing chat API...');
  try {
    const response = await fetch(`${BACKEND_URL}/api/chats`);
    const chats = await response.json();
    console.log('✅ Chat API working:', chats.length, 'chats');
    return true;
  } catch (error) {
    console.error('❌ Chat API failed:', error.message);
    return false;
  }
}

async function testFrontendHealth() {
  console.log('🔍 Testing frontend...');
  try {
    const response = await fetch(FRONTEND_URL);
    console.log('✅ Frontend responding:', response.status);
    return true;
  } catch (error) {
    console.error('❌ Frontend not responding:', error.message);
    return false;
  }
}

async function runIntegrationTests() {
  console.log('🚀 Starting integration tests...\n');

  const tests = [
    testBackendHealth,
    testAgentsHealth,
    testAppsAPI,
    testChatAPI,
    testFrontendHealth
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      const result = await test();
      if (result) passed++;
      else failed++;
    } catch (error) {
      console.error('❌ Test error:', error.message);
      failed++;
    }
    console.log('');
  }

  console.log('📊 Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${Math.round((passed / tests.length) * 100)}%`);

  if (failed === 0) {
    console.log('\n🎉 All integration tests passed!');
    console.log('\n🎯 Ready to use:');
    console.log(`   Frontend: ${FRONTEND_URL}`);
    console.log(`   Backend: ${BACKEND_URL}`);
    console.log(`   Health: ${BACKEND_URL}/health`);
    console.log(`   API Docs: ${BACKEND_URL}/api`);
  } else {
    console.log('\n⚠️  Some tests failed. Check the logs above.');
    process.exit(1);
  }
}

// Check if backend is running
async function checkBackendRunning() {
  try {
    await fetch(`${BACKEND_URL}/health`);
    return true;
  } catch {
    return false;
  }
}

async function main() {
  console.log('🔧 Dyad Integration Test Suite\n');

  const backendRunning = await checkBackendRunning();
  
  if (!backendRunning) {
    console.log('⚠️  Backend not detected. Please start the backend first:');
    console.log('   npm run start:web-server');
    console.log('\nOr use the integrated start script:');
    console.log('   npm run start:integrated');
    process.exit(1);
  }

  await runIntegrationTests();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { runIntegrationTests };
