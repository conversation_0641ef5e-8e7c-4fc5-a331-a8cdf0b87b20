"use strict";
/**
 * AG3NT Framework - Tester Agent
 *
 * Specialized agent for comprehensive testing and quality assurance.
 * Handles unit testing, integration testing, e2e testing, and quality metrics.
 *
 * Features:
 * - Automated test generation and execution
 * - Test strategy planning and optimization
 * - Quality metrics and coverage analysis
 * - Performance and security testing
 * - Test maintenance and refactoring
 * - CI/CD integration
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = exports.TesterAgent = void 0;
const base_agent_1 = require("../core/base-agent");
const ai_service_1 = require("../../ai-service");
/**
 * Tester Agent - Comprehensive testing and quality assurance
 */
class TesterAgent extends base_agent_1.BaseAgent {
    constructor(config = {}) {
        super('tester', {
            capabilities: {
                requiredCapabilities: [
                    'test_generation',
                    'test_execution',
                    'quality_assurance',
                    'performance_testing',
                    'security_testing',
                    'accessibility_testing',
                    'test_automation',
                    'coverage_analysis'
                ],
                contextFilters: ['testing', 'quality', 'code', 'performance', 'security'],
                mcpEnhanced: true,
                sequentialThinking: true,
                contextEnrichment: true,
                ragIntegration: true
            },
            ...config
        });
        this.testingSteps = [
            'analyze_testability', 'plan_testing_strategy', 'generate_tests',
            'execute_unit_tests', 'execute_integration_tests', 'execute_e2e_tests',
            'performance_testing', 'security_testing', 'accessibility_testing', 'generate_reports'
        ];
    }
    /**
     * Execute testing workflow
     */
    async executeWorkflow(state) {
        const input = state.input;
        console.log(`🧪 Starting testing workflow: ${input.task.title}`);
        // Execute testing steps sequentially
        for (const stepId of this.testingSteps) {
            console.log(`🔄 Executing step: ${stepId}`);
            const stepResult = await this.executeStepWithContext(stepId, input);
            if (stepResult.needsInput) {
                state.needsInput = true;
                state.question = stepResult.question;
                break;
            }
            // Update progress
            state.metadata.completedSteps++;
            state.metadata.lastUpdated = new Date().toISOString();
            this.emit('step_completed', {
                agentId: this.agentId,
                stepId,
                progress: state.metadata.completedSteps / state.metadata.totalSteps
            });
        }
        // Mark as completed if no input needed
        if (!state.needsInput) {
            state.completed = true;
            console.log(`✅ Testing completed: ${input.task.title}`);
        }
        return state;
    }
    /**
     * Execute individual testing step with context enhancement
     */
    async executeStepWithContext(stepId, input) {
        // Get enhanced context from context engine
        const enhancedState = await this.getEnhancedContext(stepId);
        // Execute step with MCP enhancement
        switch (stepId) {
            case 'analyze_testability':
                return await this.analyzeTestabilityWithMCP(enhancedState, input);
            case 'plan_testing_strategy':
                return await this.planTestingStrategyWithMCP(enhancedState);
            case 'generate_tests':
                return await this.generateTestsWithMCP(enhancedState);
            case 'execute_unit_tests':
                return await this.executeUnitTestsWithMCP(enhancedState);
            case 'execute_integration_tests':
                return await this.executeIntegrationTestsWithMCP(enhancedState);
            case 'execute_e2e_tests':
                return await this.executeE2ETestsWithMCP(enhancedState);
            case 'performance_testing':
                return await this.performanceTestingWithMCP(enhancedState);
            case 'security_testing':
                return await this.securityTestingWithMCP(enhancedState);
            case 'accessibility_testing':
                return await this.accessibilityTestingWithMCP(enhancedState);
            case 'generate_reports':
                return await this.generateReportsWithMCP(enhancedState);
            default:
                throw new Error(`Unknown testing step: ${stepId}`);
        }
    }
    /**
     * Get total steps for progress tracking
     */
    getTotalSteps() {
        return this.testingSteps.length;
    }
    /**
     * Get relevant documentation for testing
     */
    async getRelevantDocumentation() {
        return {
            testing: 'Software testing methodologies and best practices',
            automation: 'Test automation frameworks and strategies',
            performance: 'Performance testing and load testing techniques',
            security: 'Security testing and vulnerability assessment',
            accessibility: 'Accessibility testing and WCAG compliance',
            quality: 'Quality assurance and metrics analysis'
        };
    }
    // ============================================================================
    // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
    // ============================================================================
    async analyzeTestabilityWithMCP(state, input) {
        const analysis = await ai_service_1.aiService.analyzeTestability(input.codebase, input.requirements, input.strategy);
        this.state.results.testabilityAnalysis = analysis;
        return {
            results: analysis,
            needsInput: false,
            completed: false
        };
    }
    async planTestingStrategyWithMCP(state) {
        const testabilityAnalysis = this.state.results.testabilityAnalysis;
        const strategy = await ai_service_1.aiService.planTestingStrategy(testabilityAnalysis, this.state.input.requirements);
        this.state.results.testingStrategy = strategy;
        return {
            results: strategy,
            needsInput: false,
            completed: false
        };
    }
    async generateTestsWithMCP(state) {
        const strategy = this.state.results.testingStrategy;
        const tests = await ai_service_1.aiService.generateTests(strategy, this.state.input.codebase);
        this.state.results.generatedTests = tests;
        return {
            results: tests,
            needsInput: false,
            completed: false
        };
    }
    async executeUnitTestsWithMCP(state) {
        const tests = this.state.results.generatedTests;
        const unitTestResults = await ai_service_1.aiService.executeUnitTests(tests.unit);
        this.state.results.unitTestResults = unitTestResults;
        return {
            results: unitTestResults,
            needsInput: false,
            completed: false
        };
    }
    async executeIntegrationTestsWithMCP(state) {
        const tests = this.state.results.generatedTests;
        const integrationTestResults = await ai_service_1.aiService.executeIntegrationTests(tests.integration);
        this.state.results.integrationTestResults = integrationTestResults;
        return {
            results: integrationTestResults,
            needsInput: false,
            completed: false
        };
    }
    async executeE2ETestsWithMCP(state) {
        const tests = this.state.results.generatedTests;
        const e2eTestResults = await ai_service_1.aiService.executeE2ETests(tests.e2e);
        this.state.results.e2eTestResults = e2eTestResults;
        return {
            results: e2eTestResults,
            needsInput: false,
            completed: false
        };
    }
    async performanceTestingWithMCP(state) {
        const requirements = this.state.input.requirements.performance;
        const performanceResults = await ai_service_1.aiService.performPerformanceTesting(requirements, this.state.input.codebase);
        this.state.results.performanceResults = performanceResults;
        return {
            results: performanceResults,
            needsInput: false,
            completed: false
        };
    }
    async securityTestingWithMCP(state) {
        const requirements = this.state.input.requirements.security;
        const securityResults = await ai_service_1.aiService.performSecurityTesting(requirements, this.state.input.codebase);
        this.state.results.securityResults = securityResults;
        return {
            results: securityResults,
            needsInput: false,
            completed: false
        };
    }
    async accessibilityTestingWithMCP(state) {
        const requirements = this.state.input.requirements.accessibility;
        const accessibilityResults = await ai_service_1.aiService.performAccessibilityTesting(requirements, this.state.input.codebase);
        this.state.results.accessibilityResults = accessibilityResults;
        return {
            results: accessibilityResults,
            needsInput: false,
            completed: false
        };
    }
    async generateReportsWithMCP(state) {
        const allResults = {
            unit: this.state.results.unitTestResults,
            integration: this.state.results.integrationTestResults,
            e2e: this.state.results.e2eTestResults,
            performance: this.state.results.performanceResults,
            security: this.state.results.securityResults,
            accessibility: this.state.results.accessibilityResults
        };
        const reports = await ai_service_1.aiService.generateTestingReports(allResults, this.state.input.task);
        this.state.results.reports = reports;
        return {
            results: reports,
            needsInput: false,
            completed: true // Final step
        };
    }
}
exports.TesterAgent = TesterAgent;
exports.default = TesterAgent;
//# sourceMappingURL=tester-agent.js.map