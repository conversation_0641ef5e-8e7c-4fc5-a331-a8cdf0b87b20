/**
 * AG3NT Framework - Task Planner Agent
 *
 * Specialized agent for breaking down high-level plans into executable tasks.
 * Works in coordination with PlanningAgent to create detailed task breakdowns.
 *
 * Features:
 * - Task decomposition and dependency analysis
 * - Resource estimation and timeline planning
 * - Task prioritization and scheduling
 * - Integration with workflow coordination
 * - MCP-enhanced task analysis
 */
import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent";
export interface TaskPlannerInput {
    projectPlan: any;
    requirements: any;
    constraints?: {
        timeline?: string;
        resources?: string[];
        budget?: number;
    };
    preferences?: {
        methodology?: 'agile' | 'waterfall' | 'kanban';
        teamSize?: number;
        sprintDuration?: number;
    };
}
export interface TaskPlannerResult {
    tasks: Task[];
    dependencies: TaskDependency[];
    timeline: ProjectTimeline;
    resources: ResourceAllocation[];
    milestones: Milestone[];
    risks: Risk[];
}
export interface Task {
    taskId: string;
    title: string;
    description: string;
    type: 'development' | 'design' | 'testing' | 'deployment' | 'documentation';
    priority: 'critical' | 'high' | 'medium' | 'low';
    estimatedHours: number;
    complexity: 'simple' | 'medium' | 'complex';
    requiredSkills: string[];
    assignedAgent?: string;
    status: 'pending' | 'in_progress' | 'completed' | 'blocked';
    dependencies: string[];
    deliverables: string[];
    acceptanceCriteria: string[];
}
export interface TaskDependency {
    fromTask: string;
    toTask: string;
    type: 'finish_to_start' | 'start_to_start' | 'finish_to_finish';
    lag?: number;
}
export interface ProjectTimeline {
    totalDuration: number;
    phases: Phase[];
    criticalPath: string[];
    bufferTime: number;
}
export interface Phase {
    phaseId: string;
    name: string;
    startDate: string;
    endDate: string;
    tasks: string[];
    deliverables: string[];
}
export interface ResourceAllocation {
    agentType: string;
    requiredCapabilities: string[];
    utilizationPercentage: number;
    peakDemandPeriod: string;
}
export interface Milestone {
    milestoneId: string;
    name: string;
    description: string;
    targetDate: string;
    criteria: string[];
    dependencies: string[];
}
export interface Risk {
    riskId: string;
    description: string;
    probability: 'low' | 'medium' | 'high';
    impact: 'low' | 'medium' | 'high';
    mitigation: string;
    contingency: string;
}
/**
 * Task Planner Agent - Sophisticated task breakdown and project planning
 */
export declare class TaskPlannerAgent extends BaseAgent {
    private readonly planningSteps;
    constructor(config?: Partial<AgentConfig>);
    /**
     * Execute task planning workflow
     */
    protected executeWorkflow(state: AgentState): Promise<AgentState>;
    /**
     * Execute individual planning step with context enhancement
     */
    private executeStepWithContext;
    /**
     * Get total steps for progress tracking
     */
    protected getTotalSteps(): number;
    /**
     * Get relevant documentation for task planning
     */
    protected getRelevantDocumentation(): Promise<Record<string, any>>;
    private analyzePlanWithMCP;
    private decomposeTasksWithMCP;
    private analyzeDependenciesWithMCP;
    private estimateEffortWithMCP;
    private createTimelineWithMCP;
    private allocateResourcesWithMCP;
    private identifyRisksWithMCP;
    private defineMilestonesWithMCP;
    private optimizeScheduleWithMCP;
}
export { TaskPlannerAgent as default };
//# sourceMappingURL=task-planner-agent.d.ts.map