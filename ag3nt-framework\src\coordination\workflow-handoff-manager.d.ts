/**
 * AG3NT Framework - Workflow Handoff Manager
 *
 * Formal handoff protocols with state validation and rollback capabilities
 * for seamless work transitions between agents.
 */
import { EventEmitter } from "events";
export interface HandoffConfig {
    enableStateValidation: boolean;
    enableRollback: boolean;
    handoffTimeout: number;
    requireConfirmation: boolean;
    enableCheckpoints: boolean;
    maxRetries: number;
}
export interface WorkflowHandoff {
    handoffId: string;
    fromAgent: string;
    toAgent: string;
    workflowId: string;
    taskId: string;
    handoffType: 'sequential' | 'parallel' | 'conditional' | 'emergency' | 'escalation';
    state: WorkflowState;
    context: HandoffContext;
    validation: StateValidation;
    status: 'initiated' | 'pending' | 'accepted' | 'rejected' | 'completed' | 'failed' | 'rolled_back';
    createdAt: number;
    acceptedAt?: number;
    completedAt?: number;
    checkpoints: HandoffCheckpoint[];
    rollbackPlan?: RollbackPlan;
}
export interface WorkflowState {
    stateId: string;
    version: number;
    data: any;
    metadata: StateMetadata;
    dependencies: StateDependency[];
    artifacts: StateArtifact[];
    checksum: string;
}
export interface StateMetadata {
    lastModified: number;
    modifiedBy: string;
    size: number;
    encoding: string;
    format: string;
    schema: string;
}
export interface StateDependency {
    dependencyId: string;
    type: 'data' | 'service' | 'resource' | 'agent';
    status: 'available' | 'unavailable' | 'pending';
    critical: boolean;
}
export interface StateArtifact {
    artifactId: string;
    name: string;
    type: 'file' | 'data' | 'configuration' | 'result';
    content: any;
    metadata: ArtifactMetadata;
}
export interface ArtifactMetadata {
    size: number;
    created: number;
    checksum: string;
    mimeType: string;
    encoding: string;
}
export interface HandoffContext {
    workflowPhase: string;
    previousPhases: string[];
    nextPhases: string[];
    requirements: HandoffRequirement[];
    constraints: HandoffConstraint[];
    expectations: HandoffExpectation[];
    documentation: string;
}
export interface HandoffRequirement {
    type: 'capability' | 'resource' | 'permission' | 'knowledge';
    description: string;
    mandatory: boolean;
    validationCriteria: string;
}
export interface HandoffConstraint {
    type: 'time' | 'resource' | 'quality' | 'security' | 'compliance';
    description: string;
    value: any;
    enforcement: 'strict' | 'flexible';
}
export interface HandoffExpectation {
    metric: string;
    target: any;
    tolerance: number;
    measurement: string;
    timeline: string;
}
export interface StateValidation {
    validationId: string;
    rules: ValidationRule[];
    status: 'pending' | 'passed' | 'failed' | 'warning';
    results: ValidationResult[];
    score: number;
}
export interface ValidationRule {
    ruleId: string;
    name: string;
    type: 'data_integrity' | 'completeness' | 'format' | 'business_logic' | 'security';
    condition: string;
    severity: 'error' | 'warning' | 'info';
    autoFix: boolean;
}
export interface ValidationResult {
    ruleId: string;
    status: 'passed' | 'failed' | 'warning';
    message: string;
    details: any;
    autoFixed: boolean;
}
export interface HandoffCheckpoint {
    checkpointId: string;
    name: string;
    timestamp: number;
    state: WorkflowState;
    validation: StateValidation;
    agentId: string;
    description: string;
    rollbackPoint: boolean;
}
export interface RollbackPlan {
    planId: string;
    triggers: RollbackTrigger[];
    strategy: 'checkpoint' | 'previous_agent' | 'initial_state' | 'custom';
    steps: RollbackStep[];
    dataRecovery: DataRecoveryPlan;
    notifications: NotificationPlan[];
}
export interface RollbackTrigger {
    type: 'validation_failure' | 'timeout' | 'agent_failure' | 'manual' | 'quality_threshold';
    condition: string;
    threshold: any;
    automatic: boolean;
}
export interface RollbackStep {
    stepId: string;
    order: number;
    action: 'restore_state' | 'notify_agents' | 'cleanup_resources' | 'validate_rollback';
    parameters: any;
    validation: string;
    timeout: number;
}
export interface DataRecoveryPlan {
    backupStrategy: 'checkpoint' | 'incremental' | 'full';
    retentionPeriod: number;
    compressionEnabled: boolean;
    encryptionEnabled: boolean;
}
export interface NotificationPlan {
    recipients: string[];
    channels: string[];
    template: string;
    urgency: 'low' | 'medium' | 'high' | 'critical';
}
export interface HandoffMetrics {
    totalHandoffs: number;
    successRate: number;
    averageHandoffTime: number;
    rollbackRate: number;
    validationFailureRate: number;
    agentPerformance: AgentHandoffPerformance[];
}
export interface AgentHandoffPerformance {
    agentId: string;
    handoffsReceived: number;
    handoffsGiven: number;
    successRate: number;
    averageAcceptanceTime: number;
    validationScore: number;
}
/**
 * Workflow Handoff Manager
 */
export declare class WorkflowHandoffManager extends EventEmitter {
    private config;
    private activeHandoffs;
    private handoffHistory;
    private validationRules;
    private checkpoints;
    constructor(config?: Partial<HandoffConfig>);
    /**
     * Initiate workflow handoff
     */
    initiateHandoff(fromAgent: string, toAgent: string, workflowId: string, taskId: string, state: WorkflowState, context?: Partial<HandoffContext>, handoffType?: WorkflowHandoff['handoffType']): Promise<WorkflowHandoff>;
    /**
     * Accept or reject handoff
     */
    respondToHandoff(handoffId: string, response: 'accept' | 'reject', reason?: string): Promise<void>;
    /**
     * Complete handoff with final state
     */
    completeHandoff(handoffId: string, finalState: WorkflowState, result?: any): Promise<void>;
    /**
     * Rollback handoff to previous state
     */
    rollbackHandoff(handoffId: string, reason: string, targetCheckpoint?: string): Promise<void>;
    /**
     * Create checkpoint
     */
    createCheckpoint(workflowId: string, agentId: string, state: WorkflowState, description: string, rollbackPoint?: boolean): Promise<HandoffCheckpoint>;
    /**
     * Get handoff metrics
     */
    getHandoffMetrics(): HandoffMetrics;
    /**
     * Private helper methods
     */
    private validateState;
    private executeValidationRule;
    private createPassingValidation;
    private createRollbackPlan;
    private requestHandoffAcceptance;
    private setHandoffTimeout;
    private executeRollbackStep;
    private sendRollbackNotification;
    private cloneState;
    private calculateStateChecksum;
    private moveToHistory;
    private calculateAverageHandoffTime;
    private calculateValidationFailureRate;
    private calculateAgentPerformance;
    private initializeDefaultValidationRules;
}
export default WorkflowHandoffManager;
//# sourceMappingURL=workflow-handoff-manager.d.ts.map