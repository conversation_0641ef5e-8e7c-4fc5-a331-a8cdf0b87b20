{"version": 3, "file": "unified-context-engine.js", "sourceRoot": "", "sources": ["unified-context-engine.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;AAEH,mCAAqC;AAyDrC;;GAEG;AACH,MAAa,oBAAqB,SAAQ,qBAAY;IAKpD,YAAY,SAAqC,EAAE;QACjD,KAAK,EAAE,CAAA;QAJD,aAAQ,GAAqB,IAAI,GAAG,EAAE,CAAA;QACtC,gBAAW,GAAG,KAAK,CAAA;QAIzB,IAAI,CAAC,MAAM,GAAG;YACZ,SAAS,EAAE,KAAK;YAChB,wBAAwB,EAAE,KAAK;YAC/B,SAAS,EAAE,KAAK;YAChB,WAAW,EAAE,KAAK;YAClB,sBAAsB,EAAE,KAAK;YAC7B,GAAG,MAAM;SACV,CAAA;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,IAAI,CAAC,WAAW;YAAE,OAAM;QAE5B,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAA;QAElE,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAA;QACtD,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,wBAAwB,EAAE,CAAC;YACzC,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAA;QAC1D,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAA;QACtD,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;QACvB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;IAC1B,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,KAAwB;QAC1C,MAAM,SAAS,GAAG,GAAG,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,WAAW,EAAE,CAAA;QAE3D,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE;YAC3B,KAAK;YACL,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;YACxB,IAAI,EAAE,EAAE;SACT,CAAC,CAAA;QAEF,OAAO,CAAC,GAAG,CAAC,gCAAgC,SAAS,EAAE,CAAC,CAAA;IAC1D,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,SAAiB,EAAE,WAAmB;QACrD,MAAM,SAAS,GAAG,GAAG,SAAS,IAAI,WAAW,EAAE,CAAA;QAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAE5C,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YACjC,OAAO,OAAO,CAAC,IAAI,CAAA;QACrB,CAAC;QAED,OAAO,EAAE,CAAA;IACX,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,SAAiB,EAAE,WAAmB,EAAE,IAAS;QACnE,MAAM,SAAS,GAAG,GAAG,SAAS,IAAI,WAAW,EAAE,CAAA;QAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAE5C,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,IAAI,GAAG,EAAE,GAAG,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI,EAAE,CAAA;YAC3C,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YACjC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;QACnD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,OAAY;QAC9B,mEAAmE;QACnE,MAAM,WAAW,GAAwB,EAAE,CAAA;QAE3C,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YAC1B,WAAW,CAAC,IAAI,CAAC;gBACf,IAAI,EAAE,iBAAiB;gBACvB,IAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;gBACxB,UAAU,EAAE,GAAG;aAChB,CAAC,CAAA;QACJ,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,wBAAwB,EAAE,CAAC;YACzC,WAAW,CAAC,IAAI,CAAC;gBACf,IAAI,EAAE,qBAAqB;gBAC3B,IAAI,EAAE,EAAE,eAAe,EAAE,EAAE,EAAE;gBAC7B,UAAU,EAAE,GAAG;aAChB,CAAC,CAAA;QACJ,CAAC;QAED,OAAO;YACL,QAAQ,EAAE,OAAO;YACjB,WAAW;YACX,QAAQ,EAAE;gBACR,UAAU,EAAE,IAAI,CAAC,GAAG,EAAE;gBACtB,OAAO,EAAE,kBAAkB;aAC5B;SACF,CAAA;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,KAAa;QAC9B,6BAA6B;QAC7B,MAAM,OAAO,GAAU,EAAE,CAAA;QAEzB,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;YAC3D,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;gBACxE,OAAO,CAAC,IAAI,CAAC;oBACX,SAAS;oBACT,SAAS,EAAE,GAAG;oBACd,IAAI,EAAE,OAAO,CAAC,IAAI;iBACnB,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,SAAiB;QACvC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,SAAS,EAAE,CAAC,CAAA;QACzD,OAAO,OAAO,EAAE,IAAI,IAAI,IAAI,CAAA;IAC9B,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,SAAiB,EAAE,OAAgC;QAC5E,MAAM,SAAS,GAAG,WAAW,SAAS,EAAE,CAAA;QACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAE7C,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC,IAAI,GAAG,EAAE,GAAG,QAAQ,CAAC,IAAI,EAAE,GAAG,OAAO,EAAE,CAAA;QAClD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE;gBAC3B,KAAK,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,oBAAoB,EAAE,EAAE,EAAE;gBACjF,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;gBACxB,IAAI,EAAE,OAAO;aACd,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,SAAiB;QACxC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,SAAS,EAAE,CAAC,CAAA;QAC1D,OAAO,OAAO,EAAE,IAAI,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE,CAAA;IACtE,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,SAAiB,EAAE,OAAiC;QAC9E,MAAM,SAAS,GAAG,YAAY,SAAS,EAAE,CAAA;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;QAE7C,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC,IAAI,GAAG,EAAE,GAAG,QAAQ,CAAC,IAAI,EAAE,GAAG,OAAO,EAAE,CAAA;QAClD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE;gBAC3B,KAAK,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,oBAAoB,EAAE,EAAE,EAAE;gBAClF,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;gBACxB,IAAI,EAAE,OAAO;aACd,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED,QAAQ;QACN,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;YACjC,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE;SACnC,CAAA;IACH,CAAC;IAED,KAAK,CAAC,OAAO;QACX,4CAA4C;QAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAA;QAEnC,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC;YAC3D,IAAI,OAAO,CAAC,YAAY,GAAG,MAAM,EAAE,CAAC;gBAClC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;YACjC,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAA;QACrB,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACzB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;QACxB,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAA;IACpD,CAAC;CACF;AA7LD,oDA6LC;AAED,0BAA0B;AACb,QAAA,oBAAoB,GAAG,IAAI,oBAAoB,EAAE,CAAA;AAE9D,kBAAe,oBAAoB,CAAA"}