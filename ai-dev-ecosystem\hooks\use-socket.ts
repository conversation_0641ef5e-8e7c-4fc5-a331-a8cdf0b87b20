import { useEffect, useRef } from 'react';
import { io, Socket } from 'socket.io-client';

interface AppStatusUpdate {
  appId: number;
  isRunning: boolean;
  url: string | null;
  port?: number;
  exitCode?: number;
  signal?: string;
  error?: string;
}

interface UseSocketOptions {
  onAppStatusChanged?: (update: AppStatusUpdate) => void;
  onChatUpdated?: (data: any) => void;
  onChatMessageAdded?: (data: any) => void;
  onFileChanged?: (data: any) => void;
}

export function useSocket(options: UseSocketOptions = {}) {
  const socketRef = useRef<Socket | null>(null);
  const { onAppStatusChanged, onChatUpdated, onChatMessageAdded, onFileChanged } = options;

  useEffect(() => {
    // Initialize socket connection
    const socket = io('http://localhost:3002', {
      transports: ['websocket', 'polling'],
    });

    socketRef.current = socket;

    socket.on('connect', () => {
      console.log('Connected to Dyad server via Socket.IO');
    });

    socket.on('disconnect', () => {
      console.log('Disconnected from Dyad server');
    });

    socket.on('connect_error', (error) => {
      console.error('Socket.IO connection error:', error);
    });

    // Listen for app status changes
    if (onAppStatusChanged) {
      socket.on('app-status-changed', onAppStatusChanged);
    }

    // Listen for chat updates
    if (onChatUpdated) {
      socket.on('chat-updated', onChatUpdated);
    }

    // Listen for chat message updates
    if (onChatMessageAdded) {
      socket.on('chat-message-added', onChatMessageAdded);
    }

    // Listen for file changes
    if (onFileChanged) {
      socket.on('file-changed', onFileChanged);
    }

    return () => {
      socket.disconnect();
    };
  }, [onAppStatusChanged, onChatUpdated, onChatMessageAdded, onFileChanged]);

  const emit = (event: string, data: any) => {
    if (socketRef.current) {
      socketRef.current.emit(event, data);
    }
  };

  return {
    socket: socketRef.current,
    emit,
    isConnected: socketRef.current?.connected || false,
  };
}
