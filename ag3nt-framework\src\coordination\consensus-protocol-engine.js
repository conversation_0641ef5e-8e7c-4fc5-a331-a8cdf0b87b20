"use strict";
/**
 * AG3NT Framework - Consensus Protocol Engine
 *
 * Democratic decision-making mechanisms with voting, quorum, and conflict resolution
 * for sophisticated multi-agent coordination.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConsensusProtocolEngine = void 0;
const events_1 = require("events");
/**
 * Consensus Protocol Engine
 */
class ConsensusProtocolEngine extends events_1.EventEmitter {
    constructor(config = {}) {
        super();
        this.activeProposals = new Map();
        this.proposalHistory = [];
        this.agentVotingRights = new Map();
        this.config = {
            defaultProtocol: 'majority',
            quorumThreshold: 0.6,
            votingTimeout: 300000, // 5 minutes
            enableDelegatedVoting: true,
            enableVetoRights: false,
            conflictResolutionStrategy: 'mediation',
            ...config
        };
        this.consensusMetrics = this.initializeMetrics();
    }
    /**
     * Register agent with voting rights
     */
    registerVoter(agentId, rights) {
        const votingRights = {
            agentId,
            weight: rights.weight || 1,
            expertise: rights.expertise || [],
            authority: rights.authority || 1,
            canPropose: rights.canPropose !== false,
            canVeto: rights.canVeto || false,
            canDelegate: rights.canDelegate !== false,
            trustScore: rights.trustScore || 0.8,
            ...rights
        };
        this.agentVotingRights.set(agentId, votingRights);
        this.emit('voter_registered', { agentId, rights: votingRights });
    }
    /**
     * Submit proposal for consensus
     */
    async submitProposal(proposal) {
        console.log(`📋 Submitting proposal: ${proposal.title}`);
        const fullProposal = {
            proposalId: `prop-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            proposerId: proposal.proposerId,
            title: proposal.title,
            description: proposal.description,
            type: proposal.type || 'decision',
            options: proposal.options || [],
            votingRules: proposal.votingRules || this.getDefaultVotingRules(),
            context: proposal.context || this.getDefaultContext(),
            status: 'active',
            createdAt: Date.now(),
            votingDeadline: Date.now() + this.config.votingTimeout
        };
        // Validate proposal
        this.validateProposal(fullProposal);
        // Store proposal
        this.activeProposals.set(fullProposal.proposalId, fullProposal);
        // Notify eligible voters
        await this.notifyEligibleVoters(fullProposal);
        // Start voting timeout
        this.startVotingTimeout(fullProposal.proposalId);
        this.emit('proposal_submitted', fullProposal);
        console.log(`✅ Proposal ${fullProposal.proposalId} submitted for voting`);
        return fullProposal;
    }
    /**
     * Cast vote on proposal
     */
    async castVote(vote) {
        const proposal = this.activeProposals.get(vote.proposalId);
        if (!proposal) {
            throw new Error(`Proposal ${vote.proposalId} not found`);
        }
        if (proposal.status !== 'active') {
            throw new Error(`Proposal ${vote.proposalId} is not active`);
        }
        if (Date.now() > proposal.votingDeadline) {
            throw new Error(`Voting deadline has passed for proposal ${vote.proposalId}`);
        }
        // Validate voting rights
        const votingRights = this.agentVotingRights.get(vote.agentId);
        if (!votingRights) {
            throw new Error(`Agent ${vote.agentId} does not have voting rights`);
        }
        const fullVote = {
            voteId: `vote-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            agentId: vote.agentId,
            proposalId: vote.proposalId,
            optionId: vote.optionId,
            voteType: vote.voteType || 'support',
            weight: this.calculateVoteWeight(votingRights, proposal),
            confidence: vote.confidence || 0.8,
            reasoning: vote.reasoning || '',
            conditions: vote.conditions,
            timestamp: Date.now(),
            delegatedFrom: vote.delegatedFrom
        };
        // Store vote
        if (!proposal.votes) {
            proposal.votes = [];
        }
        proposal.votes.push(fullVote);
        // Check if consensus reached
        if (await this.checkConsensusReached(proposal)) {
            await this.finalizeConsensus(proposal);
        }
        this.emit('vote_cast', fullVote);
        console.log(`🗳️ Vote cast by ${vote.agentId} on proposal ${vote.proposalId}`);
        return fullVote;
    }
    /**
     * Delegate voting rights
     */
    async delegateVote(fromAgent, toAgent, proposalId, conditions) {
        if (!this.config.enableDelegatedVoting) {
            throw new Error('Delegated voting is not enabled');
        }
        const fromRights = this.agentVotingRights.get(fromAgent);
        const toRights = this.agentVotingRights.get(toAgent);
        if (!fromRights || !toRights) {
            throw new Error('Invalid delegation: agent voting rights not found');
        }
        if (!fromRights.canDelegate) {
            throw new Error(`Agent ${fromAgent} cannot delegate votes`);
        }
        // Create delegation record
        const delegation = {
            fromAgent,
            toAgent,
            proposalId,
            conditions,
            timestamp: Date.now()
        };
        this.emit('vote_delegated', delegation);
        console.log(`🤝 Vote delegated from ${fromAgent} to ${toAgent}`);
    }
    /**
     * Exercise veto right
     */
    async exerciseVeto(agentId, proposalId, justification) {
        if (!this.config.enableVetoRights) {
            throw new Error('Veto rights are not enabled');
        }
        const proposal = this.activeProposals.get(proposalId);
        if (!proposal) {
            throw new Error(`Proposal ${proposalId} not found`);
        }
        const votingRights = this.agentVotingRights.get(agentId);
        if (!votingRights || !votingRights.canVeto) {
            throw new Error(`Agent ${agentId} does not have veto rights`);
        }
        // Check if veto is valid for this proposal
        const vetoRight = proposal.votingRules.vetoRights.find(v => v.agentId === agentId);
        if (!vetoRight) {
            throw new Error(`Agent ${agentId} does not have veto rights for this proposal`);
        }
        // Exercise veto
        const veto = {
            voteId: `veto-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            agentId,
            proposalId,
            optionId: 'veto',
            voteType: 'veto',
            weight: 1,
            confidence: 1,
            reasoning: justification,
            timestamp: Date.now()
        };
        if (!proposal.votes) {
            proposal.votes = [];
        }
        proposal.votes.push(veto);
        // Veto immediately stops the proposal
        proposal.status = 'failed';
        proposal.result = {
            decision: 'vetoed',
            winningOption: 'none',
            votingSummary: this.calculateVotingSummary(proposal),
            consensusLevel: 0,
            dissent: { dissentLevel: 1, majorConcerns: [justification], dissentingAgents: [agentId], compromisePossible: false, recommendedActions: [] },
            implementation: { phases: [], timeline: '', responsibilities: [], successMetrics: [], rollbackPlan: { triggers: [], steps: [], timeframe: 0, approvalRequired: false } },
            appeals: []
        };
        this.moveToHistory(proposal);
        this.emit('proposal_vetoed', { proposal, veto });
        console.log(`❌ Proposal ${proposalId} vetoed by ${agentId}`);
    }
    /**
     * Get consensus metrics
     */
    getConsensusMetrics() {
        return { ...this.consensusMetrics };
    }
    /**
     * Private helper methods
     */
    validateProposal(proposal) {
        if (!proposal.title || !proposal.description) {
            throw new Error('Proposal must have title and description');
        }
        if (proposal.options.length < 2) {
            throw new Error('Proposal must have at least 2 options');
        }
        const proposerRights = this.agentVotingRights.get(proposal.proposerId);
        if (!proposerRights || !proposerRights.canPropose) {
            throw new Error(`Agent ${proposal.proposerId} cannot submit proposals`);
        }
    }
    getDefaultVotingRules() {
        return {
            protocol: this.config.defaultProtocol,
            quorum: this.config.quorumThreshold,
            allowAbstention: true,
            allowDelegation: this.config.enableDelegatedVoting,
            vetoRights: [],
            tieBreakingMethod: 'random'
        };
    }
    getDefaultContext() {
        return {
            urgency: 'medium',
            stakeholders: [],
            affectedSystems: [],
            prerequisites: [],
            constraints: {},
            relatedProposals: []
        };
    }
    async notifyEligibleVoters(proposal) {
        for (const [agentId] of this.agentVotingRights.entries()) {
            this.emit('voting_notification', { agentId, proposal });
        }
    }
    startVotingTimeout(proposalId) {
        setTimeout(async () => {
            const proposal = this.activeProposals.get(proposalId);
            if (proposal && proposal.status === 'active') {
                await this.finalizeConsensus(proposal);
            }
        }, this.config.votingTimeout);
    }
    calculateVoteWeight(votingRights, proposal) {
        if (proposal.votingRules.protocol === 'weighted') {
            const criteria = proposal.votingRules.weightingCriteria;
            return (votingRights.authority * criteria.authorityWeight +
                votingRights.trustScore * criteria.trustWeight +
                votingRights.weight * 0.5);
        }
        return votingRights.weight;
    }
    async checkConsensusReached(proposal) {
        if (!proposal.votes)
            return false;
        const summary = this.calculateVotingSummary(proposal);
        // Check quorum
        if (summary.participationRate < proposal.votingRules.quorum) {
            return false;
        }
        // Check if any option has enough support
        const winningOption = summary.optionResults.reduce((prev, current) => current.percentage > prev.percentage ? current : prev);
        switch (proposal.votingRules.protocol) {
            case 'majority':
                return winningOption.percentage > 0.5;
            case 'supermajority':
                return winningOption.percentage > 0.67;
            case 'unanimous':
                return winningOption.percentage === 1.0;
            default:
                return winningOption.percentage > 0.5;
        }
    }
    calculateVotingSummary(proposal) {
        const votes = proposal.votes || [];
        const totalEligible = this.agentVotingRights.size;
        const optionCounts = new Map();
        // Initialize option counts
        for (const option of proposal.options) {
            optionCounts.set(option.optionId, { votes: 0, weightedVotes: 0, support: [] });
        }
        // Count votes
        let totalVotes = 0;
        let delegatedVotes = 0;
        let vetoesReceived = 0;
        for (const vote of votes) {
            if (vote.voteType === 'veto') {
                vetoesReceived++;
                continue;
            }
            if (vote.voteType === 'abstain')
                continue;
            totalVotes++;
            if (vote.delegatedFrom)
                delegatedVotes++;
            const optionCount = optionCounts.get(vote.optionId);
            if (optionCount) {
                optionCount.votes++;
                optionCount.weightedVotes += vote.weight;
                optionCount.support.push(vote);
            }
        }
        // Calculate results
        const optionResults = [];
        for (const [optionId, count] of optionCounts.entries()) {
            optionResults.push({
                optionId,
                votes: count.votes,
                weightedVotes: count.weightedVotes,
                percentage: totalVotes > 0 ? count.votes / totalVotes : 0,
                support: count.support
            });
        }
        return {
            totalEligibleVoters: totalEligible,
            totalVotes,
            participationRate: totalVotes / totalEligible,
            optionResults,
            vetoesReceived,
            delegatedVotes
        };
    }
    async finalizeConsensus(proposal) {
        const summary = this.calculateVotingSummary(proposal);
        // Determine winning option
        const winningOption = summary.optionResults.reduce((prev, current) => current.percentage > prev.percentage ? current : prev);
        // Calculate consensus level
        const consensusLevel = winningOption.percentage;
        // Analyze dissent
        const dissent = this.analyzeDissent(proposal, winningOption);
        // Create implementation plan
        const implementation = this.createImplementationPlan(proposal, winningOption);
        proposal.result = {
            decision: winningOption.optionId,
            winningOption: winningOption.optionId,
            votingSummary: summary,
            consensusLevel,
            dissent,
            implementation,
            appeals: []
        };
        proposal.status = 'completed';
        this.moveToHistory(proposal);
        this.emit('consensus_reached', proposal);
        console.log(`✅ Consensus reached on proposal ${proposal.proposalId}: ${winningOption.optionId}`);
    }
    analyzeDissent(proposal, winningOption) {
        const votes = proposal.votes || [];
        const dissentingVotes = votes.filter(v => v.optionId !== winningOption.optionId && v.voteType !== 'abstain');
        return {
            dissentLevel: dissentingVotes.length / Math.max(votes.length, 1),
            majorConcerns: dissentingVotes.map(v => v.reasoning).filter(r => r),
            dissentingAgents: dissentingVotes.map(v => v.agentId),
            compromisePossible: dissentingVotes.length < votes.length * 0.3,
            recommendedActions: dissentingVotes.length > 0 ? ['Address concerns', 'Monitor implementation'] : []
        };
    }
    createImplementationPlan(proposal, winningOption) {
        return {
            phases: [
                {
                    phaseId: 'implementation',
                    name: 'Implementation Phase',
                    description: 'Execute the decided option',
                    startConditions: ['consensus_reached'],
                    deliverables: ['option_implemented'],
                    duration: 3600000, // 1 hour
                    dependencies: []
                }
            ],
            timeline: '1 hour',
            responsibilities: winningOption.support.map(vote => ({
                agentId: vote.agentId,
                role: 'implementer',
                tasks: ['execute_decision'],
                authority: ['implementation'],
                accountability: ['results']
            })),
            successMetrics: [
                {
                    name: 'implementation_success',
                    target: 1,
                    measurement: 'boolean',
                    timeline: '1 hour'
                }
            ],
            rollbackPlan: {
                triggers: ['implementation_failure'],
                steps: ['revert_changes', 'notify_stakeholders'],
                timeframe: 1800000, // 30 minutes
                approvalRequired: true
            }
        };
    }
    moveToHistory(proposal) {
        this.proposalHistory.push(proposal);
        this.activeProposals.delete(proposal.proposalId);
        this.updateMetrics(proposal);
    }
    updateMetrics(proposal) {
        this.consensusMetrics.totalProposals++;
        if (proposal.status === 'completed') {
            this.consensusMetrics.successRate =
                (this.consensusMetrics.successRate * (this.consensusMetrics.totalProposals - 1) + 1) /
                    this.consensusMetrics.totalProposals;
        }
        if (proposal.result) {
            this.consensusMetrics.participationRate =
                (this.consensusMetrics.participationRate + proposal.result.votingSummary.participationRate) / 2;
        }
    }
    initializeMetrics() {
        return {
            totalProposals: 0,
            successRate: 0,
            averageConsensusTime: 0,
            participationRate: 0,
            conflictRate: 0,
            appealRate: 0,
            topContributors: []
        };
    }
}
exports.ConsensusProtocolEngine = ConsensusProtocolEngine;
exports.default = ConsensusProtocolEngine;
//# sourceMappingURL=consensus-protocol-engine.js.map