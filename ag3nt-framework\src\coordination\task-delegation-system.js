"use strict";
/**
 * AG3NT Framework - Advanced Task Delegation System
 *
 * Sophisticated delegation patterns with authority levels, capability matching,
 * and accountability tracking for multi-agent coordination.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskDelegationSystem = void 0;
const events_1 = require("events");
/**
 * Advanced Task Delegation System
 */
class TaskDelegationSystem extends events_1.EventEmitter {
    constructor(config = {}) {
        super();
        this.agentAuthorities = new Map();
        this.activeDelegations = new Map();
        this.delegationHistory = [];
        this.delegationStrategies = new Map();
        this.config = {
            enableHierarchicalDelegation: true,
            enablePeerDelegation: true,
            maxDelegationDepth: 3,
            delegationTimeout: 300000, // 5 minutes
            requireConfirmation: true,
            enableRollback: true,
            ...config
        };
        this.initializeDefaultStrategies();
    }
    /**
     * Register agent with authority and capabilities
     */
    registerAgent(agentId, authority) {
        const fullAuthority = {
            agentId,
            authorityLevel: authority.authorityLevel || 5,
            capabilities: authority.capabilities || [],
            delegationRights: authority.delegationRights || [],
            currentLoad: 0,
            maxLoad: authority.maxLoad || 10,
            trustScore: authority.trustScore || 0.8,
            specializations: authority.specializations || []
        };
        this.agentAuthorities.set(agentId, fullAuthority);
        this.emit('agent_registered', { agentId, authority: fullAuthority });
    }
    /**
     * Delegate task to another agent
     */
    async delegateTask(fromAgent, task, delegationType = 'hierarchical', targetAgent) {
        console.log(`🤝 Delegating task ${task.taskId} from ${fromAgent}`);
        // Validate delegation authority
        const fromAuthority = this.agentAuthorities.get(fromAgent);
        if (!fromAuthority) {
            throw new Error(`Agent ${fromAgent} not registered`);
        }
        // Select target agent if not specified
        const toAgent = targetAgent || await this.selectOptimalAgent(task, fromAgent, delegationType);
        if (!toAgent) {
            throw new Error('No suitable agent found for delegation');
        }
        // Create delegation
        const delegation = {
            delegationId: `del-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            fromAgent,
            toAgent,
            task,
            delegationType,
            authority: this.calculateDelegationAuthority(fromAuthority, delegationType),
            status: 'pending',
            createdAt: Date.now(),
            rollbackPlan: this.config.enableRollback ? this.createRollbackPlan(task) : undefined
        };
        // Store delegation
        this.activeDelegations.set(delegation.delegationId, delegation);
        // Request confirmation if required
        if (this.config.requireConfirmation) {
            await this.requestDelegationConfirmation(delegation);
        }
        else {
            delegation.status = 'accepted';
            delegation.acceptedAt = Date.now();
        }
        // Update agent loads
        this.updateAgentLoad(fromAgent, -1);
        this.updateAgentLoad(toAgent, 1);
        this.emit('task_delegated', delegation);
        console.log(`✅ Task delegated to ${toAgent}`);
        return delegation;
    }
    /**
     * Accept or reject delegation
     */
    async respondToDelegation(delegationId, response, reason) {
        const delegation = this.activeDelegations.get(delegationId);
        if (!delegation) {
            throw new Error(`Delegation ${delegationId} not found`);
        }
        if (response === 'accept') {
            delegation.status = 'accepted';
            delegation.acceptedAt = Date.now();
            this.emit('delegation_accepted', delegation);
        }
        else {
            delegation.status = 'rejected';
            this.updateAgentLoad(delegation.toAgent, -1);
            this.updateAgentLoad(delegation.fromAgent, 1);
            this.emit('delegation_rejected', { delegation, reason });
        }
    }
    /**
     * Complete delegation with results
     */
    async completeDelegation(delegationId, result) {
        const delegation = this.activeDelegations.get(delegationId);
        if (!delegation) {
            throw new Error(`Delegation ${delegationId} not found`);
        }
        delegation.status = result.success ? 'completed' : 'failed';
        delegation.completedAt = Date.now();
        delegation.result = result;
        // Update agent capabilities based on performance
        await this.updateAgentCapabilities(delegation.toAgent, delegation.task, result);
        // Update trust scores
        this.updateTrustScore(delegation.toAgent, result.success, result.metrics.qualityScore);
        // Move to history
        this.delegationHistory.push(delegation);
        this.activeDelegations.delete(delegationId);
        // Update loads
        this.updateAgentLoad(delegation.toAgent, -1);
        this.emit('delegation_completed', delegation);
    }
    /**
     * Rollback delegation
     */
    async rollbackDelegation(delegationId, reason) {
        const delegation = this.activeDelegations.get(delegationId);
        if (!delegation || !delegation.rollbackPlan) {
            throw new Error(`Cannot rollback delegation ${delegationId}`);
        }
        console.log(`🔄 Rolling back delegation ${delegationId}: ${reason}`);
        // Execute rollback steps
        for (const step of delegation.rollbackPlan.steps) {
            await this.executeRollbackStep(step, delegation);
        }
        delegation.status = 'rolled_back';
        this.updateAgentLoad(delegation.toAgent, -1);
        // Try fallback agent if available
        if (delegation.rollbackPlan.fallbackAgent) {
            await this.delegateTask(delegation.fromAgent, delegation.task, 'emergency', delegation.rollbackPlan.fallbackAgent);
        }
        this.emit('delegation_rolled_back', { delegation, reason });
    }
    /**
     * Get delegation analytics
     */
    getDelegationAnalytics() {
        const allDelegations = [...this.delegationHistory, ...Array.from(this.activeDelegations.values())];
        const completed = allDelegations.filter(d => d.status === 'completed');
        return {
            totalDelegations: allDelegations.length,
            successRate: completed.length / Math.max(allDelegations.length, 1),
            averageExecutionTime: this.calculateAverageExecutionTime(completed),
            topPerformers: this.calculateTopPerformers(),
            bottlenecks: this.identifyBottlenecks(),
            recommendations: this.generateOptimizationRecommendations()
        };
    }
    /**
     * Private helper methods
     */
    async selectOptimalAgent(task, fromAgent, delegationType) {
        const strategy = this.delegationStrategies.get('capability_based');
        const candidates = [];
        for (const [agentId, authority] of this.agentAuthorities.entries()) {
            if (agentId === fromAgent)
                continue;
            if (authority.currentLoad >= authority.maxLoad)
                continue;
            const score = this.calculateAgentScore(authority, task, strategy.selectionCriteria);
            if (score > 0) {
                candidates.push({ agentId, score });
            }
        }
        // Sort by score and return best candidate
        candidates.sort((a, b) => b.score - a.score);
        return candidates.length > 0 ? candidates[0].agentId : null;
    }
    calculateAgentScore(authority, task, criteria) {
        let score = 0;
        // Capability matching
        const capabilityScore = this.calculateCapabilityScore(authority.capabilities, task.requirements);
        score += capabilityScore * criteria.capabilityWeight;
        // Load factor
        const loadScore = 1 - (authority.currentLoad / authority.maxLoad);
        score += loadScore * criteria.loadWeight;
        // Trust score
        score += authority.trustScore * criteria.trustWeight;
        // Experience factor
        const experienceScore = this.calculateExperienceScore(authority.capabilities, task.type);
        score += experienceScore * criteria.experienceWeight;
        return score;
    }
    calculateCapabilityScore(capabilities, requirements) {
        if (requirements.length === 0)
            return 1;
        let totalScore = 0;
        let totalWeight = 0;
        for (const req of requirements) {
            const capability = capabilities.find(c => c.name === req.capability);
            if (capability) {
                const score = Math.min(capability.proficiency / req.minimumProficiency, 1);
                totalScore += score * req.weight;
            }
            else if (req.required) {
                return 0; // Missing required capability
            }
            totalWeight += req.weight;
        }
        return totalWeight > 0 ? totalScore / totalWeight : 0;
    }
    calculateExperienceScore(capabilities, taskType) {
        const relevantCapabilities = capabilities.filter(c => taskType.toLowerCase().includes(c.name.toLowerCase()));
        if (relevantCapabilities.length === 0)
            return 0.5;
        return relevantCapabilities.reduce((sum, cap) => sum + cap.experience, 0) / relevantCapabilities.length;
    }
    calculateDelegationAuthority(fromAuthority, delegationType) {
        return {
            level: Math.max(1, fromAuthority.authorityLevel - 1),
            scope: ['task_execution'],
            limitations: ['no_sub_delegation'],
            canSubDelegate: fromAuthority.authorityLevel > 7,
            reportingRequired: true
        };
    }
    createRollbackPlan(task) {
        return {
            triggers: [
                { condition: 'timeout', threshold: this.config.delegationTimeout, timeWindow: 60000 },
                { condition: 'quality_below_threshold', threshold: 0.5, timeWindow: 0 }
            ],
            steps: [
                { order: 1, action: 'stop_execution', parameters: {}, validation: 'execution_stopped' },
                { order: 2, action: 'cleanup_resources', parameters: {}, validation: 'resources_cleaned' }
            ],
            dataRecovery: true,
            notificationRequired: true
        };
    }
    async requestDelegationConfirmation(delegation) {
        // In a real implementation, this would send a confirmation request
        // For now, we'll auto-accept after a short delay
        setTimeout(() => {
            if (delegation.status === 'pending') {
                delegation.status = 'accepted';
                delegation.acceptedAt = Date.now();
                this.emit('delegation_accepted', delegation);
            }
        }, 1000);
    }
    updateAgentLoad(agentId, delta) {
        const authority = this.agentAuthorities.get(agentId);
        if (authority) {
            authority.currentLoad = Math.max(0, authority.currentLoad + delta);
        }
    }
    async updateAgentCapabilities(agentId, task, result) {
        const authority = this.agentAuthorities.get(agentId);
        if (!authority)
            return;
        // Update relevant capabilities based on task performance
        for (const req of task.requirements) {
            const capability = authority.capabilities.find(c => c.name === req.capability);
            if (capability) {
                // Update proficiency based on success
                if (result.success) {
                    capability.proficiency = Math.min(1, capability.proficiency + 0.01);
                    capability.successRate = (capability.successRate * capability.experience + 1) / (capability.experience + 1);
                }
                else {
                    capability.successRate = (capability.successRate * capability.experience) / (capability.experience + 1);
                }
                capability.experience += 1;
                capability.lastUsed = Date.now();
                capability.averageTime = (capability.averageTime + result.metrics.executionTime) / 2;
            }
        }
    }
    updateTrustScore(agentId, success, qualityScore) {
        const authority = this.agentAuthorities.get(agentId);
        if (!authority)
            return;
        const adjustment = success ? 0.01 : -0.02;
        authority.trustScore = Math.max(0, Math.min(1, authority.trustScore + adjustment));
        if (success) {
            authority.trustScore = (authority.trustScore + qualityScore) / 2;
        }
    }
    async executeRollbackStep(step, delegation) {
        // Implementation would execute the specific rollback action
        console.log(`Executing rollback step ${step.order}: ${step.action}`);
    }
    calculateAverageExecutionTime(delegations) {
        const completed = delegations.filter(d => d.completedAt && d.acceptedAt);
        if (completed.length === 0)
            return 0;
        const totalTime = completed.reduce((sum, d) => sum + (d.completedAt - d.acceptedAt), 0);
        return totalTime / completed.length;
    }
    calculateTopPerformers() {
        const performances = new Map();
        for (const delegation of this.delegationHistory) {
            if (delegation.status !== 'completed' || !delegation.result)
                continue;
            const agentId = delegation.toAgent;
            const existing = performances.get(agentId) || {
                agentId,
                delegationsReceived: 0,
                successRate: 0,
                averageTime: 0,
                qualityScore: 0,
                trustScore: 0
            };
            existing.delegationsReceived += 1;
            existing.successRate = (existing.successRate + (delegation.result.success ? 1 : 0)) / existing.delegationsReceived;
            existing.averageTime = (existing.averageTime + delegation.result.metrics.executionTime) / existing.delegationsReceived;
            existing.qualityScore = (existing.qualityScore + delegation.result.metrics.qualityScore) / existing.delegationsReceived;
            const authority = this.agentAuthorities.get(agentId);
            if (authority) {
                existing.trustScore = authority.trustScore;
            }
            performances.set(agentId, existing);
        }
        return Array.from(performances.values())
            .sort((a, b) => (b.successRate * b.qualityScore) - (a.successRate * a.qualityScore))
            .slice(0, 10);
    }
    identifyBottlenecks() {
        const bottlenecks = [];
        // Check for overloaded agents
        for (const [agentId, authority] of this.agentAuthorities.entries()) {
            if (authority.currentLoad / authority.maxLoad > 0.8) {
                bottlenecks.push({
                    type: 'overload',
                    description: `Agent ${agentId} is operating at ${Math.round(authority.currentLoad / authority.maxLoad * 100)}% capacity`,
                    impact: authority.currentLoad / authority.maxLoad,
                    suggestedSolution: 'Increase agent capacity or redistribute load'
                });
            }
        }
        return bottlenecks;
    }
    generateOptimizationRecommendations() {
        const recommendations = [];
        // Analyze delegation patterns for recommendations
        const failedDelegations = this.delegationHistory.filter(d => d.status === 'failed');
        if (failedDelegations.length > this.delegationHistory.length * 0.1) {
            recommendations.push({
                type: 'training',
                description: 'High failure rate detected - consider additional agent training',
                expectedBenefit: 'Reduced failure rate and improved task completion',
                implementationEffort: 'medium'
            });
        }
        return recommendations;
    }
    initializeDefaultStrategies() {
        this.delegationStrategies.set('capability_based', {
            name: 'Capability-Based Selection',
            type: 'capability_based',
            selectionCriteria: {
                capabilityWeight: 0.4,
                loadWeight: 0.2,
                trustWeight: 0.2,
                experienceWeight: 0.15,
                availabilityWeight: 0.05,
                costWeight: 0
            },
            optimizationGoals: ['quality', 'capability_match']
        });
        this.delegationStrategies.set('load_based', {
            name: 'Load-Based Selection',
            type: 'load_based',
            selectionCriteria: {
                capabilityWeight: 0.2,
                loadWeight: 0.5,
                trustWeight: 0.1,
                experienceWeight: 0.1,
                availabilityWeight: 0.1,
                costWeight: 0
            },
            optimizationGoals: ['load_balancing', 'throughput']
        });
    }
}
exports.TaskDelegationSystem = TaskDelegationSystem;
exports.default = TaskDelegationSystem;
//# sourceMappingURL=task-delegation-system.js.map