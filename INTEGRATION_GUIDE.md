# Dyad Web Integration Guide

This guide explains how to integrate Dyad's backend functionality with your existing web frontend at ai-dev-ecosystem.

## ✅ What's Been Completed

### 🔧 **Backend Web Server**
- ✅ Express.js HTTP API server running on `http://localhost:3002`
- ✅ All major Dyad functionality exposed via REST endpoints
- ✅ Socket.IO support for real-time features
- ✅ CORS configured for web frontend integration
- ✅ Database integration working
- ✅ Environment variables loaded (including OpenRouter API key)

### 📡 **API Endpoints Available**
- ✅ **Apps**: Create, read, update, delete, start/stop apps
- ✅ **Chats**: Create chats, send messages, stream responses
- ✅ **Settings**: User preferences, API keys, themes
- ✅ **Language Models**: All OpenRouter models + providers
- ✅ **Node.js**: Environment status and package management
- ✅ **File Operations**: Read/write app files

### 🔌 **Client Library**
- ✅ TypeScript API client (`src/web-client/api-client.ts`)
- ✅ Type definitions for all data models
- ✅ Streaming chat support
- ✅ Error handling and authentication

## 🚀 **Integration Steps**

### 1. **Start the Dyad Web Server**

```bash
# In the dyad directory
npm run web-server
```

The server will run on `http://localhost:3002` with:
- Health check: `http://localhost:3002/health`
- API endpoints: `http://localhost:3002/api/*`

### 2. **Copy the API Client to Your Web Frontend**

Copy these files to your ai-dev-ecosystem project:

```bash
# Copy the API client
cp src/web-client/api-client.ts /path/to/ai-dev-ecosystem/src/lib/dyad-client.ts
```

### 3. **Install Dependencies in Your Web Frontend**

Your web frontend will need these dependencies if not already installed:

```bash
npm install # No additional dependencies needed - uses native fetch
```

### 4. **Initialize the Dyad Client**

In your web frontend, create a Dyad client instance:

```typescript
// src/lib/dyad.ts
import { DyadApiClient } from './dyad-client';

export const dyad = new DyadApiClient('http://localhost:3002');

// Test the connection
export async function testDyadConnection() {
  try {
    const health = await dyad.healthCheck();
    console.log('Dyad connection successful:', health);
    return true;
  } catch (error) {
    console.error('Dyad connection failed:', error);
    return false;
  }
}
```

### 5. **Use Dyad Functionality in Your Components**

```typescript
// Example: List and create apps
import { dyad } from '@/lib/dyad';

// List all apps
const apps = await dyad.listApps();

// Create a new app
const newApp = await dyad.createApp({ 
  name: 'my-web-app',
  template: 'react' 
});

// Get chats for an app
const chats = await dyad.listChats(newApp.id);

// Stream a chat response
await dyad.streamChat(chatId, {
  message: 'Create a login page',
  model: { name: 'gpt-4', provider: 'openai' },
  appId: newApp.id
}, (chunk) => {
  // Handle streaming response
  if (chunk.done) {
    console.log('Response complete');
  } else {
    console.log('Received:', chunk.content);
  }
});
```

## 🔧 **Available API Endpoints**

### **Apps Management**
```typescript
// List all apps
GET /api/apps

// Create new app
POST /api/apps
{ "name": "my-app", "template": "react" }

// Get specific app
GET /api/apps/:id

// Update app
PUT /api/apps/:id
{ "name": "updated-name" }

// Delete app
DELETE /api/apps/:id

// Read app file
GET /api/apps/:id/files?path=src/App.tsx

// Write app file
PUT /api/apps/:id/files?path=src/App.tsx
{ "content": "file content here" }
```

### **Chat Functionality**
```typescript
// List chats for an app
GET /api/chats?appId=1

// Create new chat
POST /api/chats
{ "appId": 1, "title": "New Chat" }

// Get chat with messages
GET /api/chats/:id

// Add message to chat
POST /api/chats/:id/messages
{ "content": "Hello", "role": "user" }

// Stream chat response (Server-Sent Events)
POST /api/chats/:id/stream
{ 
  "message": "Create a component",
  "model": { "name": "gpt-4", "provider": "openai" },
  "appId": 1
}
```

### **Settings & Configuration**
```typescript
// Get user settings
GET /api/settings

// Update settings
PUT /api/settings
{ "theme": "dark", "selectedModel": {...} }

// Get environment variables (masked)
GET /api/settings/env-vars

// Get language models
GET /api/language-models/all
GET /api/language-models/openrouter
```

## 🔄 **Real-time Features**

The server supports Socket.IO for real-time updates:

```typescript
import { io } from 'socket.io-client';

const socket = io('http://localhost:3002');

socket.on('connect', () => {
  console.log('Connected to Dyad server');
});

// Listen for real-time updates
socket.on('app-updated', (data) => {
  console.log('App updated:', data);
});

socket.on('chat-message', (data) => {
  console.log('New chat message:', data);
});
```

## 🛡️ **Security & CORS**

The server is configured to accept requests from your web frontend. Update the CORS settings if needed:

```env
# .env file in dyad directory
WEB_FRONTEND_URL=http://localhost:3001
WEB_SERVER_PORT=3002
```

## 🔧 **Environment Variables**

Make sure these are set in the dyad `.env` file:

```env
# API Keys (already configured)
OPENROUTER_API_KEY=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
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key

# Web server config
WEB_SERVER_PORT=3002
WEB_FRONTEND_URL=http://localhost:3001
```

## 🚀 **Next Steps**

1. **Test the Integration**: Start both servers and test the API calls
2. **Implement UI Components**: Create React components that use the Dyad API
3. **Add Error Handling**: Implement proper error boundaries and loading states
4. **Optimize Performance**: Add caching and request debouncing as needed
5. **Add Authentication**: Implement user authentication if required

## 📝 **Example Integration**

Here's a complete example of integrating Dyad into a React component:

```typescript
// components/DyadAppManager.tsx
import { useState, useEffect } from 'react';
import { dyad } from '@/lib/dyad';

export function DyadAppManager() {
  const [apps, setApps] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadApps();
  }, []);

  const loadApps = async () => {
    try {
      const appList = await dyad.listApps();
      setApps(appList);
    } catch (error) {
      console.error('Failed to load apps:', error);
    } finally {
      setLoading(false);
    }
  };

  const createApp = async (name: string) => {
    try {
      const newApp = await dyad.createApp({ name });
      setApps(prev => [newApp, ...prev]);
    } catch (error) {
      console.error('Failed to create app:', error);
    }
  };

  if (loading) return <div>Loading apps...</div>;

  return (
    <div>
      <h2>Dyad Apps</h2>
      <button onClick={() => createApp('New App')}>
        Create App
      </button>
      <ul>
        {apps.map(app => (
          <li key={app.id}>{app.name}</li>
        ))}
      </ul>
    </div>
  );
}
```

## ✅ **Status**

- ✅ **Backend Server**: Running and functional
- ✅ **API Endpoints**: All major functionality exposed
- ✅ **Client Library**: Ready for integration
- ✅ **Documentation**: Complete integration guide
- 🔄 **Next**: Integrate with your web frontend UI

The Dyad backend is now ready to be connected to your ai-dev-ecosystem web frontend!
